import{r as u,S as g,j as r}from"./index-BnbL29JP.js";import{u as i}from"./Layout-pFzaaQDc.js";const m=({handleSelectLanguage:o,register:t})=>{const{languages:s,langError:n,langLoading:d}=i(),{lang:a}=u.useContext(g);return r.jsx(r.Fragment,{children:r.jsxs("select",{name:"language",...t("language",{required:"language is required!"}),onChange:e=>o(e.target.value),className:"block w-20 h-10 border border-emerald-400 bg-gray-100 dark:bg-gray-700 px-2 py-1 text-sm dark:text-gray-300 focus:outline-none rounded-md form-select focus:bg-white dark:focus:bg-gray-700",children:[r.jsx("option",{value:a,defaultChecked:!0,hidden:!0,children:a}),!n&&!d&&s?.map(e=>r.jsxs("option",{value:e.iso_code,children:[e.iso_code," "]},e._id))]})})};export{m as S};
