import{r as b,S as D,j as e,t as n,h as s}from"./index-BnbL29JP.js";import{l as _,e as U,f as R,g as L}from"./Layout-pFzaaQDc.js";import{C as E,S as O,B as q}from"./BulkActionDrawer-0F-ByaaS.js";import{T as S,D as H,u as B,M as F}from"./DrawerButton-BQHT-xfW.js";import{u as V,E as T}from"./index.esm-ClJnGQn6.js";import{I as k}from"./InputArea-B_vEs1uv.js";import{L as N}from"./LabelArea-DQFDcuEN.js";import{S as z}from"./SwitchToggle-CGiqq8S_.js";import{C}from"./CurrencyServices-3wuDp8cZ.js";import{a as v,n as A}from"./toast-DZMsp61l.js";import{D as I,E as G}from"./EditDeleteButton-BfkOlssy.js";import{P as J}from"./PageTitle-FuOKSvYQ.js";import{u as K}from"./useAsync-DWXVKl2F.js";import{u as Q}from"./useFilter-7smJgyBE.js";import{T as W}from"./TableLoading-BGBA3G2p.js";import{N as X}from"./NotFound-H45Wi1lW.js";import{A as Y}from"./AnimatedContent-0V4vlNfe.js";import"./iconBase-CKOh_aia.js";import"./index.prod-BbOPZ2BB.js";import"./ProductServices-CXwJ-2YB.js";import"./index-C148XJoK.js";import"./CouponServices-BN-cEYCp.js";import"./ParentCategory-BlfDuDd5.js";import"./useDisableForDemo-Bu4HEiKz.js";import"./SelectLanguageTwo-KDdaD-gj.js";import"./spinner-CkndCogW.js";import"./AdminServices-DjQuFfvs.js";import"./Tooltip-CCK2Gwc2.js";const Z=l=>{const[t,d]=b.useState(!0),[u,a]=b.useState(!1),{isDrawerOpen:m,closeDrawer:i,setIsUpdate:h}=b.useContext(D),{handleSubmit:r,register:j,setValue:x,clearErrors:g,formState:{errors:w}}=V(),f=async({symbol:c,name:p})=>{try{a(!0);const o={name:p,symbol:c,status:t?"show":"hide"};if(l){const y=await C.updateCurrency(l,o);h(!0),a(!1),A(y.message),i()}else{const y=await C.addCurrency(o);h(!0),a(!1),A(y.message),i()}}catch(o){a(!1),v(o?.response?.data?.message||o?.message),i()}};return b.useEffect(()=>{if(!m){x("name"),x("symbol"),d(!0),g("symbol"),g("name");return}l&&(async()=>{try{const c=await C.getCurrencyById(l);c&&(x("name",c.name),x("symbol",c.symbol),d(c.status==="show"))}catch(c){v(c?.response?.data?.message||c?.message)}})()},[g,l,m,x]),{errors:w,onSubmit:f,register:j,status:t,setStatus:d,isSubmitting:u,handleSubmit:r}},P=({id:l})=>{const{errors:t,onSubmit:d,register:u,status:a,setStatus:m,isSubmitting:i,handleSubmit:h}=Z(l);return e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"w-full relative p-6 border-b border-gray-100 bg-gray-50 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-300",children:l?e.jsx(S,{title:n("UpdateCurrency"),description:n("UpdateCurrencyText")}):e.jsx(S,{title:n("AddCurrency"),description:n("AddCurrencyText")})}),e.jsx(_.Scrollbars,{className:"w-full md:w-7/12 lg:w-8/12 xl:w-8/12 relative dark:bg-gray-700 dark:text-gray-200",children:e.jsxs("form",{onSubmit:h(d),children:[e.jsxs("div",{className:"px-6 pt-8 flex-grow scrollbar-hide w-full max-h-full pb-40",children:[e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(N,{label:n("CurrenciesName")}),e.jsxs("div",{className:"col-span-8 sm:col-span-4",children:[e.jsx(k,{required:!0,register:u,label:"Name",name:"name",type:"text",placeholder:"Name"}),e.jsx(T,{errorName:t.name})]})]}),e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(N,{label:n("CurrenciesSymbol")}),e.jsxs("div",{className:"col-span-8 sm:col-span-4",children:[e.jsx(k,{required:!0,register:u,label:"Symbol",name:"symbol",type:"text",placeholder:"Symbol"}),e.jsx(T,{errorName:t.symbol})]})]}),e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(N,{label:n("CurrenciesEnabled")}),e.jsx("div",{className:"col-span-8 sm:col-span-1 text-align-left",children:e.jsx(z,{processOption:a,handleProcess:m})})]})]}),e.jsx(H,{id:l,title:"Currency",isSubmitting:i})]})})]})},$=({currency:l,isCheck:t,setIsCheck:d})=>{const{title:u,serviceId:a,handleModalOpen:m,handleUpdate:i}=B(),h=r=>{const{id:j,checked:x}=r.target;d([...t,j]),x||d(t.filter(g=>g!==j))};return e.jsxs(e.Fragment,{children:[t.length<1&&e.jsx(I,{id:a,title:u}),e.jsx(F,{children:e.jsx(P,{id:a})}),e.jsx(s.TableBody,{children:l?.map(r=>e.jsxs(s.TableRow,{children:[e.jsx(s.TableCell,{children:e.jsx(E,{type:"checkbox",name:r.symbol,id:r._id,handleClick:h,isChecked:t.includes(r._id)})}),e.jsx(s.TableCell,{className:"text-center",children:e.jsx("span",{className:"font-medium text-sm",children:r.name})}),e.jsx(s.TableCell,{className:"text-center",children:e.jsx("span",{className:"font-medium text-sm",children:r.symbol})}),e.jsx(s.TableCell,{className:"text-center",children:e.jsx(O,{id:r._id,status:r.status,currencyStatusName:"status"})}),e.jsx(s.TableCell,{children:e.jsx(G,{title:r.name,id:r._id,handleUpdate:i,handleModalOpen:m})})]},r._id))})]})},Ae=()=>{const{toggleDrawer:l}=b.useContext(D),{allId:t,handleUpdateMany:d,handleDeleteMany:u}=B(),{data:a,loading:m,error:i}=K(C.getAllCurrency),{totalResults:h,resultsPerPage:r,dataTable:j,handleChangePage:x,handleSubmitCurrency:g,currencyRef:w}=Q(a),[f,c]=b.useState(!1),[p,o]=b.useState([]),y=()=>{c(!f),o(a.map(M=>M._id)),f&&o([])};return e.jsxs(e.Fragment,{children:[e.jsx(J,{children:"Currencies"}),e.jsx(q,{ids:t,title:"Currencies"}),e.jsx(F,{children:e.jsx(P,{})}),e.jsx(I,{ids:t,setIsCheck:o,title:"Selected Currencies"}),e.jsx(Y,{children:e.jsx(s.Card,{className:"min-w-0 shadow-xs overflow-hidden bg-white dark:bg-gray-800 mb-5",children:e.jsx(s.CardBody,{children:e.jsxs("form",{onSubmit:g,className:"py-3 grid gap-4 lg:gap-6 xl:gap-6 md:flex xl:flex md:justify-between",children:[e.jsx("div",{className:"w-full",children:e.jsx(s.Input,{ref:w,type:"search",placeholder:n("SearchIsoCode")})}),e.jsxs("div",{className:"lg:flex  md:flex xl:justify-end xl:w-1/2  md:w-full md:justify-start flex-grow-0",children:[e.jsx("div",{className:"w-full md:w-40 lg:w-40 xl:w-40 mr-3 mb-3 lg:mb-0",children:e.jsxs(s.Button,{disabled:p.length<1,onClick:()=>d(p),className:"w-full rounded-md h-12 btn-gray text-gray-600",children:[e.jsx("span",{className:"mr-2",children:e.jsx(U,{})}),"Bulk Action"]})}),e.jsx("div",{className:"w-full md:w-32 lg:w-32 xl:w-32 mr-3 mb-3 lg:mb-0",children:e.jsxs(s.Button,{disabled:p.length<1,onClick:()=>u(p),className:"w-full rounded-md h-12 bg-red-500 btn-red",children:[e.jsx("span",{className:"mr-2",children:e.jsx(R,{})}),"Delete"]})}),e.jsxs(s.Button,{onClick:l,className:"rounded-md h-12 w-48",children:[e.jsx("span",{className:"mr-2",children:e.jsx(L,{})}),"Add Currency"]})]})]})})})}),m?e.jsx(W,{row:12,col:7,width:163,height:20}):i?e.jsx("span",{className:"text-center mx-auto text-red-500",children:i}):a.length!==0&&e.jsxs(s.TableContainer,{className:"mb-8 rounded-b-lg",children:[e.jsxs(s.Table,{children:[e.jsx(s.TableHeader,{children:e.jsxs("tr",{children:[e.jsx(s.TableCell,{children:e.jsx(E,{type:"checkbox",name:"selectAll",id:"selectAll",isChecked:f,handleClick:y})}),e.jsx(s.TableCell,{className:"text-center",children:n("CurrenciesName")}),e.jsx(s.TableCell,{className:"text-center",children:n("CurrenciesSymbol")}),e.jsx(s.TableCell,{className:"text-center",children:n("CurrenciesEnabled")}),e.jsx(s.TableCell,{className:"text-right",children:n("CurrenciesActions")})]})}),e.jsx($,{currency:j,isCheck:p,setIsCheck:o})]}),e.jsx(s.TableFooter,{children:e.jsx(s.Pagination,{totalResults:h,resultsPerPage:r,onChange:x,label:"Table navigation"})})]}),!m&&a.length===0&&!i&&e.jsx(X,{title:"Sorry, There are no currency right now."})]})};export{Ae as default};
