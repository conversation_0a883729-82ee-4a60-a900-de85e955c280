function v(e){return Object.prototype.toString.call(e)==="[object Array]"}function O(e,n){if(!e)throw new Error(n)}function A(e){return Object.keys(e)}function x(e){return Object.keys(e).map(function(n){return[n,e[n]]})}function m(e,n,t){var r="."+n,a=new RegExp("(\\".concat(n,")?$"));return t(e).replace(a,r)}function z(e){return e.replace(/[^_a-zA-Z 0-9:\-\.]/g,"").replace(/^([ 0-9-:\-\.]|(xml))+/i,"").replace(/ +/g,"-")}function W(e){return Array(e+1).join(" ")}function Y(e){return e.replace(/([<>&])/g,function(n,t){switch(t){case"<":return"&lt;";case">":return"&gt;";case"&":return"&amp;";default:return""}})}function H(e,n,t){switch(n){case"txt":{var r="text/plain;charset=utf-8";return t?URL.createObjectURL(new Blob([e],{type:r})):"data:,".concat(r)+encodeURIComponent(e)}case"css":{var r="text/css;charset=utf-8";return t?URL.createObjectURL(new Blob([e],{type:r})):"data:,".concat(r)+encodeURIComponent(e)}case"html":{var r="text/html;charset=utf-8";return t?URL.createObjectURL(new Blob([e],{type:r})):"data:,".concat(r)+encodeURIComponent(e)}case"json":{var r="text/json;charset=utf-8";return t?URL.createObjectURL(new Blob([e],{type:r})):"data:,".concat(r)+encodeURIComponent(e)}case"csv":{var r="text/csv;charset=utf-8";return t?URL.createObjectURL(new Blob([e],{type:r})):"data:,".concat(r)+encodeURIComponent(e)}case"xls":{var r="text/application/vnd.ms-excel;charset=utf-8";return t?URL.createObjectURL(new Blob([e],{type:r})):"data:,".concat(r)+encodeURIComponent(e)}case"xml":{var r="text/application/xml;charset=utf-8";return t?URL.createObjectURL(new Blob([e],{type:r})):"data:,".concat(r)+encodeURIComponent(e)}default:return""}}function M(e,n,t,r){t===void 0&&(t="download"),r===void 0&&(r=!0);var a=H(e,n,r),c=document.createElement("a");c.href=a,c.download=t,c.setAttribute("style","visibility:hidden"),document.body.appendChild(c),c.dispatchEvent(new MouseEvent("click",{bubbles:!1,cancelable:!1,view:window})),document.body.removeChild(c)}var s=function(){return s=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},s.apply(this,arguments)},y=function(e,n){var t=typeof Symbol=="function"&&e[Symbol.iterator];if(!t)return e;var r=t.call(e),a,c=[],o;try{for(;(n===void 0||n-- >0)&&!(a=r.next()).done;)c.push(a.value)}catch(i){o={error:i}}finally{try{a&&!a.done&&(t=r.return)&&t.call(r)}finally{if(o)throw o.error}}return c};function K(e){if(!e||v(e)&&!e.length||!v(e)&&!A(e).length)return function(t){return t};var n=v(e)?e.reduce(function(t,r){var a;return s(s({},t),(a={},a[r]=r,a))},Object.create(null)):e;return function(t){return v(t)?t.map(function(r){return x(r).reduce(function(a,c){var o=y(c,2),i=o[0],u=o[1];return i in n&&(a[n[i]]=u),a},Object.create(null))}).filter(function(r){return A(r).length}):x(t).reduce(function(r,a){var c=y(a,2),o=c[0],i=c[1];return o in n&&(r[n[o]]=i),r},Object.create(null))}}function B(e){var n="Invalid export data. Please provide a valid JSON";try{return typeof e=="string"?JSON.parse(e):e}catch{throw new Error(n)}}function Z(e,n,t){n===void 0&&(n=null);var r="Invalid export data. Please provide valid JSON object";try{return JSON.stringify(e,n,t)}catch{throw new Error(r)}}function C(e){return e.map(x).reduce(function(n,t,r){return t.reduce(function(a,c){var o=y(c,2),i=o[0],u=o[1],l=a[i]||Array.from({length:e.length}).map(function(f){return""});return l[r]=(typeof u!="string"?JSON.stringify(u):u)||"",a[i]=l,a},n)},Object.create(null))}function D(e,n){return n===void 0&&(n=function(t){return t}),n(x(e).map(function(t){var r=y(t,2),a=r[0],c=r[1];return{fieldName:a,fieldValues:c}}))}function $(e){var n=/,|"|\n/.test(e)?'"':"",t=e.replace(/"/g,'""');return"".concat(n).concat(t).concat(n)}var q={beforeTableEncode:function(e){return e},delimiter:","};function Q(e,n){n===void 0&&(n={});var t=s(s({},q),n),r=t.beforeTableEncode,a=t.delimiter;if(!e.length)return"";var c=C(e),o=D(c,r),i=o.map(function(f){var d=f.fieldName;return d}).join(a)+`\r
`,u=o.map(function(f){var d=f.fieldValues;return d}).map(function(f){return f.map($)}),l=u.reduce(function(f,d){return f.map(function(_,w){return"".concat(_).concat(a).concat(d[w])})});return i+l.join(`\r
`)}function ee(e,n){O(e.length>0);var t=C(e),r=D(t,n),a=r.map(function(i){var u=i.fieldName;return u}).join("</b></th><th><b>"),c=r.map(function(i){var u=i.fieldValues;return u}).map(function(i){return i.map(function(u){return"<td>".concat(u,"</td>")})}),o=c.reduce(function(i,u){return i.map(function(l,f){return"".concat(l).concat(u[f])})});return`
    <table>
      <thead>
        <tr><th><b>`.concat(a,`</b></th></tr>
      </thead>
      <tbody>
        <tr>`).concat(o.join(`</tr>
        <tr>`),`</tr>
      </tbody>
    </table>
  `)}var re={beforeTableEncode:function(e){return e}};function te(e,n){var t=s(s({},re),n).beforeTableEncode;if(!e.length)return"";var r=`<html>
  <head>
    <meta charset="UTF-8" />
  </head >
  <body>
    `.concat(ee(e,t),`
  </body>
</html >
`);return r}function ne(e){var n=`<?xml version="1.0" encoding="utf-8"?><!DOCTYPE base>
`.concat(g(e,"base"),`
`);return n}function g(e,n,t,r){t===void 0&&(t="element"),r===void 0&&(r=0);var a=z(n),c=W(r);if(e==null)return"".concat(c,"<").concat(a," />");var o=v(e)?e.map(function(u){return g(u,t,t,r+2)}).join(`
`):typeof e=="object"?x(e).map(function(u){var l=y(u,2),f=l[0],d=l[1];return g(d,f,t,r+2)}).join(`
`):c+"  "+Y(String(e)),i="".concat(c,"<").concat(a,`>
`).concat(o,`
`).concat(c,"</").concat(a,">");return i}var ae={txt:"txt",css:"css",html:"html",json:"json",csv:"csv",xls:"xls",xml:"xml"};function F(e){var n=e.data,t=e.fileName,r=t===void 0?"download":t,a=e.extension,c=e.fileNameFormatter,o=c===void 0?function(j){return j.replace(/\s+/,"_")}:c,i=e.fields,u=e.exportType,l=u===void 0?"txt":u,f=e.replacer,d=f===void 0?null:f,_=e.space,w=_===void 0?4:_,E=e.processor,p=E===void 0?M:E,S=e.withBOM,V=S===void 0?!1:S,L=e.beforeTableEncode,R=L===void 0?function(j){return j}:L,U=e.delimiter,J=U===void 0?",":U,N="Invalid export data. Please provide an array of objects",P="Can't export unknown data type ".concat(l,"."),k="Can't export string data to ".concat(l,".");if(typeof n=="string")switch(l){case"txt":case"css":case"html":return p(n,l,m(r,a??l,o));default:throw new Error(k)}var X=K(i),b=X(B(n)),T=Z(b,d,w);switch(l){case"txt":case"css":case"html":return p(T,l,m(r,a??l,o));case"json":return p(T,l,m(r,a??"json",o));case"csv":{O(v(b),N);var G="\uFEFF",I=Q(b,{beforeTableEncode:R,delimiter:J}),h=V?G+I:I;return p(h,l,m(r,a??"csv",o))}case"xls":{O(v(b),N);var h=te(b,{beforeTableEncode:R});return p(h,l,m(r,a??"xls",o))}case"xml":{var h=ne(b);return p(h,l,m(r,a??"xml",o))}default:throw new Error(P)}}F.types=ae;F.processors={downloadFile:M};export{F as e};
