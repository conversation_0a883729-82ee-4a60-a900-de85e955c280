import{j as e,r as c,h as a,l as B}from"./index-BnbL29JP.js";import{u as I,N as i,t as M,f as N,S as _,A as E}from"./Layout-pFzaaQDc.js";import{P as F}from"./PageTitle-FuOKSvYQ.js";import{a as d,n as j}from"./toast-DZMsp61l.js";import"./iconBase-CKOh_aia.js";const y=({id:m,name:n,type:x,handleClick:g,isChecked:u})=>e.jsx("input",{id:m,name:n,type:x,onChange:g,checked:u}),W=()=>{const[m,n]=c.useState([]),[x,g]=c.useState(0),[u,r]=c.useState(0),[b,f]=c.useState(2),[l,o]=c.useState([]),[p,k]=c.useState(!1),{showDateTimeFormat:w}=I(),C=async s=>{try{await i.updateStatusNotification(s,{status:"read"});const t=await i.getAllNotification();n(t?.notifications),r(t?.totalUnreadDoc),window.location.reload(!1)}catch(t){d(t?.response?.data?.message||t?.message)}},A=async s=>{try{await i.deleteNotification(s);const t=await i.getAllNotification();n(t?.notifications),r(t?.totalUnreadDoc),g(t?.totalDoc)}catch(t){d(t?.response?.data?.message||t?.message)}},D=async s=>{try{const t=await i.getAllNotification(s);n(h=>[...h,...t?.notifications]),r(t?.totalUnreadDoc),f(h=>h+1)}catch(t){d(t?.response?.data?.message||t?.message)}},S=async()=>{try{const s=await i.updateManyStatusNotification({ids:l,status:"read"});o([]),j(s.message),f(1);const t=await i.getAllNotification();n(t?.notifications),r(t?.totalUnreadDoc)}catch(s){d(s?.response?.data?.message||s?.message)}},v=async()=>{try{const s=await i.deleteManyNotification({ids:l});j(s.message),o([]),f(1);const t=await i.getAllNotification();n(t?.notifications),r(t?.totalUnreadDoc)}catch(s){d(s?.response?.data?.message||s?.message)}},T=()=>{k(!p),o(m?.map(s=>s._id)),p&&o([])},U=s=>{const{id:t,checked:h}=s.target;o([...l,t]),h||o(l.filter(R=>R!==t))};return c.useEffect(()=>{(async()=>{try{const s=await i.getAllNotification();n(s?.notifications),r(s?.totalUnreadDoc),g(s?.totalDoc),f(1)}catch(s){d(s?.response?.data?.message||s?.message)}})()},[]),e.jsxs(e.Fragment,{children:[e.jsx(F,{children:"Notifications"}),e.jsx(a.Card,{className:"shadow-xs overflow-hidden bg-white dark:bg-gray-800 mb-5",children:e.jsxs(a.CardBody,{className:"flex justify-between",children:[e.jsx("div",{className:"",children:e.jsxs(a.Button,{disabled:l?.length<1,onClick:S,className:"w-full rounded-md h-10 flex items-center justify-center bg-blue-500 text-white px-1 hover:bg-blue-700",children:[e.jsx("span",{className:"mr-2",children:e.jsx(M,{})}),"Mark is read"]})}),e.jsx("div",{className:"",children:e.jsxs(a.Button,{disabled:l?.length<1,onClick:v,className:"w-full rounded-md h-10 bg-red-500 btn-red",children:[e.jsx("span",{className:"mr-3",children:e.jsx(N,{})}),"Delete"]})})]})}),e.jsx(a.Card,{className:"shadow-xs overflow-hidden bg-white dark:bg-gray-800 mb-5",children:e.jsxs(a.CardBody,{style:{padding:0},children:[e.jsx("div",{className:"p-4 dark:text-gray-300",children:e.jsxs("p",{className:"text-sm font-semibold text-teal-700",children:["Unread Notification (",u,")"]})}),e.jsxs("div",{className:"border rounded-md",children:[e.jsxs("div",{className:"bg-gray-200 border-gray-400 p-2 dark:bg-gray-700 dark:text-gray-400 flex justify-between",children:[e.jsxs("div",{className:"flex",children:[e.jsx(y,{type:"checkbox",name:"selectAll",id:"selectAll",handleClick:T,isChecked:p}),e.jsx("p",{className:"text-xs font-semibold text-gray-500 my-auto dark:text-gray-300 ml-6 uppercase",children:"Notification"})]}),e.jsx("div",{className:"text-right",children:e.jsx("p",{className:"text-xs font-semibold text-gray-500 my-auto dark:text-gray-300 mr-2 uppercase",children:"Action"})})]}),e.jsx("div",{className:"w-full lg:h-lg md:h-sm h-md relative",children:e.jsx(_,{className:"scrollbar-hide",children:e.jsxs(a.TableContainer,{className:"border-none p-2",children:[e.jsx(a.Table,{children:e.jsx(a.TableBody,{className:"w-full h-440",children:m.map((s,t)=>e.jsxs(a.TableRow,{className:"border-none",children:[e.jsx(a.TableCell,{style:{padding:0},children:e.jsx(y,{type:"checkbox",name:s?._id,id:s._id,handleClick:U,isChecked:l?.includes(s._id)})}),e.jsx(a.TableCell,{className:"md:w-full w-1/5",style:{paddingRight:0},children:e.jsxs(B,{to:s.productId?`/product/${s.productId}`:`/order/${s.orderId}`,className:"flex items-center",onClick:()=>C(s._id),children:[e.jsx(a.Avatar,{className:"mr-2 md:block hidden bg-gray-50 border border-gray-200",src:s.image,alt:"image"}),e.jsxs("div",{className:"notification-content",children:[e.jsx("div",{className:"md:inline-block hidden",children:e.jsx("h6",{className:"font-medium text-gray-500",children:s?.message})}),e.jsx("div",{className:"md:hidden",children:e.jsx("h6",{className:"font-medium text-gray-500",children:s?.message.substring(0,33)+"..."})}),e.jsxs("p",{className:"flex items-center text-xs text-gray-400",children:[s.productId?e.jsx(a.Badge,{type:"danger",children:"Stock Out"}):e.jsx(a.Badge,{type:"success",children:"New Order"}),e.jsx("span",{className:"ml-2",children:w(s?.createdAt)})]})]}),s.status==="unread"&&e.jsx("span",{className:"px-2 md:flex hidden focus:outline-none text-emerald-600",children:e.jsx("img",{src:E,width:12,height:12,alt:"ellipse",className:"w-3 h-3 text-emerald-600"})})]})}),e.jsx(a.TableCell,{className:"text-right",style:{padding:`${window.innerWidth<420?"0":"0.5rem"}`},children:e.jsxs("div",{className:"group inline-block relative",children:[e.jsx("button",{onClick:()=>A(s._id),type:"button",className:"px-2 group-hover:text-blue-500 text-red-500 focus:outline-none",children:e.jsx(N,{})}),e.jsx("div",{className:"absolute hidden group-hover:inline-block bg-gray-50 dark:text-white mr-8 mb-1 right-0 z-50 px-3 py-2 text-sm font-medium text-red-600 rounded-lg shadow-sm tooltip dark:bg-gray-700",children:"Delete"})]})})]},t+1))})}),e.jsx("div",{children:x>5&&m.length!==x?e.jsx("div",{className:"text-center py-2",children:e.jsx("button",{onClick:()=>D(b+1),type:"button",className:"focus:outline-none text-blue-700 hover:underline transition ease-out duration-200 dark:text-gray-400",children:"See more notifications"})}):null})]})})})]})]})})]})};export{W as default};
