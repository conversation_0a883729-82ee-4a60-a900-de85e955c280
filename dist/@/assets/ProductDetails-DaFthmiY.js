import{j as e,h as s,s as C,k as P,r as h,S as v}from"./index-BnbL29JP.js";import{u as w}from"./useAsync-DWXVKl2F.js";import{u as S}from"./useFilter-7smJgyBE.js";import{u as D,P as B}from"./ProductDrawer-rLFmsmir.js";import{u as F,M as A}from"./DrawerButton-BQHT-xfW.js";import{P as E}from"./ProductServices-CXwJ-2YB.js";import{u as f}from"./Layout-pFzaaQDc.js";import{L as I}from"./Loading-_yh85t44.js";import{P as j}from"./PageTitle-FuOKSvYQ.js";import"./useDisableForDemo-Bu4HEiKz.js";import"./toast-DZMsp61l.js";import"./CouponServices-BN-cEYCp.js";import"./CurrencyServices-3wuDp8cZ.js";import"./ParentCategory-BlfDuDd5.js";import"./index-CJFAfqOd.js";import"./index.esm-ClJnGQn6.js";import"./InputArea-B_vEs1uv.js";import"./LabelArea-DQFDcuEN.js";import"./InputValue-ohzJqb3r.js";import"./useTranslationValue-DCEiB8J2.js";import"./Uploader-COz_-nhd.js";import"./_commonjs-dynamic-modules-BJDvAndU.js";import"./index-C148XJoK.js";import"./Tooltip-CCK2Gwc2.js";import"./index.prod-BbOPZ2BB.js";import"./SelectLanguageTwo-KDdaD-gj.js";import"./spinner-CkndCogW.js";import"./iconBase-CKOh_aia.js";const O=({variants:n,variantTitle:t})=>{const{showingTranslateValue:x,currency:c,getNumberTwo:d}=f();return e.jsx(e.Fragment,{children:e.jsx(s.TableBody,{children:n?.map((r,m)=>e.jsxs(s.TableRow,{children:[e.jsx(s.TableCell,{className:"font-semibold uppercase text-xs",children:m+1}),e.jsx(s.TableCell,{children:e.jsx("div",{className:"flex items-center",children:r.image?e.jsx(s.Avatar,{className:"hidden p-1 mr-2 md:block bg-gray-50 shadow-none",src:r.image,alt:"product"}):e.jsx(s.Avatar,{src:"https://res.cloudinary.com/ahossain/image/upload/v1655097002/placeholder_kvepfp.png",alt:"product",className:"hidden p-1 mr-2 md:block bg-gray-50 shadow-none"})})}),e.jsx(s.TableCell,{children:e.jsxs("div",{className:"flex flex-col text-sm",children:[e.jsx("span",{children:t?.map(a=>{const l=a?.variants?.filter(i=>i?.name!=="All")?.find(i=>i._id===r[a?._id])?.name;return l===void 0?l?.en:x(l)})?.filter(Boolean).join(" ")}),r.productId&&e.jsxs("span",{className:"text-xs text-gray-500",children:["(",r.productId,")"]})]})}),e.jsx(s.TableCell,{className:"font-semibold uppercase text-xs",children:r.sku}),e.jsx(s.TableCell,{className:"font-semibold uppercase text-xs",children:r.barcode}),e.jsxs(s.TableCell,{className:"font-semibold uppercase text-xs",children:[c,d(r.originalPrice)]}),e.jsxs(s.TableCell,{className:"font-semibold uppercase text-xs",children:[c,d(r.price)]}),e.jsx(s.TableCell,{className:"font-semibold uppercase text-xs",children:r.quantity})]},m+1))})})},de=()=>{const{id:n}=C(),{t}=P(),{handleUpdate:x}=F(),{attribue:c}=D(n),[d,r]=h.useState([]),{lang:m}=h.useContext(v),{data:a,loading:o}=w(()=>E.getProductById(n)),{currency:l,showingTranslateValue:i,getNumberTwo:g}=f(),{handleChangePage:b,totalResults:y,resultsPerPage:N,dataTable:T}=S(a?.variants);return h.useEffect(()=>{if(!o){const p=Object.keys(Object.assign({},...a?.variants)),u=c?.filter(k=>p.includes(k._id));r(u)}},[c,a?.variants,o,m]),console.log("product",a),e.jsxs(e.Fragment,{children:[e.jsx(A,{product:!0,children:e.jsx(B,{id:n})}),e.jsx(j,{children:t("ProductDetails")}),o?e.jsx(I,{loading:o}):e.jsx("div",{className:"inline-block overflow-y-auto h-full align-middle transition-all transform",children:e.jsxs("div",{className:"flex flex-col lg:flex-row md:flex-row w-full overflow-hidden",children:[e.jsx("div",{className:"flex-shrink-0 flex items-center justify-center h-auto",children:a?.image[0]?e.jsx("img",{src:a?.image[0],alt:"product",className:"h-64 w-64"}):e.jsx("img",{src:"https://res.cloudinary.com/ahossain/image/upload/v1655097002/placeholder_kvepfp.png",alt:"product"})}),e.jsxs("div",{className:"w-full flex flex-col p-5 md:p-8 text-left",children:[e.jsxs("div",{className:"mb-5 block ",children:[e.jsx("div",{className:"font-serif font-semibold py-1 text-sm",children:e.jsxs("p",{className:"text-sm text-gray-500 pr-4",children:[t("Status"),":"," ",a.status==="show"?e.jsx("span",{className:"text-emerald-400",children:t("ThisProductShowing")}):e.jsx("span",{className:"text-red-400",children:t("ThisProductHidden")})]})}),e.jsx("h2",{className:"text-heading text-lg md:text-xl lg:text-2xl font-semibold font-serif dark:text-gray-400",children:i(a?.title)}),e.jsxs("p",{className:"uppercase font-serif font-medium text-gray-500 dark:text-gray-400 text-sm",children:[t("Sku")," :"," ",e.jsx("span",{className:"font-bold text-gray-500 dark:text-gray-500",children:a?.sku})]})]}),e.jsx("div",{className:"font-serif product-price font-bold dark:text-gray-400",children:e.jsxs("span",{className:"inline-block text-2xl",children:[l,g(a?.prices?.price),a?.prices?.discount>=1&&e.jsxs("del",{className:"text-gray-400 dark:text-gray-500 text-lg pl-2",children:[l,g(a?.prices?.originalPrice)]})]})}),e.jsxs("div",{className:"mb-3",children:[a?.stock<=0?e.jsxs(s.Badge,{type:"danger",children:[e.jsx("span",{className:"font-bold",children:t("StockOut")})," "]}):e.jsxs(s.Badge,{type:"success",children:[" ",e.jsx("span",{className:"font-bold",children:t("InStock")})]}),e.jsxs("span",{className:"text-sm text-gray-500 dark:text-gray-400 font-medium pl-4",children:[t("Quantity"),": ",a?.stock]})]}),e.jsx("p",{className:"text-sm leading-6 text-gray-500 dark:text-gray-400 md:leading-7",children:i(a?.description)}),e.jsxs("div",{className:"flex flex-col mt-4",children:[e.jsxs("p",{className:"font-serif font-semibold py-1 text-gray-500 text-sm",children:[e.jsxs("span",{className:"text-gray-700 dark:text-gray-400",children:[t("Category"),":"," "]})," ",i(a?.category?.name)]}),e.jsx("div",{className:"flex flex-row",children:JSON.parse(a?.tag).map((p,u)=>e.jsx("span",{className:"bg-gray-200 mr-2 border-0 text-gray-500 rounded-full inline-flex items-center justify-center px-2 py-1 text-xs font-semibold font-serif mt-2 dark:bg-gray-700 dark:text-gray-300",children:p},u+1))})]}),e.jsx("div",{className:"mt-6",children:e.jsx("button",{onClick:()=>x(n),className:"cursor-pointer leading-5 transition-colors duration-150 font-medium text-sm focus:outline-none px-5 py-2 rounded-md text-white bg-emerald-500 border border-transparent active:bg-emerald-600 hover:bg-emerald-600 ",children:t("EditProduct")})})]})]})}),a?.isCombination&&d?.length>0&&!o&&e.jsxs(e.Fragment,{children:[e.jsx(j,{children:t("ProductVariantList")}),e.jsxs(s.TableContainer,{className:"mb-8 rounded-b-lg",children:[e.jsxs(s.Table,{children:[e.jsx(s.TableHeader,{children:e.jsxs("tr",{children:[e.jsx(s.TableCell,{children:t("SR")}),e.jsx(s.TableCell,{children:t("Image")}),e.jsx(s.TableCell,{children:t("Combination")}),e.jsx(s.TableCell,{children:t("Sku")}),e.jsx(s.TableCell,{children:t("Barcode")}),e.jsx(s.TableCell,{children:t("OrginalPrice")}),e.jsx(s.TableCell,{children:t("SalePrice")}),e.jsx(s.TableCell,{children:t("Quantity")})]})}),e.jsx(O,{lang:m,variants:T,currency:l,variantTitle:d})]}),e.jsx(s.TableFooter,{children:e.jsx(s.Pagination,{totalResults:y,resultsPerPage:N,onChange:b,label:"Product Page Navigation"})})]})]})]})};export{de as default};
