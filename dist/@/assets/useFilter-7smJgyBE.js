import{a as ze,C as Ze,b as He,A as Xe,P as ke,c as Ge}from"./ProductServices-CXwJ-2YB.js";import{d as B,u as Ke}from"./Layout-pFzaaQDc.js";import{g as fe,r as a,u as Qe,S as We,L as et}from"./index-BnbL29JP.js";import{u as tt}from"./useDisableForDemo-Bu4HEiKz.js";import{C as st}from"./CouponServices-BN-cEYCp.js";import{C as at}from"./CurrencyServices-3wuDp8cZ.js";import{n as D,a as d}from"./toast-DZMsp61l.js";var M={exports:{}},ot=M.exports,pe;function nt(){return pe||(pe=1,function(L,T){(function(C,g){L.exports=g()})(ot,function(){return function(C,g,y){g.prototype.isBetween=function(v,w,p,h){var b=y(v),S=y(w),x=(h=h||"()")[0]==="(",A=h[1]===")";return(x?this.isAfter(b,p):!this.isBefore(b,p))&&(A?this.isBefore(S,p):!this.isAfter(S,p))||(x?this.isBefore(b,p):!this.isAfter(b,p))&&(A?this.isAfter(S,p):!this.isBefore(S,p))}}})}(M)),M.exports}var rt=nt();const it=fe(rt);var V={exports:{}},ct=V.exports,me;function ut(){return me||(me=1,function(L,T){(function(C,g){L.exports=g()})(ct,function(){return function(C,g,y){g.prototype.isToday=function(){var v="YYYY-MM-DD",w=y();return this.format(v)===w.format(v)}}})}(V)),V.exports}var dt=ut();const lt=fe(dt),pt={type:"object",properties:{_id:{type:"string"},name:{type:"object"},description:{type:"object"},icon:{type:"string"},status:{type:"string"}},required:["name"]},mt={type:"object",properties:{status:{type:"string"},title:{type:"object"},name:{type:"object"},variants:{type:"array"},option:{type:"string"},type:{type:"string"}},required:["name","title"]},ft={type:"object",properties:{title:{type:"object"},couponCode:{type:"string"},endTime:{type:"string"},discountPercentage:{type:"number"},minimumAmount:{type:"number"},productType:{type:"string"},logo:{type:"string"},discountType:{type:"object"},status:{type:"string"}},required:["title","couponCode","endTime","status"]},gt={type:"object",properties:{name:{type:"string"},email:{type:"string"}},required:["name","email"]},Jt=L=>{const T=new ze({allErrors:!0}),[C,g]=a.useState(""),[y,v]=a.useState(""),[w,p]=a.useState(""),[h,b]=a.useState(""),[S,x]=a.useState(""),[A,ge]=a.useState(""),[N,Q]=a.useState(""),[_,W]=a.useState(""),[P,ee]=a.useState(""),[he,Se]=a.useState(""),[O,ye]=a.useState(""),[Y,Ce]=a.useState(""),[we,be]=a.useState([]),[De,Le]=a.useState([]),[Te,ve]=a.useState([]),[$,Ae]=a.useState(""),[z,Re]=a.useState(""),[U,xe]=a.useState(""),[Z,Pe]=a.useState(1),[Oe,te]=a.useState([]),[Fe,je]=a.useState(""),[Be,Ne]=a.useState(""),[_e,Ue]=a.useState(""),[m,H]=a.useState([]),[Ie,I]=a.useState(""),[Je,J]=a.useState(!1),[X,qe]=a.useState(""),[k]=a.useState([]),se=a.useRef(""),ae=a.useRef(""),oe=a.useRef(""),ne=a.useRef(""),re=a.useRef(""),G=a.useRef(""),ie=a.useRef(""),ce=a.useRef(""),ue=a.useRef(""),Ee=a.useRef(""),de=a.useRef("");B.extend(it),B.extend(lt);const i=Qe(),{lang:F,setIsUpdate:R,setLoading:l}=a.useContext(We),{globalSetting:le}=Ke(),{handleDisableForDemo:K}=tt(),q=a.useMemo(()=>{const n=new Date;n.setDate(n.getDate()-U);let e=L?.map(t=>{const c=new Date(t?.updatedAt).toLocaleString("en-US",{timeZone:le?.default_time_zone});return{...t,updatedDate:c==="Invalid Date"?"":c}});if(i.pathname==="/dashboard"){const t=e?.filter(u=>u.status==="Pending");be(t);const c=e?.filter(u=>u.status==="Processing");Le(c);const s=e?.filter(u=>u.status==="Delivered");ve(s);const o=e?.filter(u=>B(u.createdAt).isToday())?.reduce((u,j)=>u+j.total,0);je(o);const Ye=e?.filter(u=>B(u.createdAt).isBetween(new Date().setDate(new Date().getDate()-30),new Date))?.reduce((u,j)=>u+j.total,0);Ne(Ye);const $e=e?.reduce((u,j)=>u+j.total,0);Ue($e)}return C&&(e=e.filter(t=>t.parent===C)),y==="Low"&&(e=e.sort((t,c)=>t.price<c.price&&-1)),y==="High"&&(e=e.sort((t,c)=>t.price>c.price&&-1)),w&&(e=e.filter(t=>t?.title?.toLowerCase().includes(w.toLowerCase()))),_&&(e=e.filter(t=>t?.title[F]?.toLowerCase()?.includes(_?.toLowerCase())||t?.attribute?.toLowerCase().includes(_?.toLowerCase()))),N&&(e=e.filter(t=>t?.name[F]?.toLowerCase()?.includes(N?.toLowerCase())||t?.category?.toLowerCase().includes(N?.toLowerCase()))),z&&(e=e.filter(t=>t.role===z)),h&&(e=e.filter(t=>t?.name[F]?.toLowerCase().includes(h.toLowerCase())||t?.phone?.toLowerCase().includes(h.toLowerCase())||t?.email?.toLowerCase().includes(h.toLowerCase()))),S&&(e=e?.filter(t=>t?.title[F]?.toLowerCase()?.includes(S?.toLowerCase())||t?.couponCode?.toLowerCase().includes(S?.toLowerCase()))),$&&(e=e.filter(t=>t.status===$)),A&&(e=e.filter(t=>t.contact.toLowerCase().includes(A.toLowerCase()))),U&&(e=e.filter(t=>B(t.createdAt).isBetween(n,new Date))),P&&(e=e.filter(t=>t?.name?.toLowerCase().includes(P.toLowerCase())||t?.iso_code?.toLowerCase().includes(P.toLowerCase()))),X&&(e=e.filter(t=>t?.name.toLowerCase().includes(X.toLowerCase()))),O&&(e=e.filter(t=>t.name.toLowerCase().includes(O.toLowerCase())||t.iso_code.toLowerCase().includes(O.toLowerCase())||t.language_code.toLowerCase().includes(O.toLowerCase()))),Y&&(e=e.filter(t=>t.iso_code.toLowerCase().includes(Y.toLowerCase()))),e},[U,L,i.pathname,C,y,w,_,N,z,h,S,$,A,P,X,O,Y,G,le?.default_time_zone,F]),E=20,Me=q?.length,Ve=n=>{Pe(n)};return a.useEffect(()=>{te(q?.slice((Z-1)*E,Z*E))},[q,Z,E]),{userRef:oe,searchRef:ae,couponRef:ne,orderRef:re,categoryRef:G,attributeRef:ie,pending:we,processing:De,delivered:Te,todayOrder:Fe,monthlyOrder:Be,totalOrder:_e,setFilter:g,setSortedField:v,setStatus:Ae,setRole:Re,time:U,zone:he,setTime:xe,taxRef:Ee,setZone:Se,filename:Ie,countryRef:ce,dataTable:Oe,serviceData:q,country:P,setSearchText:p,setCountry:ee,isDisabled:Je,languageRef:ue,currencyRef:se,shippingRef:de,setSearchUser:b,setDataTable:te,setCategoryType:Q,handleChangePage:Ve,totalResults:Me,resultsPerPage:E,handleOnDrop:n=>{for(let e=0;e<n.length;e++)k.push(n[e].data)},setSearchCoupon:x,setAttributeTitle:W,handleSelectFile:n=>{if(n.preventDefault(),K())return;const e=new FileReader,t=n.target?.files[0];t&&t.type==="application/json"?(I(t?.name),J(!0),console.log("if"),e.readAsText(t,"UTF-8"),e.onload=c=>{let s=JSON.parse(c.target.result),f=[];i.pathname==="/categories"&&(f=s.map(o=>({_id:o._id,id:o.id,status:o.status,name:o.name,description:o.description,parentName:o.parentName,parentId:o.parentId,icon:o.icon}))),i.pathname==="/attributes"&&(f=s.map(o=>({_id:o._id,status:o.status,title:o.title,name:o.name,variants:o.variants,option:o.option,type:o.type}))),i.pathname==="/coupons"&&(f=s.map(o=>({title:o.title,couponCode:o.couponCode,endTime:o.endTime,discountPercentage:o.discountPercentage,minimumAmount:o.minimumAmount,productType:o.productType,logo:o.logo,discountType:o.discountType,status:o.status}))),i.pathname==="/customers"&&(f=s.map(o=>({name:o.name,email:o.email,password:o.password,phone:o.phone}))),H(f)}):t&&t.type==="text/csv"?(I(t?.name),J(!0),console.log("else if"),e.onload=async c=>{const s=c.target.result,f=await Ge().fromString(s);let o=[];i.pathname==="/categories"&&(o=f.map(r=>({_id:r._id,id:r.id,status:r.status,name:JSON.parse(r.name),description:JSON.parse(r.description),parentName:r.parentName,parentId:r.parentId,icon:r.icon}))),i.pathname==="/attributes"&&(o=f.map(r=>({status:r.status,title:JSON.parse(r.title),name:JSON.parse(r.name),variants:JSON.parse(r.variants),option:r.option,type:r.type}))),i.pathname==="/coupons"&&(o=f.map(r=>({title:JSON.parse(r.title),couponCode:r.couponCode,endTime:r.endTime,discountPercentage:r.discountPercentage?JSON.parse(r.discountPercentage):0,minimumAmount:r.minimumAmount?JSON.parse(r.minimumAmount):0,productType:r.productType,logo:r.logo,status:r.status}))),i.pathname==="/customers"&&(o=f.map(r=>({name:r.name,email:r.email,password:r.password,phone:r.phone}))),H(o)},e.readAsText(t)):(I(t?.name),J(!0),d("Unsupported file type!"))},handleSubmitUser:n=>{n.preventDefault(),b(oe.current.value)},handleSubmitForAll:n=>{n.preventDefault(),p(ae.current.value)},handleSubmitCoupon:n=>{n.preventDefault(),x(ne.current.value)},handleSubmitOrder:n=>{n.preventDefault(),ge(re.current.value)},handleSubmitCategory:n=>{n.preventDefault(),Q(G.current.value)},handleSubmitAttribute:n=>{n.preventDefault(),W(ie.current.value)},handleUploadProducts:()=>{if(k.length<1)d("Please upload/select csv file first!");else{if(K())return;ke.addAllProducts(k).then(n=>{D(n.message)}).catch(n=>d(n.message))}},handleSubmitCountry:n=>{n.preventDefault(),ee(ce.current.value)},handleSubmitCurrency:n=>{n.preventDefault(),Ce(se.current.value)},handleSubmitShipping:n=>{n.preventDefault(),qe(de.current.value)},handleSubmitLanguage:n=>{n.preventDefault(),ye(ue.current.value)},handleUploadMultiple:n=>{if(!K())if(m.length>1){if(i.pathname==="/categories"){l(!0);let e=m.map(s=>T.validate(pt,s));const t=s=>s===!0;e.every(t)?Ze.addAllCategory(m).then(s=>{l(!1),R(!0),D(s.message)}).catch(s=>{l(!1),d(s?s.response.data.message:s.message)}):d("Please enter valid data!")}if(i.pathname==="/customers"){l(!0);let e=m.map(s=>T.validate(gt,s));const t=s=>s===!0;e.every(t)?He.addAllCustomers(m).then(s=>{l(!1),R(!0),D(s.message)}).catch(s=>{l(!1),d(s?s.response.data.message:s.message)}):d("Please enter valid data!")}if(i.pathname==="/coupons"){l(!0);let e=m.map(s=>T.validate(ft,s));const t=s=>s===!0;e.every(t)?st.addAllCoupon(m).then(s=>{l(!1),R(!0),D(s.message)}).catch(s=>{l(!1),d(s?s.response.data.message:s.message)}):d("Please enter valid data!")}if(i.pathname==="/attributes"){l(!0);let e=m.map(s=>T.validate(mt,s));const t=s=>s===!0;e.every(t)?Xe.addAllAttributes(m).then(s=>{l(!1),R(!0),D(s.message)}).catch(s=>{l(!1),d(s?s.response.data.message:s.message)}):d("Please enter valid data!")}i.pathname==="/languages"&&et.addAllLanguage(m).then(e=>{R(!0),D(e.message)}).catch(e=>d(e?e.response.data.message:e.message)),i.pathname==="/currencies"&&at.addAllCurrency(m).then(e=>{R(!0),D(e.message)}).catch(e=>d(e?e.response.data.message:e.message))}else d("Please select a valid .JSON/.CSV/.XLS file first!")},handleRemoveSelectFile:n=>{I(""),H([]),setTimeout(()=>J(!1),1e3)}}};export{lt as a,it as i,Jt as u};
