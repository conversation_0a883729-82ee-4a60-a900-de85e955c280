import{r as u,S as q,j as e,t as o,h as s,k as J}from"./index-BnbL29JP.js";import{u as z,d as V,l as K,e as Q,f as X,g as ee}from"./Layout-pFzaaQDc.js";import{C as F}from"./CouponServices-BN-cEYCp.js";import{u as se}from"./useAsync-DWXVKl2F.js";import{T as _,D as le,u as Y,M as $}from"./DrawerButton-BQHT-xfW.js";import{u as ae}from"./useFilter-7smJgyBE.js";import{P as te}from"./PageTitle-FuOKSvYQ.js";import{D as W,E as re}from"./EditDeleteButton-BfkOlssy.js";import{C as Z,S as ne,B as ie}from"./BulkActionDrawer-0F-ByaaS.js";import{u as oe,E as v}from"./index.esm-ClJnGQn6.js";import{I as R}from"./InputArea-B_vEs1uv.js";import{I as L}from"./InputValue-ohzJqb3r.js";import{L as N}from"./LabelArea-DQFDcuEN.js";import{U as de}from"./Uploader-COz_-nhd.js";import{a as O,n as H}from"./toast-DZMsp61l.js";import{u as ce}from"./useTranslationValue-DCEiB8J2.js";import{S as me}from"./SwitchToggle-CGiqq8S_.js";import{a as pe}from"./index.prod-BbOPZ2BB.js";import{T as ue}from"./TableLoading-BGBA3G2p.js";import{N as xe}from"./NotFound-H45Wi1lW.js";import{U as ge}from"./UploadMany-DQiD3frs.js";import{A as he}from"./AnimatedContent-0V4vlNfe.js";import"./iconBase-CKOh_aia.js";import"./SelectLanguageTwo-KDdaD-gj.js";import"./spinner-CkndCogW.js";import"./ProductServices-CXwJ-2YB.js";import"./index-C148XJoK.js";import"./useDisableForDemo-Bu4HEiKz.js";import"./CurrencyServices-3wuDp8cZ.js";import"./AdminServices-DjQuFfvs.js";import"./Tooltip-CCK2Gwc2.js";import"./ParentCategory-BlfDuDd5.js";import"./_commonjs-dynamic-modules-BJDvAndU.js";import"./exportFromJSON-fDIoOtpr.js";const je=l=>{const{isDrawerOpen:r,closeDrawer:x,setIsUpdate:d,lang:i}=u.useContext(q),[f,g]=u.useState(""),[c,h]=u.useState("en"),[m,p]=u.useState({}),[C,y]=u.useState(!1),[T,a]=u.useState(!1),[j,b]=u.useState(!1),{currency:S}=z(),{handlerTextTranslateHandler:B}=ce(),{register:E,handleSubmit:I,setValue:n,clearErrors:w,formState:{errors:U}}=oe(),k=async t=>{try{b(!0);const P={title:{...await B(t.title,c,m?.title),[c]:title},couponCode:t.couponCode,endTime:t.endTime,minimumAmount:t.minimumAmount,logo:f,lang:c,status:C?"show":"hide",discountType:{type:T?"percentage":"fixed",value:t.discountPercentage},productType:t.productType};if(l){const A=await F.updateCoupon(l,P);d(!0),b(!1),H(A.message),x()}else{const A=await F.addCoupon(P);d(!0),b(!1),H(A.message),x()}}catch(D){O(D?.response?.data?.message||D?.message),b(!1),x()}},M=t=>{h(t),Object.keys(m).length>0&&n("title",m.title[t||"en"])};return u.useEffect(()=>{if(!r){p({}),n("title"),n("productType"),n("couponCode"),n("endTime"),n("discountPercentage"),n("minimumAmount"),g(""),w("title"),w("productType"),w("couponCode"),w("endTime"),w("discountPercentage"),w("minimumAmount"),h(i),n("language",c);return}l&&(async()=>{try{const t=await F.getCouponById(l);t&&(p(t),n("title",t.title[c||"en"]),n("productType",t.productType),n("couponCode",t.couponCode),n("endTime",V(t.endTime).format("YYYY-MM-DD HH:mm")),n("discountPercentage",t.discountType?.value),n("minimumAmount",t.minimumAmount),y(t.status==="show"),a(t.discountType?.type==="percentage"),g(t.logo))}catch(t){O(t?.response?.data?.message||t?.message)}})()},[l,n,r,w,c,i]),{register:E,handleSubmit:I,onSubmit:k,errors:U,setImageUrl:g,imageUrl:f,published:C,setPublished:y,currency:S,discountType:T,isSubmitting:j,setDiscountType:a,handleSelectLanguage:M}},be=({title:l,handleProcess:r,processOption:x,product:d,handleIsCombination:i})=>e.jsx(e.Fragment,{children:e.jsx("div",{className:`${d?"mb-3 flex flex-wrap justify-end items-center mr-8":"mb-3"}`,children:e.jsxs("div",{className:"flex flex-wrap items-center",children:[d?e.jsx("label",{className:"block text-base font-normal text-orange-500 dark:text-orange-400 mx-4",children:"Does this product have variants?"}):e.jsx("label",{className:"block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-1",children:l}),e.jsx(pe,{onChange:d?i:r,checked:x,className:"react-switch",uncheckedIcon:e.jsx("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%",fontSize:14,color:"white",paddingRight:45,paddingTop:1},children:"Fixed"}),width:125,height:33,handleDiameter:28,offColor:"#E53E3E",onColor:"#2F855A",checkedIcon:e.jsx("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%",fontSize:14,color:"white",paddingLeft:50,paddingTop:1},children:"Percentage"})})]})})}),G=({id:l})=>{const{register:r,handleSubmit:x,onSubmit:d,errors:i,setImageUrl:f,imageUrl:g,published:c,setPublished:h,currency:m,discountType:p,setDiscountType:C,isSubmitting:y,handleSelectLanguage:T}=je(l);return e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"w-full relative  p-6 border-b border-gray-100 bg-gray-50 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-300 ",children:l?e.jsx(_,{register:r,handleSelectLanguage:T,title:o("UpdateCoupon"),description:o("UpdateCouponDescription")}):e.jsx(_,{register:r,handleSelectLanguage:T,title:o("AddCoupon"),description:o("AddCouponDescription")})}),e.jsx(K.Scrollbars,{className:"w-full md:w-7/12 lg:w-8/12 xl:w-8/12 relative dark:bg-gray-700 dark:text-gray-200",children:e.jsxs("form",{onSubmit:x(d),children:[e.jsxs("div",{className:"px-6 pt-8 flex-grow scrollbar-hide w-full max-h-full pb-40",children:[e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(N,{label:o("CouponBannerImage")}),e.jsx("div",{className:"col-span-8 sm:col-span-4",children:e.jsx(de,{imageUrl:g,setImageUrl:f,folder:"coupon",targetWidth:238,targetHeight:238})})]}),e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(N,{label:o("CampaignName")}),e.jsxs("div",{className:"col-span-8 sm:col-span-4",children:[e.jsx(R,{required:!0,register:r,label:"Coupon title",name:"title",type:"text",placeholder:o("CampaignName")}),e.jsx(v,{errorName:i.title})]})]}),e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(N,{label:o("CampaignCode")}),e.jsxs("div",{className:"col-span-8 sm:col-span-4",children:[e.jsx(R,{required:!0,register:r,label:"Coupon Code",name:"couponCode",type:"text",placeholder:o("CampaignCode")}),e.jsx(v,{errorName:i.couponCode})]})]}),e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(N,{label:o("CouponValidityTime")}),e.jsxs("div",{className:"col-span-8 sm:col-span-4",children:[e.jsx(s.Input,{...r("endTime",{required:"Coupon Validation End Time"}),label:"Coupon Validation End Time",name:"endTime",type:"datetime-local",placeholder:o("CouponValidityTime")}),e.jsx(v,{errorName:i.endTime})]})]}),e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(N,{label:o("DiscountType")}),e.jsxs("div",{className:"col-span-8 sm:col-span-4",children:[e.jsx(be,{handleProcess:C,processOption:p}),e.jsx(v,{errorName:i.discountType})]})]}),e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(N,{label:o("Discount")}),e.jsxs("div",{className:"col-span-8 sm:col-span-4",children:[e.jsx(L,{product:!0,required:!0,register:r,maxValue:p?99:1e3,minValue:1,label:"Discount",name:"discountPercentage",type:"number",placeholder:p?"Percentage":"Fixed Amount",currency:p?"%":m}),e.jsx(v,{errorName:i.discountPercentage})]})]}),e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(N,{label:o("MinimumAmount")}),e.jsxs("div",{className:"col-span-8 sm:col-span-4",children:[e.jsx(L,{product:!0,required:!0,register:r,maxValue:2e5,minValue:100,label:"Minimum Amount",name:"minimumAmount",type:"number",placeholder:o("MinimumAmountPlasholder"),currency:m}),e.jsx(v,{errorName:i.minimumAmount})]})]}),e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(N,{label:o("Published")}),e.jsxs("div",{className:"col-span-8 sm:col-span-4",children:[e.jsx(me,{handleProcess:h,processOption:c}),e.jsx(v,{errorName:i.productType})]})]})]}),e.jsx(le,{id:l,title:"Coupon",isSubmitting:y})]})})]})},fe=({isCheck:l,coupons:r,setIsCheck:x})=>{const[d,i]=u.useState([]),{title:f,serviceId:g,handleModalOpen:c,handleUpdate:h}=Y(),{currency:m,showDateFormat:p,globalSetting:C,showingTranslateValue:y}=z(),T=a=>{const{id:j,checked:b}=a.target;x([...l,j]),b||x(l.filter(S=>S!==j))};return u.useEffect(()=>{const a=r?.map(j=>{const b=new Date(j?.updatedAt).toLocaleString("en-US",{timeZone:C?.default_time_zone});return{...j,updatedDate:b}});i(a)},[r,C?.default_time_zone]),e.jsxs(e.Fragment,{children:[l.length<1&&e.jsx(W,{id:g,title:f}),l.length<2&&e.jsx($,{children:e.jsx(G,{id:g})}),e.jsx(s.TableBody,{children:d?.map((a,j)=>e.jsxs(s.TableRow,{children:[e.jsx(s.TableCell,{children:e.jsx(Z,{type:"checkbox",name:a?.title?.en,id:a._id,handleClick:T,isChecked:l?.includes(a._id)})}),e.jsxs(s.TableCell,{children:[e.jsxs("div",{className:"flex items-center",children:[a?.logo?e.jsx(s.Avatar,{className:"hidden p-1 mr-2 md:block bg-gray-50 shadow-none",src:a?.logo,alt:"product"}):e.jsx(s.Avatar,{src:"https://res.cloudinary.com/ahossain/image/upload/v1655097002/placeholder_kvepfp.png",alt:"product"}),e.jsxs("div",{children:[e.jsx("span",{className:"text-sm",children:y(a?.title)})," "]})]})," "]}),e.jsxs(s.TableCell,{children:[" ",e.jsxs("span",{className:"text-sm",children:[" ",a.couponCode]})," "]}),a?.discountType?.type?e.jsxs(s.TableCell,{children:[" ",e.jsxs("span",{className:"text-sm font-semibold",children:[" ",a?.discountType?.type==="percentage"?`${a?.discountType?.value}%`:`${m}${a?.discountType?.value}`]})," "]}):e.jsxs(s.TableCell,{children:[" ",e.jsx("span",{className:"text-sm font-semibold",children:" "})," "]}),e.jsx(s.TableCell,{className:"text-center",children:e.jsx(ne,{id:a._id,status:a.status})}),e.jsx(s.TableCell,{children:e.jsx("span",{className:"text-sm",children:p(a.startTime)})}),e.jsx(s.TableCell,{children:e.jsx("span",{className:"text-sm",children:p(a.endTime)})}),e.jsx(s.TableCell,{className:"align-middle ",children:V().isAfter(V(a.endTime))?e.jsx(s.Badge,{type:"danger",children:"Expired"}):e.jsx(s.Badge,{type:"success",children:"Active"})}),e.jsx(s.TableCell,{children:e.jsx(re,{id:a?._id,isCheck:l,handleUpdate:h,handleModalOpen:c,title:y(a?.title)})})]},j+1))})]})},es=()=>{const{t:l}=J(),{toggleDrawer:r,lang:x}=u.useContext(q),{data:d,loading:i,error:f}=se(F.getAllCoupons),[g,c]=u.useState(!1),[h,m]=u.useState([]),{allId:p,serviceId:C,handleDeleteMany:y,handleUpdateMany:T}=Y(),{filename:a,isDisabled:j,couponRef:b,dataTable:S,serviceData:B,totalResults:E,resultsPerPage:I,handleChangePage:n,handleSelectFile:w,setSearchCoupon:U,handleSubmitCoupon:k,handleUploadMultiple:M,handleRemoveSelectFile:t}=ae(d),D=()=>{c(!g),m(d?.map(A=>A._id)),g&&m([])},P=()=>{U(""),b.current.value=""};return e.jsxs(e.Fragment,{children:[e.jsx(te,{children:l("CouponspageTitle")}),e.jsx(W,{ids:p,setIsCheck:m,title:"Selected Coupon"}),e.jsx(ie,{ids:p,title:"Coupons"}),e.jsx($,{children:e.jsx(G,{id:C})}),e.jsxs(he,{children:[e.jsx(s.Card,{className:"min-w-0 shadow-xs overflow-hidden bg-white dark:bg-gray-800 mb-5",children:e.jsx(s.CardBody,{children:e.jsxs("form",{onSubmit:k,className:"py-3 grid gap-4 lg:gap-6 xl:gap-6  xl:flex",children:[e.jsx("div",{className:"flex justify-start xl:w-1/2  md:w-full",children:e.jsx(ge,{title:"Coupon",exportData:d,filename:a,isDisabled:j,handleSelectFile:w,handleUploadMultiple:M,handleRemoveSelectFile:t})}),e.jsxs("div",{className:"lg:flex  md:flex xl:justify-end xl:w-1/2  md:w-full md:justify-start flex-grow-0",children:[e.jsx("div",{className:"w-full md:w-40 lg:w-40 xl:w-40 mr-3 mb-3 lg:mb-0",children:e.jsxs(s.Button,{disabled:h.length<1,onClick:()=>T(h),className:"w-full rounded-md h-12 btn-gray text-gray-600",children:[e.jsx("span",{className:"mr-2",children:e.jsx(Q,{})}),l("BulkAction")]})}),e.jsx("div",{className:"w-full md:w-32 lg:w-32 xl:w-32 mr-3 mb-3 lg:mb-0",children:e.jsxs(s.Button,{disabled:h.length<1,onClick:()=>y(h),className:"w-full rounded-md h-12 bg-red-500 btn-red",children:[e.jsx("span",{className:"mr-2",children:e.jsx(X,{})}),l("Delete")]})}),e.jsx("div",{className:"w-full md:w-48 lg:w-48 xl:w-48",children:e.jsxs(s.Button,{onClick:r,className:"w-full rounded-md h-12",children:[e.jsx("span",{className:"mr-2",children:e.jsx(ee,{})}),l("AddCouponsBtn")]})})]})]})})}),e.jsx(s.Card,{className:"min-w-0 shadow-xs overflow-hidden bg-white dark:bg-gray-800 mb-5",children:e.jsx(s.CardBody,{children:e.jsxs("form",{onSubmit:k,className:"py-3 grid gap-4 lg:gap-6 xl:gap-6 md:flex xl:flex",children:[e.jsx("div",{className:"flex-grow-0 md:flex-grow lg:flex-grow xl:flex-grow",children:e.jsx(s.Input,{ref:b,type:"search",placeholder:l("SearchCoupon")})}),e.jsxs("div",{className:"flex items-center gap-2 flex-grow-0 md:flex-grow lg:flex-grow xl:flex-grow",children:[e.jsx("div",{className:"w-full mx-1",children:e.jsx(s.Button,{type:"submit",className:"h-12 w-full bg-emerald-700",children:"Filter"})}),e.jsx("div",{className:"w-full mx-1",children:e.jsx(s.Button,{layout:"outline",onClick:P,type:"reset",className:"px-4 md:py-1 py-2 h-12 text-sm dark:bg-gray-700",children:e.jsx("span",{className:"text-black dark:text-gray-200",children:"Reset"})})})]})]})})})]}),i?e.jsx(ue,{row:12,col:8,width:140,height:20}):f?e.jsx("span",{className:"text-center mx-auto text-red-500",children:f}):B?.length!==0?e.jsxs(s.TableContainer,{className:"mb-8",children:[e.jsxs(s.Table,{children:[e.jsx(s.TableHeader,{children:e.jsxs("tr",{children:[e.jsx(s.TableCell,{children:e.jsx(Z,{type:"checkbox",name:"selectAll",id:"selectAll",handleClick:D,isChecked:g})}),e.jsx(s.TableCell,{children:l("CoupTblCampaignsName")}),e.jsx(s.TableCell,{children:l("CoupTblCode")}),e.jsx(s.TableCell,{children:l("Discount")}),e.jsx(s.TableCell,{className:"text-center",children:l("catPublishedTbl")}),e.jsx(s.TableCell,{children:l("CoupTblStartDate")}),e.jsx(s.TableCell,{children:l("CoupTblEndDate")}),e.jsx(s.TableCell,{children:l("CoupTblStatus")}),e.jsx(s.TableCell,{className:"text-right",children:l("CoupTblActions")})]})}),e.jsx(fe,{lang:x,isCheck:h,coupons:S,setIsCheck:m})]}),e.jsx(s.TableFooter,{children:e.jsx(s.Pagination,{totalResults:E,resultsPerPage:I,onChange:n,label:"Table navigation"})})]}):e.jsx(xe,{title:"Sorry, There are no coupons right now."})]})};export{es as default};
