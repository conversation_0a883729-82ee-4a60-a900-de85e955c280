import{u as Fa,r as b,j as e,h as D,k as ae,p as os,S as qa,e as zt,X as _,l as ue}from"./index-BnbL29JP.js";import{z as U,u as Va}from"./Layout-pFzaaQDc.js";import{E as c,u as Ga}from"./index.esm-ClJnGQn6.js";import{s as fe}from"./spinner-CkndCogW.js";import{I as g}from"./InputAreaTwo-0b6oYMpv.js";import{S}from"./SwitchToggle-CGiqq8S_.js";import{U as O}from"./Uploader-COz_-nhd.js";import{j as Pa}from"./index-CJFAfqOd.js";import{u as Ya}from"./useDisableForDemo-Bu4HEiKz.js";import{a as ua,n as ha}from"./toast-DZMsp61l.js";import{C as Za}from"./CouponServices-BN-cEYCp.js";import{P as Ja}from"./PageTitle-FuOKSvYQ.js";import{S as Xa}from"./SelectLanguageTwo-KDdaD-gj.js";import{A as he}from"./AnimatedContent-0V4vlNfe.js";import"./iconBase-CKOh_aia.js";import"./index.prod-BbOPZ2BB.js";import"./_commonjs-dynamic-modules-BJDvAndU.js";import"./index-C148XJoK.js";const el=()=>{const{search:i}=Fa();return b.useMemo(()=>new URLSearchParams(i),[i])},P=({register:i,name:t,label:n,placeholder:a,required:o,type:h,value:x})=>e.jsx(e.Fragment,{children:e.jsx(D.Textarea,{...i(`${t}`,{required:o?`${n} is required!`:!1}),type:h,placeholder:a,name:t,value:x,rows:"4",spellCheck:"false"})}),sl=({isSave:i,errors:t,register:n,setFaqStatus:a,faqStatus:o,setFaqHeaderBg:h,faqHeaderBg:x,setFaqLeftColImage:j,faqLeftColImage:u,setFaqLeftColStatus:N,faqLeftColStatus:k,setFaqRightColStatus:s,faqRightColStatus:w,isSubmitting:C})=>{const{t:y}=ae();return e.jsx(e.Fragment,{children:e.jsxs("div",{className:"col-span-12 md:col-span-12 lg:col-span-12 mr-3 ",children:[e.jsx("div",{className:"sticky top-0 z-20 flex justify-end",children:C?e.jsxs(D.Button,{disabled:!0,type:"button",className:"h-10 px-6",children:[e.jsx("img",{src:fe,alt:"Loading",width:20,height:10})," ",e.jsxs("span",{className:"font-serif ml-2 font-light",children:[" ",y("Processing")]})]}):e.jsxs(D.Button,{type:"submit",className:"h-10 px-6 ",children:[" ",y(i?"SaveBtn":"UpdateBtn")]})}),e.jsxs("div",{className:"inline-flex text-lg text-gray-800 font-semibold dark:text-gray-400 md:mb-3 mb-1",children:[e.jsx(U,{className:"mt-1 mr-2"}),y("FAQSetting")]}),e.jsx("hr",{className:"md:mb-10 mb-3"}),e.jsxs("div",{className:"xl:px-10 flex-grow scrollbar-hide w-full max-h-full pb-0",children:[e.jsx("div",{className:"inline-flex md:text-md text-sm mb-3 text-gray-500 dark:text-gray-400",children:e.jsx("strong",{children:y("FAQPageHeader")})}),e.jsx("hr",{className:"md:mb-12 mb-3"}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:y("EnableThisBlock")}),e.jsx("div",{className:"sm:col-span-4",children:e.jsx(S,{title:"",handleProcess:a,processOption:o,name:o})})]}),e.jsxs("div",{className:"mb-height-0",style:{height:o?"auto":0,transition:"all 0.5s",visibility:o?"visible":"hidden",opacity:o?"1":"0"},children:[e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:y("PageHeaderBg")}),e.jsx("div",{className:"sm:col-span-4",children:e.jsx(O,{imageUrl:x,setImageUrl:h})})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:y("PageTitle")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:n,label:"Page Title",name:"faq_page_title",type:"text",placeholder:y("PageTitle")}),e.jsx(c,{errorName:t.faq_page_title})]})]})]}),e.jsx("div",{className:"inline-flex md:text-md text-sm mb-3 text-gray-500 dark:text-gray-400",children:e.jsx("strong",{children:y("FaqLeftCol")})}),e.jsx("hr",{className:"md:mb-12 mb-3"}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:y("EnableThisBlock")}),e.jsx("div",{className:"sm:col-span-4",children:e.jsx(S,{title:"",handleProcess:N,processOption:k,name:k})})]}),e.jsxs("div",{style:{height:k?"auto":0,transition:"all 0.5s",visibility:k?"visible":"hidden",opacity:k?"1":"0"},className:"mb-height-0 grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3 relative",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:y("LeftImage")}),e.jsx("div",{className:"sm:col-span-4",children:e.jsx(O,{imageUrl:u,setImageUrl:j})})]}),e.jsx("div",{className:"inline-flex md:text-md text-sm mb-3 text-gray-500 dark:text-gray-400",children:e.jsx("strong",{children:y("FAQS")})}),e.jsx("hr",{className:"md:mb-12 mb-3"}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:y("EnableThisBlock")}),e.jsx("div",{className:"sm:col-span-4",children:e.jsx(S,{title:"",handleProcess:s,processOption:w,name:w})})]}),e.jsxs("div",{style:{height:w?"auto":0,transition:"all 0.5s",visibility:w?"visible":"hidden",opacity:w?"1":"0"},children:[e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3 relative",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:y("FaqTitleOne")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:n,label:"FAQ Title",name:"faq_title_one",type:"text",placeholder:"FAQ Title"}),e.jsx(c,{errorName:t.faq_title_one})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:y("FaqDescriptionOne")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(P,{register:n,label:"Faq Description",name:"faq_description_one",type:"text",placeholder:y("FaqDescriptionOne")}),e.jsx(c,{errorName:t.faq_description_one})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:y("FaqTitleTwo")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:n,label:"FAQ Title",name:"faq_title_two",type:"text",placeholder:y("FaqTitleTwo")}),e.jsx(c,{errorName:t.faq_title_two})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:y("FaqDescriptionTwo")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(P,{register:n,label:"Faq Description",name:"faq_description_two",type:"text",placeholder:y("FaqDescriptionTwo")}),e.jsx(c,{errorName:t.faq_description_two})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:y("FaqTitleThree")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:n,label:"FAQ Title",name:"faq_title_three",type:"text",placeholder:y("FaqTitleThree")}),e.jsx(c,{errorName:t.faq_title_three})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:y("FaqDescriptionThree")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(P,{register:n,label:"Faq Description",name:"faq_description_three",type:"text",placeholder:y("FaqDescriptionThree")}),e.jsx(c,{errorName:t.faq_description_three})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:y("FaqTitleFour")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:n,label:"FAQ Title",name:"faq_title_four",type:"text",placeholder:y("FaqTitleFour")}),e.jsx(c,{errorName:t.faq_title_four})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:y("FaqDescriptionFour")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(P,{register:n,label:"Faq Description",name:"faq_description_four",type:"text",placeholder:y("FaqDescriptionFour")}),e.jsx(c,{errorName:t.faq_description_four})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:y("FaqTitleFive")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:n,label:"FAQ Title",name:"faq_title_five",type:"text",placeholder:y("FaqTitleFive")}),e.jsx(c,{errorName:t.faq_title_five})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:y("FaqDescriptionFive")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(P,{register:n,label:"Faq Description",name:"faq_description_five",type:"text",placeholder:y("FaqDescriptionFive")}),e.jsx(c,{errorName:t.faq_description_five})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:y("FaqTitleSix")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:n,label:"FAQ Title",name:"faq_title_six",type:"text",placeholder:y("FaqTitleSix")}),e.jsx(c,{errorName:t.faq_title_six})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:y("FaqDescriptionSix")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(P,{register:n,label:"Faq Description",name:"faq_description_six",type:"text",placeholder:y("FaqDescriptionSix")}),e.jsx(c,{errorName:t.faq_description_six})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:y("FaqTitleSeven")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:n,label:"FAQ Title",name:"faq_title_seven",type:"text",placeholder:y("FaqTitleSeven")}),e.jsx(c,{errorName:t.faq_title_seven})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:y("FaqDescriptionSeven")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(P,{register:n,label:"Faq Description",name:"faq_description_seven",type:"text",placeholder:y("FaqDescriptionSeven")}),e.jsx(c,{errorName:t.faq_description_seven})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:y("FaqTitleEight")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:n,label:"FAQ Title",name:"faq_title_eight",type:"text",placeholder:y("FaqTitleEight")}),e.jsx(c,{errorName:t.faq_title_eight})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:y("FaqDescriptionEight")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(P,{register:n,label:"Faq Description",name:"faq_description_eight",type:"text",placeholder:y("FaqDescriptionEight")}),e.jsx(c,{errorName:t.faq_description_eight})]})]})]})]})]})})},tl=({errors:i,isSave:t,coupons:n,register:a,setOffersPageHeader:o,offersPageHeader:h,setOffersHeaderBg:x,offersHeaderBg:j,couponList1:u,setCouponList1:N,isSubmitting:k})=>{const{mode:s}=b.useContext(D.WindmillContext),{t:w}=ae();return e.jsx(e.Fragment,{children:e.jsxs("div",{className:"col-span-12 md:col-span-12 lg:col-span-12 pr-4",children:[e.jsx("div",{className:"sticky top-0 z-20 flex justify-end",children:k?e.jsxs(D.Button,{disabled:!0,type:"button",className:"h-10 px-6",children:[e.jsx("img",{src:fe,alt:"Loading",width:20,height:10})," ",e.jsxs("span",{className:"font-serif ml-2 font-light",children:[" ",w("Processing")]})]}):e.jsxs(D.Button,{type:"submit",className:"h-10 px-6 ",children:[" ",w(t?"SaveBtn":"UpdateBtn")]})}),e.jsxs("div",{className:"inline-flex md:text-lg text-md text-gray-800 font-semibold dark:text-gray-400 md:mb-3 mb-1",children:[e.jsx(U,{className:"mt-1 mr-2"}),w("Offers")]}),e.jsx("hr",{className:"md:mb-10 mb-3"}),e.jsxs("div",{className:"xl:px-10 flex-grow scrollbar-hide w-full max-h-full pb-0",children:[e.jsx("div",{className:"inline-flex md:text-md text-sm mb-3 text-gray-500 dark:text-gray-400",children:e.jsx("strong",{children:w("PageHeader")})}),e.jsx("hr",{className:"md:mb-12 mb-3"}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:w("EnableThisBlock")}),e.jsx("div",{className:"sm:col-span-4",children:e.jsx(S,{title:"",handleProcess:o,processOption:h,name:h})})]}),e.jsxs("div",{className:"mb-height-0",style:{height:h?"auto":0,transition:"all 0.5s",visibility:h?"visible":"hidden",opacity:h?"1":"0"},children:[e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:w("PageHeaderBg")}),e.jsx("div",{className:"sm:col-span-4",children:e.jsx(O,{imageUrl:j,setImageUrl:x})})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:w("PageTitle")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:a,label:"Page Title",name:"offers_page_title",type:"text",placeholder:w("PageTitle")}),e.jsx(c,{errorName:i.offers_page_title})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3 relative",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:w("SuperDiscountActiveCouponCode")}),e.jsx("div",{className:"sm:col-span-4",children:e.jsx(Pa,{options:n,value:u,className:s,onChange:C=>N(C),labelledBy:"Select Coupon"})})]})]})]})]})})};function Zt(i){return t=>!!t.type&&t.type.tabsRole===i}const Mt=Zt("Tab"),Jt=Zt("TabList"),Xt=Zt("TabPanel");function al(i){return Mt(i)||Jt(i)||Xt(i)}function Vt(i,t){return b.Children.map(i,n=>n===null?null:al(n)?t(n):n.props&&n.props.children&&typeof n.props.children=="object"?b.cloneElement(n,{...n.props,children:Vt(n.props.children,t)}):n)}function Oa(i,t){return b.Children.forEach(i,n=>{n!==null&&(Mt(n)||Xt(n)?t(n):n.props&&n.props.children&&typeof n.props.children=="object"&&(Jt(n)&&t(n),Oa(n.props.children,t)))})}function Ba(i){var t,n,a="";if(typeof i=="string"||typeof i=="number")a+=i;else if(typeof i=="object")if(Array.isArray(i))for(t=0;t<i.length;t++)i[t]&&(n=Ba(i[t]))&&(a&&(a+=" "),a+=n);else for(t in i)i[t]&&(a&&(a+=" "),a+=t);return a}function Ht(){for(var i,t,n=0,a="";n<arguments.length;)(i=arguments[n++])&&(t=Ba(i))&&(a&&(a+=" "),a+=t);return a}function Ia(i){let t=0;return Oa(i,n=>{Mt(n)&&t++}),t}function La(i){return i&&"getAttribute"in i}function fa(i){return La(i)&&i.getAttribute("data-rttab")}function Pe(i){return La(i)&&i.getAttribute("aria-disabled")==="true"}let At;function ll(i){const t=i||(typeof window<"u"?window:void 0);try{At=!!(typeof t<"u"&&t.document&&t.document.activeElement)}catch{At=!1}}const ol={className:"react-tabs",focus:!1},Da=i=>{let t=b.useRef([]),n=b.useRef([]);const a=b.useRef();function o(v,F){if(v<0||v>=N())return;const{onSelect:I,selectedIndex:f}=i;I(v,f,F)}function h(v){const F=N();for(let I=v+1;I<F;I++)if(!Pe(k(I)))return I;for(let I=0;I<v;I++)if(!Pe(k(I)))return I;return v}function x(v){let F=v;for(;F--;)if(!Pe(k(F)))return F;for(F=N();F-- >v;)if(!Pe(k(F)))return F;return v}function j(){const v=N();for(let F=0;F<v;F++)if(!Pe(k(F)))return F;return null}function u(){let v=N();for(;v--;)if(!Pe(k(v)))return v;return null}function N(){const{children:v}=i;return Ia(v)}function k(v){return t.current[`tabs-${v}`]}function s(){let v=0;const{children:F,disabledTabClassName:I,focus:f,forceRenderTabPanel:E,selectedIndex:A,selectedTabClassName:$,selectedTabPanelClassName:je,environment:Z}=i;n.current=n.current||[];let H=n.current.length-N();const W=b.useId();for(;H++<0;)n.current.push(`${W}${n.current.length}`);return Vt(F,V=>{let K=V;if(Jt(V)){let R=0,J=!1;At==null&&ll(Z);const ye=Z||(typeof window<"u"?window:void 0);At&&ye&&(J=os.Children.toArray(V.props.children).filter(Mt).some((X,le)=>ye.document.activeElement===k(le))),K=b.cloneElement(V,{children:Vt(V.props.children,X=>{const le=`tabs-${R}`,ee=A===R,oe={tabRef:te=>{t.current[le]=te},id:n.current[R],selected:ee,focus:ee&&(f||J)};return $&&(oe.selectedClassName=$),I&&(oe.disabledClassName=I),R++,b.cloneElement(X,oe)})})}else if(Xt(V)){const R={id:n.current[v],selected:A===v};E&&(R.forceRender=E),je&&(R.selectedClassName=je),v++,K=b.cloneElement(V,R)}return K})}function w(v){const{direction:F,disableUpDownKeys:I,disableLeftRightKeys:f}=i;if(y(v.target)){let{selectedIndex:E}=i,A=!1,$=!1;(v.code==="Space"||v.keyCode===32||v.code==="Enter"||v.keyCode===13)&&(A=!0,$=!1,C(v)),!f&&(v.keyCode===37||v.code==="ArrowLeft")||!I&&(v.keyCode===38||v.code==="ArrowUp")?(F==="rtl"?E=h(E):E=x(E),A=!0,$=!0):!f&&(v.keyCode===39||v.code==="ArrowRight")||!I&&(v.keyCode===40||v.code==="ArrowDown")?(F==="rtl"?E=x(E):E=h(E),A=!0,$=!0):v.keyCode===35||v.code==="End"?(E=u(),A=!0,$=!0):(v.keyCode===36||v.code==="Home")&&(E=j(),A=!0,$=!0),A&&v.preventDefault(),$&&o(E,v)}}function C(v){let F=v.target;do if(y(F)){if(Pe(F))return;const I=[].slice.call(F.parentNode.children).filter(fa).indexOf(F);o(I,v);return}while((F=F.parentNode)!=null)}function y(v){if(!fa(v))return!1;let F=v.parentElement;do{if(F===a.current)return!0;if(F.getAttribute("data-rttabs"))break;F=F.parentElement}while(F);return!1}const{children:B,className:q,disabledTabClassName:M,domRef:L,focus:Q,forceRenderTabPanel:T,onSelect:ve,selectedIndex:Ne,selectedTabClassName:we,selectedTabPanelClassName:Te,environment:Se,disableUpDownKeys:Ce,disableLeftRightKeys:Fe,...z}={...ol,...i};return os.createElement("div",Object.assign({},z,{className:Ht(q),onClick:C,onKeyDown:w,ref:v=>{a.current=v,L&&L(v)},"data-rttabs":!0}),s())};Da.propTypes={};const il=0,Ut=1,nl={defaultFocus:!1,focusTabOnClick:!0,forceRenderTabPanel:!1,selectedIndex:null,defaultIndex:null,environment:null,disableUpDownKeys:!1,disableLeftRightKeys:!1},rl=i=>i.selectedIndex===null?Ut:il,is=i=>{const{children:t,defaultFocus:n,defaultIndex:a,focusTabOnClick:o,onSelect:h,...x}={...nl,...i},[j,u]=b.useState(n),[N]=b.useState(rl(x)),[k,s]=b.useState(N===Ut?a||0:null);if(b.useEffect(()=>{u(!1)},[]),N===Ut){const y=Ia(t);b.useEffect(()=>{if(k!=null){const B=Math.max(0,y-1);s(Math.min(k,B))}},[y])}const w=(y,B,q)=>{typeof h=="function"&&h(y,B,q)===!1||(o&&u(!0),N===Ut&&s(y))};let C={...i,...x};return C.focus=j,C.onSelect=w,k!=null&&(C.selectedIndex=k),delete C.defaultFocus,delete C.defaultIndex,delete C.focusTabOnClick,os.createElement(Da,C,t)};is.propTypes={};is.tabsRole="Tabs";const dl={className:"react-tabs__tab-list"},Rt=i=>{const{children:t,className:n,...a}={...dl,...i};return os.createElement("ul",Object.assign({},a,{className:Ht(n),role:"tablist"}),t)};Rt.tabsRole="TabList";Rt.propTypes={};const $t="react-tabs__tab",ml={className:$t,disabledClassName:`${$t}--disabled`,focus:!1,id:null,selected:!1,selectedClassName:`${$t}--selected`},G=i=>{let t=b.useRef();const{children:n,className:a,disabled:o,disabledClassName:h,focus:x,id:j,selected:u,selectedClassName:N,tabIndex:k,tabRef:s,...w}={...ml,...i};return b.useEffect(()=>{u&&x&&t.current.focus()},[u,x]),os.createElement("li",Object.assign({},w,{className:Ht(a,{[N]:u,[h]:o}),ref:C=>{t.current=C,s&&s(C)},role:"tab",id:`tab${j}`,"aria-selected":u?"true":"false","aria-disabled":o?"true":"false","aria-controls":`panel${j}`,tabIndex:k||(u?"0":null),"data-rttab":!0}),n)};G.propTypes={};G.tabsRole="Tab";const ja="react-tabs__tab-panel",cl={className:ja,forceRender:!1,selectedClassName:`${ja}--selected`},Y=i=>{const{children:t,className:n,forceRender:a,id:o,selected:h,selectedClassName:x,...j}={...cl,...i};return os.createElement("div",Object.assign({},j,{className:Ht(n,{[x]:h}),role:"tabpanel",id:`panel${o}`,"aria-labelledby":`tab${o}`}),a||h?t:null)};Y.tabsRole="TabPanel";Y.propTypes={};const xl=({isSave:i,register:t,errors:n,setAboutHeaderBg:a,aboutHeaderBg:o,setAboutPageHeader:h,aboutPageHeader:x,setAboutTopContentLeft:j,aboutTopContentLeft:u,setAboutTopContentRight:N,aboutTopContentRight:k,setAboutTopContentRightImage:s,aboutTopContentRightImage:w,setAboutMiddleContentSection:C,aboutMiddleContentSection:y,setAboutMiddleContentImage:B,aboutMiddleContentImage:q,setOurFounderSection:M,ourFounderSection:L,setOurFounderOneImage:Q,ourFounderOneImage:T,setOurFounderTwoImage:ve,ourFounderTwoImage:Ne,setOurFounderThreeImage:we,ourFounderThreeImage:Te,setOurFounderFourImage:Se,ourFounderFourImage:Ce,setOurFounderFiveImage:Fe,ourFounderFiveImage:z,setOurFounderSixImage:v,ourFounderSixImage:F,isSubmitting:I})=>{const{t:f}=ae();return e.jsx(e.Fragment,{children:e.jsx("div",{className:"grid grid-cols-12 font-sans pr-4",children:e.jsxs("div",{className:"col-span-12 md:col-span-12 lg:col-span-12",children:[e.jsx("div",{className:"sticky top-0 z-20 flex justify-end",children:I?e.jsxs(D.Button,{disabled:!0,type:"button",className:"h-10 px-6",children:[e.jsx("img",{src:fe,alt:"Loading",width:20,height:10})," ",e.jsxs("span",{className:"font-serif ml-2 font-light",children:[" ",f("Processing")]})]}):e.jsxs(D.Button,{type:"submit",className:"h-10 px-6 ",children:[" ",f(i?"SaveBtn":"UpdateBtn")]})}),e.jsxs("div",{className:"inline-flex md:text-lg text-base text-gray-800 font-semibold dark:text-gray-400 md:mb-3 mb-1",children:[e.jsx(U,{className:"mt-1 mr-2"}),f("AboutUs")]}),e.jsx("hr",{className:"md:mb-12 mb-3"}),e.jsxs("div",{className:"xl:px-10 flex-grow scrollbar-hide w-full max-h-full",children:[e.jsx("div",{className:"inline-flex md:text-base text-sm mb-3 text-gray-500 dark:text-gray-400",children:e.jsx("strong",{children:f("PageHeader")})}),e.jsx("hr",{className:"md:mb-12 mb-3"}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:f("EnableThisBlock")}),e.jsx("div",{className:"sm:col-span-4",children:e.jsx(S,{title:"",handleProcess:h,processOption:x,name:x})})]}),e.jsxs("div",{className:"mb-height-0",style:{height:x?"auto":0,transition:"all 0.5s",visibility:x?"visible":"hidden",opacity:x?"1":"0"},children:[e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3 relative",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:f("PageHeaderBg")}),e.jsx("div",{className:"sm:col-span-4",children:e.jsx(O,{imageUrl:o,setImageUrl:a})})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3 relative",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:f("PageTitle")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:t,label:"Page Title",name:"about_page_title",type:"text",placeholder:f("PageTitle")}),e.jsx(c,{errorName:n.about_page_title})]})]})]}),e.jsx("div",{className:"inline-flex md:text-base text-sm mb-3 mt-5 text-gray-500 dark:text-gray-400",children:e.jsx("strong",{children:f("AboutPageTopContentLeft")})}),e.jsx("hr",{className:"md:mb-12 mb-3"}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:f("EnableThisBlock")}),e.jsx("div",{className:"sm:col-span-4",children:e.jsx(S,{title:"",handleProcess:j,processOption:u,name:u})})]}),e.jsxs("div",{className:"mb-height-0",style:{height:u?"auto":0,transition:"all 0.5s",visibility:u?"visible":"hidden",opacity:u?"1":"0"},children:[e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3 relative",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:f("TopTitle")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:t,label:"Top Title",name:"about_page_Top_title",type:"text",placeholder:f("TopTitle")}),e.jsx(c,{errorName:n.about_page_Top_title_left})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3 relative",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:f("TopDescription")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(P,{required:!0,register:t,label:"About Us Top Description",name:"about_us_top_description",type:"text",placeholder:"About Us Top Description"}),e.jsx(c,{errorName:n.name="about_us_top_description"})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3 relative",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:f("BoxOneTitle")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:t,label:"Top Title",name:"about_page_Top_left_box_one_title",type:"text",placeholder:f("BoxOneTitle")}),e.jsx(c,{errorName:n.about_page_Top_left_box_one_title})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:f("BoxOneSubtitle")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:t,label:"Top Title",name:"about_page_Top_left_box_one_subtitle",type:"text",placeholder:f("BoxOneSubtitle")}),e.jsx(c,{errorName:n.about_page_Top_left_box_one_subtitle})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:f("BoxOneDescription")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(P,{required:!0,register:t,label:"About Us Top Box One Description",name:"about_us_top_box_one_description",type:"text",placeholder:f("BoxOneDescription")}),e.jsx(c,{errorName:n.name="about_us_top_box_one_description"})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3 relative",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:f("BoxTwoTitle")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:t,label:"Top Title",name:"about_page_Top_left_box_two_title",type:"text",placeholder:f("BoxTwoTitle")}),e.jsx(c,{errorName:n.name="about_page_Top_left_box_two_title"})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:f("BoxTwoSubtitle")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:t,label:"Top Title",name:"about_page_Top_left_box_two_subtitle",type:"text",placeholder:f("BoxTwoSubtitle")}),e.jsx(c,{errorName:n.about_page_Top_left_box_two_subtitle})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:f("BoxTwoDescription")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(P,{required:!0,register:t,label:"About Us Top Box Two Description",name:"about_us_top_box_two_description",type:"text",placeholder:f("BoxTwoDescription")}),e.jsx(c,{errorName:n.name="about_us_top_box_two_description"})]})]})]}),e.jsx("div",{className:"inline-flex md:text-base text-sm mb-3 md:mt-5 text-gray-500 dark:text-gray-400 ",children:e.jsx("strong",{children:f("PageTopContentRight")})}),e.jsx("hr",{className:"md:mb-12 mb-3"}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:f("EnableThisBlock")}),e.jsx("div",{className:"sm:col-span-4",children:e.jsx(S,{title:"",handleProcess:N,processOption:k,name:k})})]}),e.jsxs("div",{style:{height:k?"auto":0,transition:"all 0.5s",visibility:k?"visible":"hidden",opacity:k?"1":"0"},className:"mb-height-0 grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3 relative",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:f("TopContentRightImage")}),e.jsx("div",{className:"sm:col-span-4",children:e.jsx(O,{imageUrl:w,setImageUrl:s,targetWidth:1050,targetHeight:805})})]}),e.jsx("div",{className:"inline-flex md:text-base text-sm mb-3 md:mt-5 text-gray-500 dark:text-gray-400 relative ",children:e.jsx("strong",{children:f("MiddleContentSection")})}),e.jsx("hr",{className:"md:mb-12 mb-3"}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:f("EnableThisBlock")}),e.jsx("div",{className:"sm:col-span-4",children:e.jsx(S,{title:"",handleProcess:C,processOption:y,name:y})})]}),e.jsxs("div",{className:"mb-height-0",style:{height:y?"auto":0,transition:"all 0.5s",visibility:y?"visible":"hidden",opacity:y?"1":"0"},children:[e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:f("MiddleDescriptionOne")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(P,{required:!0,register:t,label:"About Us Middle Description",name:"about_us_middle_description_one",type:"text",placeholder:f("MiddleDescriptionOne")}),e.jsx(c,{errorName:n.name="about_us_middle_description_one"})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:f("MiddleDescriptionTwo")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(P,{required:!0,register:t,label:"About Us Middle Description",name:"about_us_middle_description_two",type:"text",placeholder:f("MiddleDescriptionTwo")}),e.jsx(c,{errorName:n.name="about_us_middle_description_two"})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:f("MiddleContentImage")}),e.jsx("div",{className:"sm:col-span-4",children:e.jsx(O,{imageUrl:q,setImageUrl:B,targetWidth:1420,targetHeight:425})})]})]}),e.jsx("div",{className:"inline-flex md:text-base text-sm mb-3 md:mt-5 text-gray-500 dark:text-gray-400 ",children:e.jsx("strong",{children:f("OurFounder")})}),e.jsx("hr",{className:"md:mb-12 mb-3"}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:f("EnableThisBlock")}),e.jsx("div",{className:"sm:col-span-4",children:e.jsx(S,{title:"",handleProcess:M,processOption:L,name:L})})]}),e.jsxs("div",{className:"mb-height-0",style:{height:L?"auto":0,transition:"all 0.5s",visibility:L?"visible":"hidden",opacity:L?"1":"0"},children:[e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3 relative",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:f("OurFounderTitle")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:t,label:"Title",name:"about_page_ourfounder_title",type:"text",placeholder:f("OurFounderTitle")}),e.jsx(c,{errorName:n.about_page_ourfounder_title})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3 relative",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:f("OurFounderDescription")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(P,{required:!0,register:t,label:"Our Founder Description",name:"about_us_ourfounder_description",type:"text",placeholder:f("OurFounderDescription")}),e.jsx(c,{errorName:n.name="about_us_ourfounder_description"})]})]}),e.jsx(is,{children:e.jsxs(is,{children:[e.jsxs(Rt,{children:[e.jsxs(G,{children:[f("OurTeam")," 1"]}),e.jsxs(G,{children:[f("OurTeam")," 2"]}),e.jsxs(G,{children:[f("OurTeam")," 3"]}),e.jsxs(G,{children:[f("OurTeam")," 4"]}),e.jsxs(G,{children:[f("OurTeam")," 5"]}),e.jsxs(G,{children:[f("OurTeam")," 6"]})]}),e.jsxs(Y,{className:"mt-10",children:[e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3 relative",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:f("OurFounderOneImage")}),e.jsx("div",{className:"sm:col-span-4",children:e.jsx(O,{imageUrl:T,setImageUrl:Q,targetWidth:600,targetHeight:600})})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:f("OurFounderOneTitle")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:t,label:"Title",name:"about_page_ourfounder_one_title",type:"text",placeholder:f("OurFounderOneTitle")}),e.jsx(c,{errorName:n.about_page_ourfounder_one_title})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:f("OurFounderOneSubTitle")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:t,label:"Sub Title",name:"about_page_ourfounder_one_sub_title",type:"text",placeholder:f("OurFounderOneSubTitle")}),e.jsx(c,{errorName:n.about_page_ourfounder_one_sub_title})]})]})]}),e.jsxs(Y,{children:[e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:f("OurFounderTwoImage")}),e.jsx("div",{className:"sm:col-span-4",children:e.jsx(O,{imageUrl:Ne,setImageUrl:ve,targetWidth:600,targetHeight:600})})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:f("OurFounderTwoTitle")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:t,label:"Title",name:"about_page_ourfounder_two_title",type:"text",placeholder:f("OurFounderTwoTitle")}),e.jsx(c,{errorName:n.about_page_ourfounder_two_title})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:f("OurFounderTwoSubTitle")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:t,label:"Sub Title",name:"about_page_ourfounder_two_sub_title",type:"text",placeholder:f("OurFounderTwoSubTitle")}),e.jsx(c,{errorName:n.about_page_ourfounder_two_sub_title})]})]})]}),e.jsxs(Y,{children:[e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:f("OurFounderThreeImage")}),e.jsx("div",{className:"sm:col-span-4",children:e.jsx(O,{imageUrl:Te,setImageUrl:we,targetWidth:600,targetHeight:600})})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:f("OurFounderThreeTitle")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:t,label:"Title",name:"about_page_ourfounder_three_title",type:"text",placeholder:f("OurFounderThreeTitle")}),e.jsx(c,{errorName:n.about_page_ourfounder_three_title})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:f("OurFounderThreeSubTitle")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:t,label:"Sub Title",name:"about_page_ourfounder_three_sub_title",type:"text",placeholder:f("OurFounderThreeSubTitle")}),e.jsx(c,{errorName:n.about_page_ourfounder_three_sub_title})]})]})]}),e.jsxs(Y,{children:[e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:f("OurFounderFourImage")}),e.jsx("div",{className:"sm:col-span-4",children:e.jsx(O,{imageUrl:Ce,setImageUrl:Se,targetWidth:600,targetHeight:600})})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:f("OurFounderFourTitle")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:t,label:"Title",name:"about_page_ourfounder_four_title",type:"text",placeholder:f("OurFounderFourTitle")}),e.jsx(c,{errorName:n.about_page_ourfounder_four_title})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:f("OurFounderFourSubTitle")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:t,label:"Sub Title",name:"about_page_ourfounder_four_sub_title",type:"text",placeholder:f("OurFounderFourSubTitle")}),e.jsx(c,{errorName:n.about_page_ourfounder_four_sub_title})]})]})]}),e.jsxs(Y,{children:[e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:f("OurFounderFiveImage")}),e.jsx("div",{className:"sm:col-span-4",children:e.jsx(O,{imageUrl:z,setImageUrl:Fe,targetWidth:600,targetHeight:600})})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:f("OurFounderFiveTitle")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:t,label:"Title",name:"about_page_ourfounder_five_title",type:"text",placeholder:f("OurFounderFiveTitle")}),e.jsx(c,{errorName:n.about_page_ourfounder_five_title})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:f("OurFounderFiveSubTitle")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:t,label:"Sub Title",name:"about_page_ourfounder_five_sub_title",type:"text",placeholder:f("OurFounderFiveSubTitle")}),e.jsx(c,{errorName:n.about_page_ourfounder_five_sub_title})]})]})]}),e.jsxs(Y,{children:[e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 ",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:f("OurFounderSixImage")}),e.jsx("div",{className:"sm:col-span-4",children:e.jsx(O,{imageUrl:F,setImageUrl:v,targetWidth:600,targetHeight:600})})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:f("OurFounderSixTitle")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:t,label:"Title",name:"about_page_ourfounder_six_title",type:"text",placeholder:f("OurFounderSixTitle")}),e.jsx(c,{errorName:n.about_page_ourfounder_six_title})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:f("OurFounderSixSubTitle")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:t,label:"Sub Title",name:"about_page_ourfounder_six_sub_title",type:"text",placeholder:f("OurFounderSixSubTitle")}),e.jsx(c,{errorName:n.about_page_ourfounder_six_sub_title})]})]})]})]})})]})]})]})})})},pl=({isSave:i,errors:t,register:n,setContactPageHeader:a,contactPageHeader:o,setContactHeaderBg:h,contactHeaderBg:x,setEmailUsBox:j,emailUsBox:u,setCallUsBox:N,callUsBox:k,setAddressBox:s,addressBox:w,setContactMidLeftColStatus:C,contactMidLeftColStatus:y,setContactMidLeftColImage:B,contactMidLeftColImage:q,setContactFormStatus:M,contactFormStatus:L,isSubmitting:Q})=>{const{t:T}=ae();return e.jsx(e.Fragment,{children:e.jsxs("div",{className:"col-span-12 md:col-span-12 lg:col-span-12 pr-3",children:[e.jsx("div",{className:"sticky top-0 z-20 flex justify-end",children:Q?e.jsxs(D.Button,{disabled:!0,type:"button",className:"h-10 px-6",children:[e.jsx("img",{src:fe,alt:"Loading",width:20,height:10})," ",e.jsxs("span",{className:"font-serif ml-2 font-light",children:[" ",T("Processing")]})]}):e.jsxs(D.Button,{type:"submit",className:"h-10 px-6 ",children:[" ",T(i?"SaveBtn":"UpdateBtn")]})}),e.jsxs("div",{className:"inline-flex md:text-lg text-md text-gray-800 font-semibold dark:text-gray-400 md:mb-3 mb-1",children:[e.jsx(U,{className:"mt-1 mr-2"}),T("ContactUs")]}),e.jsx("hr",{className:"md:mb-12 mb-3"}),e.jsxs("div",{className:"xl:px-10 flex-grow scrollbar-hide w-full max-h-full",children:[e.jsx("div",{className:"inline-flex md:text-md text-sm mb-3 text-gray-500 dark:text-gray-400",children:e.jsx("strong",{children:T("PageHeader")})}),e.jsx("hr",{className:"md:mb-12 mb-3"}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:T("EnableThisBlock")}),e.jsx("div",{className:" sm:col-span-4",children:e.jsx(S,{title:"",handleProcess:a,processOption:o,name:o})})]}),e.jsxs("div",{className:"mb-height-0",style:{height:o?"auto":0,transition:"all 0.5s",visibility:o?"visible":"hidden",opacity:o?"1":"0"},children:[e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:T("PageHeaderBg")}),e.jsx("div",{className:" sm:col-span-4",children:e.jsx(O,{imageUrl:x,setImageUrl:h})})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:T("PageTitle")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:n,label:"Page Title",name:"contact_page_title",type:"text",placeholder:T("PageTitle")}),e.jsx(c,{errorName:t.contact_page_title})]})]})]}),e.jsx("div",{className:"inline-flex md:text-md text-sm mb-3 text-gray-500 dark:text-gray-400 relative",children:e.jsx("strong",{children:T("EmailUs")})}),e.jsx("hr",{className:"md:mb-12 mb-3"}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:T("EnableThisBlock")}),e.jsx("div",{className:" sm:col-span-4",children:e.jsx(S,{title:"",handleProcess:j,processOption:u,name:u})})]}),e.jsxs("div",{className:"mb-height-0",style:{height:u?"auto":0,transition:"all 0.5s",visibility:u?"visible":"hidden",opacity:u?"1":"0"},children:[e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:T("EboxTitle")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:n,label:"Title",name:"email_box_title",type:"text",placeholder:T("EboxTitle")}),e.jsx(c,{errorName:t.email_box_title})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:T("EboxEmail")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:n,label:"Title",name:"email_box_email",type:"text",placeholder:T("EboxEmail")}),e.jsx(c,{errorName:t.email_box_email})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:T("Eboxtext")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:n,label:"Title",name:"email_box_text",type:"text",placeholder:T("Eboxtext")}),e.jsx(c,{errorName:t.email_box_text})]})]})]}),e.jsx("div",{className:"inline-flex md:text-md text-sm mb-3 text-gray-500 dark:text-gray-400 relative",children:e.jsx("strong",{children:T("CallUs")})}),e.jsx("hr",{className:"md:mb-12 mb-3"}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:T("EnableThisBlock")}),e.jsx("div",{className:" sm:col-span-4",children:e.jsx(S,{title:"",handleProcess:N,processOption:k,name:k})})]}),e.jsxs("div",{className:"mb-height-0",style:{height:k?"auto":0,transition:"all 0.5s",visibility:k?"visible":"hidden",opacity:k?"1":"0"},children:[e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:T("CallusboxTitle")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:n,label:"Title",name:"callUs_box_title",type:"text",placeholder:T("CallusboxTitle")}),e.jsx(c,{errorName:t.callUs_box_title})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:T("CallUsboxPhone")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:n,label:"Phone",name:"callUs_box_phone",type:"text",placeholder:T("CallUsboxPhone")}),e.jsx(c,{errorName:t.callUs_box_phone})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:T("CallUsboxText")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:n,label:"Title",name:"callUs_box_text",type:"text",placeholder:T("CallUsboxText")}),e.jsx(c,{errorName:t.callUs_box_text})]})]})]}),e.jsx("div",{className:"inline-flex md:text-md text-sm mb-3 text-gray-500 dark:text-gray-400 relative",children:e.jsx("strong",{children:T("Address")})}),e.jsx("hr",{className:"md:mb-12 mb-3"}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:T("EnableThisBlock")}),e.jsx("div",{className:" sm:col-span-4",children:e.jsx(S,{title:"",handleProcess:s,processOption:w,name:w})})]}),e.jsxs("div",{className:"mb-height-0",style:{height:w?"auto":0,transition:"all 0.5s",visibility:w?"visible":"hidden",opacity:w?"1":"0"},children:[e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:T("AddressboxTitle")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:n,label:"Title",name:"address_box_title",type:"text",placeholder:T("AddressboxTitle")}),e.jsx(c,{errorName:t.address_box_title})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:T("AddressboxAddressOne")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:n,label:"Title",name:"address_box_address_one",type:"text",placeholder:T("AddressboxAddressOne")}),e.jsx(c,{errorName:t.address_box_address_one})]})]})]}),e.jsx("div",{className:"inline-flex md:text-md text-sm mb-3 text-gray-500 dark:text-gray-400",children:e.jsx("strong",{children:T("MidLeftCol")})}),e.jsx("hr",{className:"md:mb-12 mb-3"}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:T("EnableThisBlock")}),e.jsx("div",{className:" sm:col-span-4",children:e.jsx(S,{title:"",handleProcess:C,processOption:y,name:y})})]}),e.jsx("div",{className:"mb-height-0",style:{height:y?"auto":0,transition:"all 0.5s",visibility:y?"visible":"hidden",opacity:y?"1":"0"},children:e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3 relative",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:T("MidLeftImage")}),e.jsx("div",{className:" sm:col-span-4",children:e.jsx(O,{imageUrl:q,setImageUrl:B,targetWidth:874,targetHeight:877})})]})}),e.jsx("div",{className:"inline-flex md:text-md text-sm mb-3 text-gray-500 dark:text-gray-400 relative",children:e.jsx("strong",{children:T("ContactForm")})}),e.jsx("hr",{className:"md:mb-12 mb-3"}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:T("EnableThisBlock")}),e.jsx("div",{className:" sm:col-span-4",children:e.jsx(S,{title:"",handleProcess:M,processOption:L,name:L})})]}),e.jsxs("div",{className:"mb-height-0",style:{height:L?"auto":0,transition:"all 0.5s",visibility:L?"visible":"hidden",opacity:L?"1":"0"},children:[e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:T("ContactFormTitle")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:n,label:"Title",name:"contact_form_title",type:"text",placeholder:T("ContactFormTitle")}),e.jsx(c,{errorName:t.contact_form_title})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:T("ContactFormDescription")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(P,{register:n,label:"Description",name:"contact_form_description",type:"text",placeholder:T("ContactFormDescription")}),e.jsx(c,{errorName:t.contact_form_description})]})]})]})]})]})})},gl=()=>{const{setIsUpdate:i,lang:t}=b.useContext(qa),{showingTranslateValue:n}=Va(),[a,o]=b.useState([]),[h,x]=b.useState([]),[j,u]=b.useState([]),[N,k]=b.useState([]),[s,w]=b.useState("en"),[C,y]=b.useState(!0),[B,q]=b.useState(!0),[M,L]=b.useState(!0),[Q,T]=b.useState(!0),[ve,Ne]=b.useState(!0),[we,Te]=b.useState(!0),[Se,Ce]=b.useState(!0),[Fe,z]=b.useState(!0),[v,F]=b.useState(!1),[I,f]=b.useState(!0),[E,A]=b.useState(!0),[$,je]=b.useState(!0),[Z,H]=b.useState(!0),[W,V]=b.useState(!0),[K,R]=b.useState(!0),[J,ye]=b.useState(!0),[X,le]=b.useState(!0),[ee,oe]=b.useState(!0),[te,Oe]=b.useState(!0),[ie,Be]=b.useState(!0),[ne,Ie]=b.useState(!0),[re,de]=b.useState(!0),[Le,De]=b.useState(!0),[Ee,Ue]=b.useState(!0),[Ae,Me]=b.useState(!1),[ns,rs]=b.useState(!0),[He,Re]=b.useState(!0),[ze,$e]=b.useState(!1),[Ke,me]=b.useState(!0),[Qe,ce]=b.useState(!0),[xe,pe]=b.useState(!0),[ge,be]=b.useState(!0),[_e,We]=b.useState(!0),[Ve,Ge]=b.useState(!0),[Ye,Ze]=b.useState(!0),[Je,Xe]=b.useState(!0),[es,ss]=b.useState(!0),[ts,r]=b.useState(!0),[as,ds]=b.useState(!0),[ms,cs]=b.useState(!0),[xs,ps]=b.useState(!0),[gs,bs]=b.useState(!0),[_s,us]=b.useState(!0),[hs,fs]=b.useState(!0),[js,ys]=b.useState(!0),[ks,vs]=b.useState(!0),[Ns,ws]=b.useState(!0),[Ts,Ss]=b.useState(!0),[Cs,Fs]=b.useState(!0),[qs,Ps]=b.useState(!0),[Os,Bs]=b.useState(!0),[Is,Ls]=b.useState(!0),[Ds,Es]=b.useState(!0),[Us,As]=b.useState(""),[Ms,Hs]=b.useState(!0),[Rs,zs]=b.useState(""),[$s,Ks]=b.useState(""),[Qs,Ws]=b.useState(""),[Vs,Gs]=b.useState(""),[Ys,Zs]=b.useState(""),[Js,Xs]=b.useState(""),[et,st]=b.useState(""),[tt,at]=b.useState(""),[lt,ot]=b.useState(""),[it,nt]=b.useState(""),[rt,dt]=b.useState(""),[mt,ct]=b.useState(""),[xt,pt]=b.useState(""),[gt,bt]=b.useState(""),[_t,ut]=b.useState(""),[ht,ft]=b.useState(""),[jt,yt]=b.useState(""),[kt,vt]=b.useState(""),[Nt,wt]=b.useState(""),[Tt,St]=b.useState(""),[Ct,Ft]=b.useState(""),[qt,Pt]=b.useState(""),[Ot,Bt]=b.useState(""),[It,Lt]=b.useState(""),[se,Dt]=b.useState(""),[ea,sa]=b.useState(""),[ta,aa]=b.useState(""),[la,oa]=b.useState(""),[ia,na]=b.useState(""),[ra,da]=b.useState(""),[ma,ca]=b.useState(""),[xa,pa]=b.useState(""),[ga,ba]=b.useState(""),[Ma,Et]=b.useState(!1),{handleDisableForDemo:Ha}=Ya(),{register:Ra,handleSubmit:za,setValue:d,formState:{errors:$a}}=Ga(),p=m=>{for(const l in m)m[l].trim()===""&&delete m[l];return m},Ka=async m=>{if(!Ha())try{Et(!0);const l={name:"storeCustomizationSetting",setting:{navbar:{categories_menu_status:_s,about_menu_status:hs,contact_menu_status:js,offers_menu_status:Je,term_and_condition_status:Is,privacy_policy_status:Os,faq_status:xe,help_text:p({...a?.navbar?.help_text,[s]:m.help_text||""}),categories:p({...a?.navbar?.categories,[s]:m.categories||""}),about_us:p({...a?.navbar?.about_us,[s]:m.about_us||""}),contact_us:p({...a?.navbar?.contact_us,[s]:m.contact_us||""}),offers:p({...a?.navbar?.offers,[s]:m.offers||""}),faq:p({...a?.navbar?.faq,[s]:m.faq||""}),privacy_policy:p({...a?.navbar?.privacy_policy,[s]:m.privacy_policy||""}),term_and_condition:p({...a?.navbar?.term_and_condition,[s]:m.term_and_condition||""}),pages:p({...a?.navbar?.pages,[s]:m.pages||""}),my_account:p({...a?.navbar?.my_account,[s]:m.my_account||""}),login:p({...a?.navbar?.login,[s]:m.login||""}),logout:p({...a?.navbar?.logout,[s]:m.logout||""}),checkout:p({...a?.navbar?.checkout,[s]:m.checkout||""}),phone:m.phone_number,logo:Vs},home:{coupon_status:Q,featured_status:$,discount_status:K,daily_needs_status:J,slider_width_status:v,promotion_banner_status:Ae,delivery_status:W,popular_products_status:Z,discount_product_status:K,discount_coupon_code:j?.map(ke=>ke?.value),place_holder_img:kt,feature_promo_status:X,quick_delivery_link:m.quick_delivery_link,quick_delivery_img:Nt,discount_title:p({...a?.home?.discount_title,[s]:m.discount_title||""}),promotion_title:p({...a?.home?.promotion_title,[s]:m.promotion_title||""}),promotion_description:p({...a?.home?.promotion_description,[s]:m.promotion_description||""}),promotion_button_name:p({...a?.home?.promotion_button_name,[s]:m.promotion_button_name||""}),promotion_button_link:m.promotion_button_link,feature_title:p({...a?.home?.feature_title,[s]:m.feature_title||""}),feature_description:p({...a?.home?.feature_description,[s]:m.feature_description||""}),feature_product_limit:m.feature_product_limit,popular_title:p({...a?.home?.popular_title,[s]:m.popular_title||""}),popular_description:p({...a?.home?.popular_description,[s]:m.popular_description||""}),popular_product_limit:m.popular_product_limit,quick_delivery_subtitle:p({...a?.home?.quick_delivery_subtitle,[s]:m.quick_delivery_subtitle||""}),quick_delivery_title:p({...a?.home?.quick_delivery_title,[s]:m.quick_delivery_title||""}),quick_delivery_description:p({...a?.home?.quick_delivery_description,[s]:m.quick_delivery_description||""}),quick_delivery_button:p({...a?.home?.quick_delivery_button,[s]:m.quick_delivery_button||""}),latest_discount_title:p({...a?.home?.latest_discount_title,[s]:m.latest_discount_title||""}),latest_discount_description:p({...a?.home?.latest_discount_description,[s]:m.latest_discount_description||""}),latest_discount_product_limit:m.latest_discount_product_limit,daily_need_title:p({...a?.home?.daily_need_title,[s]:m.daily_need_title||""}),daily_need_description:p({...a?.home?.daily_need_description,[s]:m.daily_need_description||""}),daily_need_app_link:m.daily_need_app_link,daily_need_google_link:m.daily_need_google_link,daily_need_img_left:la,daily_need_img_right:ia,button1_img:rt,button2_img:mt},about_us:{header_status:gs,content_left_status:Cs,content_right_status:qs,content_right_img:xa,content_middle_status:Ms,founder_status:Ts,header_bg:tt,content_middle_Img:ea,founder_one_img:Tt,founder_two_img:Ct,founder_three_img:qt,founder_four_img:Ot,founder_five_img:It,founder_six_img:se,title:p({...a?.about_us?.title,[s]:m.about_page_title||""}),top_title:p({...a?.about_us?.top_title,[s]:m.about_page_Top_title||""}),top_description:p({...a?.about_us?.top_description,[s]:m.about_us_top_description||""}),card_one_title:p({...a?.about_us?.card_one_title,[s]:m.about_page_Top_left_box_one_title||""}),card_one_sub:p({...a?.about_us?.card_one_sub,[s]:m.about_page_Top_left_box_one_subtitle||""}),card_one_description:p({...a?.about_us?.card_one_description,[s]:m.about_us_top_box_one_description||""}),card_two_title:p({...a?.about_us?.card_two_title,[s]:m.about_page_Top_left_box_two_title||""}),card_two_sub:p({...a?.about_us?.card_two_sub,[s]:m.about_page_Top_left_box_two_subtitle||""}),card_two_description:p({...a?.about_us?.card_two_description,[s]:m.about_us_top_box_two_description||""}),middle_description_one:p({...a?.about_us?.middle_description_one,[s]:m.about_us_middle_description_one||""}),middle_description_two:p({...a?.about_us?.middle_description_two,[s]:m.about_us_middle_description_two||""}),founder_title:p({...a?.about_us?.founder_title,[s]:m.about_page_ourfounder_title||""}),founder_description:p({...a?.about_us?.founder_description,[s]:m.about_us_ourfounder_description||""}),founder_one_name:p({...a?.about_us?.founder_one_name,[s]:m.about_page_ourfounder_one_title||""}),founder_one_sub:p({...a?.about_us?.founder_one_sub,[s]:m.about_page_ourfounder_one_sub_title||""}),founder_two_name:p({...a?.about_us?.founder_two_name,[s]:m.about_page_ourfounder_two_title||""}),founder_two_sub:p({...a?.about_us?.founder_two_sub,[s]:m.about_page_ourfounder_two_sub_title||""}),founder_three_name:p({...a?.about_us?.founder_three_name,[s]:m.about_page_ourfounder_three_title||""}),founder_three_sub:p({...a?.about_us?.founder_three_sub,[s]:m.about_page_ourfounder_three_sub_title||""}),founder_four_name:p({...a?.about_us?.founder_four_name,[s]:m.about_page_ourfounder_four_title||""}),founder_four_sub:p({...a?.about_us?.founder_four_sub,[s]:m.about_page_ourfounder_four_sub_title||""}),founder_five_name:p({...a?.about_us?.founder_five_name,[s]:m.about_page_ourfounder_five_title||""}),founder_five_sub:p({...a?.about_us?.founder_five_sub,[s]:m.about_page_ourfounder_five_sub_title||""}),founder_six_name:p({...a?.about_us?.founder_six_name,[s]:m.about_page_ourfounder_six_title||""}),founder_six_sub:p({...a?.about_us?.founder_six_sub,[s]:m.about_page_ourfounder_six_sub_title||""})},contact_us:{header_status:ks,email_box_status:ge,call_box_status:_e,address_box_status:Ve,left_col_status:Ds,form_status:Ns,header_bg:gt,left_col_img:Us,title:p({...a?.contact_us?.title,[s]:m.contact_page_title||""}),email_box_title:p({...a?.contact_us?.email_box_title,[s]:m.email_box_title||""}),email_box_email:p({...a?.contact_us?.email_box_email,[s]:m.email_box_email||""}),email_box_text:p({...a?.contact_us?.email_box_text,[s]:m.email_box_text||""}),call_box_title:p({...a?.contact_us?.call_box_title,[s]:m.callUs_box_title||""}),call_box_phone:p({...a?.contact_us?.call_box_phone,[s]:m.callUs_box_phone||""}),call_box_text:p({...a?.contact_us?.call_box_text,[s]:m.callUs_box_text||""}),address_box_title:p({...a?.contact_us?.address_box_title,[s]:m.address_box_title||""}),address_box_address_one:p({...a?.contact_us?.address_box_address_one,[s]:m.address_box_address_one||""}),address_box_address_two:p({...a?.contact_us?.address_box_address_two,[s]:m.address_box_address_two||""}),address_box_address_three:p({...a?.contact_us?.address_box_address_three,[s]:m.address_box_address_three||""}),form_title:p({...a?.contact_us?.form_title,[s]:m.contact_form_title||""}),form_description:p({...a?.contact_us?.form_description,[s]:m.contact_form_description||""})},offers:{header_status:xs,header_bg:lt,title:p({...a?.offers?.title,[s]:m.offers_page_title||""}),coupon_code:N?.map(ke=>ke?.value)},privacy_policy:{status:es,header_bg:ta,title:p({...a?.privacy_policy?.title,[s]:m.privacy_page_title||""}),description:p({...a?.privacy_policy?.description,[s]:ra||""})},term_and_condition:{status:ts,header_bg:ga,title:p({...a?.term_and_condition?.title,[s]:m.termsConditions_page_title||""}),description:{...a?.term_and_condition?.description,[s]:ma||""}},faq:{page_status:Ye,header_bg:et,leftcol_status:as,rightcol_status:ms,left_img:xt,title:p({...a?.faq?.title,[s]:m.faq_page_title||""}),faq_one:p({...a?.faq?.faq_one,[s]:m.faq_title_one||""}),description_one:p({...a?.faq?.description_one,[s]:m.faq_description_one||""}),faq_two:p({...a?.faq?.faq_two,[s]:m.faq_title_two||""}),description_two:p({...a?.faq?.description_two,[s]:m.faq_description_two||""}),faq_three:p({...a?.faq?.faq_three,[s]:m.faq_title_three||""}),description_three:p({...a?.faq?.description_three,[s]:m.faq_description_three||""}),faq_four:p({...a?.faq?.faq_four,[s]:m.faq_title_four||""}),description_four:p({...a?.faq?.description_four,[s]:m.faq_description_four||""}),faq_five:p({...a?.faq?.faq_five,[s]:m.faq_title_five||""}),description_five:p({...a?.faq?.description_five,[s]:m.faq_description_five||""}),faq_six:p({...a?.faq?.faq_six,[s]:m.faq_title_six||""}),description_six:p({...a?.faq?.description_six,[s]:m.faq_description_six||""}),faq_seven:p({...a?.faq?.faq_seven,[s]:m.faq_title_seven||""}),description_seven:p({...a?.faq?.description_seven,[s]:m.faq_description_seven||""}),faq_eight:p({...a?.faq?.faq_eight,[s]:m.faq_title_eight||""}),description_eight:p({...a?.faq?.description_eight,[s]:m.faq_description_eight||""})},slider:{left_right_arrow:ze,bottom_dots:Ke,both_slider:Qe,first_img:Ys,first_title:p({...a?.slider?.first_title,[s]:m.slider_title||""}),first_description:p({...a?.slider?.first_description,[s]:m.slider_description||""}),first_button:p({...a?.slider?.first_button,[s]:m.slider_button_name||""}),first_link:m.slider_button_link,second_img:it,second_title:p({...a?.slider?.second_title,[s]:m.slider_title_two||""}),second_description:p({...a?.slider?.second_description,[s]:m.slider_description_two||""}),second_button:p({...a?.slider?.second_button,[s]:m.slider_button_name_two||""}),second_link:m.slider_button_link_two,third_img:_t,third_title:p({...a?.slider?.third_title,[s]:m.slider_title_three||""}),third_description:p({...a?.slider?.third_description,[s]:m.slider_description_three||""}),third_button:p({...a?.slider?.third_button,[s]:m.slider_button_name_three||""}),third_link:m.slider_button_link_three,four_img:ht,four_title:p({...a?.slider?.four_title,[s]:m.slider_title_four||""}),four_description:p({...a?.slider?.four_description,[s]:m.slider_description_four||""}),four_button:p({...a?.slider?.four_button,[s]:m.slider_button_name_four||""}),four_link:m.slider_button_link_four,five_img:jt,five_title:p({...a?.slider?.five_title,[s]:m.slider_title_five||""}),five_description:p({...a?.slider?.five_description,[s]:m.slider_description_five||""}),five_button:p({...a?.slider?.five_button,[s]:m.slider_button_name_five||""}),five_link:m.slider_button_link_five},checkout:{personal_details:p({...a?.checkout?.personal_details,[s]:m.personal_details||""}),first_name:p({...a?.checkout?.first_name,[s]:m.first_name||""}),last_name:p({...a?.checkout?.last_name,[s]:m.last_name||""}),email_address:p({...a?.checkout?.email_address,[s]:m.email_address||""}),checkout_phone:p({...a?.checkout?.checkout_phone,[s]:m.checkout_phone||""}),shipping_details:p({...a?.checkout?.shipping_details,[s]:m.shipping_details||""}),street_address:p({...a?.checkout?.street_address,[s]:m.street_address||""}),city:p({...a?.checkout?.city,[s]:m.city||""}),country:p({...a?.checkout?.country,[s]:m.country||""}),zip_code:p({...a?.checkout?.zip_code,[s]:m.zip_code||""}),shipping_cost:p({...a?.checkout?.shipping_cost,[s]:m.shipping_cost||""}),shipping_name_one:p({...a?.checkout?.shipping_name_one,[s]:m.shipping_name_one||""}),shipping_one_desc:p({...a?.checkout?.shipping_one_desc,[s]:m.shipping_one_desc||""}),shipping_one_cost:m?.shipping_one_cost,shipping_name_two:p({...a?.checkout?.shipping_name_two,[s]:m.shipping_name_two||""}),shipping_two_desc:p({...a?.checkout?.shipping_two_desc,[s]:m.shipping_two_desc||""}),shipping_two_cost:m?.shipping_two_cost,payment_method:p({...a?.checkout?.payment_method,[s]:m.payment_method||""}),continue_button:p({...a?.checkout?.continue_button,[s]:m.continue_button||""}),confirm_button:p({...a?.checkout?.confirm_button,[s]:m.confirm_button||""}),order_summary:p({...a?.checkout?.order_summary,[s]:m.order_summary||""}),apply_button:p({...a?.checkout?.apply_button,[s]:m.apply_button||""}),sub_total:p({...a?.checkout?.sub_total,[s]:m.sub_total||""}),discount:p({...a?.checkout?.discount,[s]:m.discount||""}),total_cost:p({...a?.checkout?.total_cost,[s]:m.total_cost||""})},dashboard:{invoice_message_first:p({...a?.dashboard?.invoice_message_first,[s]:m.invoice_message_first||""}),invoice_message_last:p({...a?.dashboard?.invoice_message_last,[s]:m.invoice_message_last||""}),print_button:p({...a?.dashboard?.print_button,[s]:m.print_button||""}),download_button:p({...a?.dashboard?.download_button,[s]:m.download_button||""}),dashboard_title:p({...a?.dashboard?.dashboard_title,[s]:m.dashboard_title||""}),total_order:p({...a?.dashboard?.total_order,[s]:m.total_order||""}),pending_order:p({...a?.dashboard?.pending_order,[s]:m.pending_order||""}),processing_order:p({...a?.dashboard?.processing_order,[s]:m.processing_order||""}),complete_order:p({...a?.dashboard?.complete_order,[s]:m.complete_order||""}),recent_order:p({...a?.dashboard?.recent_order,[s]:m.recent_order||""}),my_order:p({...a?.dashboard?.my_order,[s]:m.my_order||""}),update_profile:p({...a?.dashboard?.update_profile,[s]:m.update_profile||""}),full_name:p({...a?.dashboard?.full_name,[s]:m.full_name||""}),address:p({...a?.dashboard?.address,[s]:m.address||""}),user_phone:p({...a?.dashboard?.user_phone,[s]:m.user_phone||""}),user_email:p({...a?.dashboard?.user_email,[s]:m.user_email||""}),update_button:p({...a?.dashboard?.update_button,[s]:m.update_button||""}),current_password:p({...a?.dashboard?.current_password,[s]:m.current_password||""}),new_password:p({...a?.dashboard?.new_password,[s]:m.new_password||""}),change_password:p({...a?.dashboard?.change_password,[s]:m.change_password||""})},footer:{promo_status:!0,block1_status:ee,block2_status:te,block3_status:ie,block4_status:ne,payment_method_status:Le,bottom_contact_status:Ee,social_links_status:re,shipping_card:p({...a?.footer?.shipping_card,[s]:m.promo_free_shipping||""}),support_card:p({...a?.footer?.support_card,[s]:m.promo_support||""}),payment_card:p({...a?.footer?.payment_card,[s]:m.promo_payment||""}),offer_card:p({...a?.footer?.offer_card,[s]:m.promo_offer||""}),block1_title:p({...a?.footer?.block1_title,[s]:m.footer_block_one_title||""}),block1_sub_title1:p({...a?.footer?.block1_sub_title1,[s]:m.footer_block_one_link_one_title||""}),block1_sub_link1:m.footer_block_one_link_one,block1_sub_title2:p({...a?.footer?.block1_sub_title2,[s]:m.footer_block_one_link_two_title||""}),block1_sub_link2:m.footer_block_one_link_two,block1_sub_title3:p({...a?.footer?.block1_sub_title3,[s]:m.footer_block_one_link_three_title||""}),block1_sub_link3:m.footer_block_one_link_three,block1_sub_title4:p({...a?.footer?.block1_sub_title4,[s]:m.footer_block_one_link_four_title||""}),block1_sub_link4:m.footer_block_one_link_four,block2_title:p({...a?.footer?.block2_title,[s]:m.footer_block_two_title||""}),block2_sub_title1:p({...a?.footer?.block2_sub_title1,[s]:m.footer_block_two_link_one_title||""}),block2_sub_link1:m.footer_block_two_link_one,block2_sub_title2:p({...a?.footer?.block2_sub_title2,[s]:m.footer_block_two_link_two_title||""}),block2_sub_link2:m.footer_block_two_link_two,block2_sub_title3:p({...a?.footer?.block2_sub_title3,[s]:m.footer_block_two_link_three_title||""}),block2_sub_link3:m.footer_block_two_link_three,block2_sub_title4:p({...a?.footer?.block2_sub_title4,[s]:m.footer_block_two_link_four_title||""}),block2_sub_link4:m.footer_block_two_link_four,block3_title:p({...a?.footer?.block3_title,[s]:m.footer_block_three_title||""}),block3_sub_title1:p({...a?.footer?.block3_sub_title1,[s]:m.footer_block_three_link_one_title||""}),block3_sub_link1:m.footer_block_three_link_one,block3_sub_title2:p({...a?.footer?.block3_sub_title2,[s]:m.footer_block_three_link_two_title||""}),block3_sub_link2:m.footer_block_three_link_two,block3_sub_title3:p({...a?.footer?.block3_sub_title3,[s]:m.footer_block_three_link_three_title||""}),block3_sub_link3:m.footer_block_three_link_three,block3_sub_title4:p({...a?.footer?.block3_sub_title4,[s]:m.footer_block_three_link_four_title||""}),block3_sub_link4:m.footer_block_three_link_four,footer_block_four_link_one:m.footer_block_four_link_one,block4_logo:Qs,block4_address:p({...a?.footer?.block4_address,[s]:m.footer_block_four_address||""}),social_facebook:m.social_facebook,social_twitter:m.social_twitter,social_pinterest:m.social_pinterest,social_linkedin:m.social_linkedin,social_whatsapp:m.social_whatsapp,payment_method_img:Js,block4_phone:m.footer_block_four_phone,block4_email:m.footer_block_four_email,bottom_contact:m.footer_Bottom_Contact},slug:{right_box_status:He,card_description_one:p({...a?.slug?.card_description_one,[s]:m.slug_page_card_description_one||""}),card_description_two:p({...a?.slug?.card_description_two,[s]:m.slug_page_card_description_two||""}),card_description_three:p({...a?.slug?.card_description_three,[s]:m.slug_page_card_description_three||""}),card_description_four:p({...a?.slug?.card_description_four,[s]:m.slug_page_card_description_four||""}),card_description_five:p({...a?.slug?.card_description_five,[s]:m.slug_page_card_description_five||""}),card_description_six:p({...a?.slug?.card_description_six,[s]:m.slug_page_card_description_six||""}),card_description_seven:p({...a?.slug?.card_description_seven,[s]:m.slug_page_card_description_seven||""})},seo:{meta_img:Rs,favicon:$s,meta_title:m.meta_title,meta_description:m.meta_description,meta_url:m.meta_url,meta_keywords:m.meta_keywords}}};if(C){const ke=await zt.addStoreCustomizationSetting(l);i(!0),Et(!1),window.location.reload(),ha(ke.message)}else{const ke=await zt.updateStoreCustomizationSetting(l);i(!0),Et(!1),window.location.reload(),ha(ke.message)}}catch(l){ua(l?l?.response?.data?.message:l?.message),Et(!1)}};return b.useEffect(()=>((async()=>{try{const l=await zt.getStoreCustomizationSetting();if(l){y(!1),o(l),us(l?.navbar?.categories_menu_status),fs(l?.navbar?.about_menu_status),ys(l?.navbar?.contact_menu_status),Xe(l?.navbar?.offers_menu_status),pe(l?.navbar?.faq_status),Bs(l?.navbar?.privacy_policy_status),Ls(l?.navbar?.term_and_condition_status),d("help_text",l?.navbar?.help_text[s||"en"]),d("categories",l?.navbar?.categories[s||"en"]),d("about_us",l?.navbar?.about_us[s||"en"]),d("contact_us",l?.navbar?.contact_us[s||"en"]),d("offers",l?.navbar?.offers[s||"en"]),d("faq",l?.navbar?.faq[s||"en"]),d("privacy_policy",l?.navbar?.privacy_policy[s||"en"]),d("term_and_condition",l?.navbar?.term_and_condition[s||"en"]),d("pages",l?.navbar?.pages[s||"en"]),d("my_account",l?.navbar?.my_account[s||"en"]),d("login",l?.navbar?.login[s||"en"]),d("logout",l?.navbar?.logout[s||"en"]),d("checkout",l?.navbar?.checkout[s||"en"]),Gs(l?.navbar?.logo),d("phone_number",l?.navbar?.phone),vt(l?.home?.place_holder_img),wt(l?.home?.quick_delivery_img),oa(l?.home?.daily_need_img_left),na(l?.home?.daily_need_img_right),dt(l?.home?.button1_img),ct(l?.home?.button2_img),T(l?.home?.coupon_status),F(l?.home?.slider_width_status),Me(l?.home?.promotion_banner_status),H(l?.home?.popular_products_status),je(l?.home?.featured_status),V(l?.home?.delivery_status),R(l?.home?.discount_product_status),ye(l?.home?.daily_needs_status),le(l?.home?.feature_promo_status);const ke=l?.home?.discount_coupon_code?.map(qe=>({label:qe,value:qe}));u(ke||[]),d("discount_title",l?.home?.discount_title[s||"en"]),d("promotion_title",l?.home?.promotion_title[s||"en"]),d("promotion_description",l?.home?.promotion_description[s||"en"]),d("promotion_button_name",l?.home?.promotion_button_name[s||"en"]),d("promotion_button_link",l?.home?.promotion_button_link),d("feature_title",l?.home?.feature_title[s||"en"]),d("feature_description",l?.home?.feature_description[s||"en"]),d("feature_product_limit",l?.home?.feature_product_limit),d("popular_title",l?.home?.popular_title[s||"en"]),d("popular_description",l?.home?.popular_description[s||"en"]),d("popular_product_limit",l?.home?.popular_product_limit),d("quick_delivery_subtitle",l?.home?.quick_delivery_subtitle[s||"en"]),d("quick_delivery_title",l?.home?.quick_delivery_title[s||"en"]),d("quick_delivery_description",l?.home?.quick_delivery_description[s||"en"]),d("quick_delivery_button",l?.home?.quick_delivery_button[s||"en"]),d("quick_delivery_link",l?.home?.quick_delivery_link),d("latest_discount_title",l?.home?.latest_discount_title[s||"en"]),d("latest_discount_description",l?.home?.latest_discount_description[s||"en"]),d("latest_discount_product_limit",l?.home?.latest_discount_product_limit),d("daily_need_title",l?.home?.daily_need_title[s||"en"]),d("daily_need_description",l?.home?.daily_need_description[s||"en"]),d("daily_need_app_link",l?.home?.daily_need_app_link),d("daily_need_google_link",l?.home?.daily_need_google_link),Zs(l?.slider?.first_img),nt(l?.slider?.second_img),ut(l?.slider?.third_img),ft(l?.slider?.four_img),yt(l?.slider?.five_img),$e(l?.slider?.left_right_arrow),me(l?.slider?.bottom_dots),ce(l?.slider?.both_slider),d("slider_title",l?.slider?.first_title[s||"en"]),d("slider_description",l?.slider?.first_description[s||"en"]),d("slider_button_name",l?.slider?.first_button[s||"en"]),d("slider_button_link",l?.slider?.first_link),d("slider_title_two",l?.slider?.second_title[s||"en"]),d("slider_description_two",l?.slider?.second_description[s||"en"]),d("slider_button_name_two",l?.slider?.second_button[s||"en"]),d("slider_button_link_two",l?.slider?.second_link),d("slider_title_three",l?.slider?.third_title[s||"en"]),d("slider_description_three",l?.slider?.third_description[s||"en"]),d("slider_button_name_three",l?.slider?.third_button[s||"en"]),d("slider_button_link_three",l?.slider?.third_link),d("slider_title_four",l?.slider?.four_title[s||"en"]),d("slider_description_four",l?.slider?.four_description[s||"en"]),d("slider_button_name_four",l?.slider?.four_button[s||"en"]),d("slider_button_link_four",l?.slider?.four_link),d("slider_title_five",l?.slider?.five_title[s||"en"]),d("slider_description_five",l?.slider?.five_description[s||"en"]),d("slider_button_name_five",l?.slider?.five_button[s||"en"]),d("slider_button_link_five",l?.slider?.five_link),d("personal_details",l?.checkout?.personal_details[s||"en"]),d("first_name",l?.checkout?.first_name[s||"en"]),d("last_name",l?.checkout?.last_name[s||"en"]),d("email_address",l?.checkout?.email_address[s||"en"]),d("checkout_phone",l?.checkout?.checkout_phone[s||"en"]),d("shipping_details",l?.checkout?.shipping_details[s||"en"]),d("street_address",l?.checkout?.street_address[s||"en"]),d("city",l?.checkout?.city[s||"en"]),d("country",l?.checkout?.country[s||"en"]),d("zip_code",l?.checkout?.zip_code[s||"en"]),d("shipping_cost",l?.checkout?.shipping_cost[s||"en"]),d("shipping_name_one",l?.checkout?.shipping_name_one[s||"en"]),d("shipping_one_desc",l?.checkout?.shipping_one_desc[s||"en"]),d("shipping_one_cost",l?.checkout?.shipping_one_cost),d("shipping_name_two",l?.checkout?.shipping_name_two[s||"en"]),d("shipping_two_desc",l?.checkout?.shipping_two_desc[s||"en"]),d("shipping_two_cost",l?.checkout?.shipping_two_cost),d("payment_method",l?.checkout?.payment_method[s||"en"]),d("continue_button",l?.checkout?.continue_button[s||"en"]),d("confirm_button",l?.checkout?.confirm_button[s||"en"]),d("order_summary",l?.checkout?.order_summary[s||"en"]),d("apply_button",l?.checkout?.apply_button[s||"en"]),d("sub_total",l?.checkout?.sub_total[s||"en"]),d("discount",l?.checkout?.discount[s||"en"]),d("total_cost",l?.checkout?.total_cost[s||"en"]),d("invoice_message_first",l?.dashboard?.invoice_message_first[s||"en"]),d("invoice_message_last",l?.dashboard?.invoice_message_last[s||"en"]),d("print_button",l?.dashboard?.print_button[s||"en"]),d("download_button",l?.dashboard?.download_button[s||"en"]),d("dashboard_title",l?.dashboard?.dashboard_title[s||"en"]),d("total_order",l?.dashboard?.total_order[s||"en"]),d("pending_order",l?.dashboard?.pending_order[s||"en"]),d("processing_order",l?.dashboard?.processing_order[s||"en"]),d("complete_order",l?.dashboard?.complete_order[s||"en"]),d("recent_order",l?.dashboard?.recent_order[s||"en"]),d("my_order",l?.dashboard?.my_order[s||"en"]),d("update_profile",l?.dashboard?.update_profile[s||"en"]),d("full_name",l?.dashboard?.full_name[s||"en"]),d("address",l?.dashboard?.address[s||"en"]),d("user_phone",l?.dashboard?.user_phone[s||"en"]),d("user_email",l?.dashboard?.user_email[s||"en"]),d("update_button",l?.dashboard?.update_button[s||"en"]),d("current_password",l?.dashboard?.current_password[s||"en"]),d("new_password",l?.dashboard?.new_password[s||"en"]),d("change_password",l?.dashboard?.change_password[s||"en"]),oe(l?.footer?.block1_status),Oe(l?.footer?.block2_status),Be(l?.footer?.block3_status),Ie(l?.footer?.block4_status),Ws(l?.footer?.block4_logo),Xs(l?.footer?.payment_method_img),de(l?.footer?.social_links_status),De(l?.footer?.payment_method_status),Ue(l?.footer?.bottom_contact_status),d("promo_free_shipping",l?.footer?.shipping_card[s||"en"]),d("promo_support",l?.footer?.support_card[s||"en"]),d("promo_payment",l?.footer?.payment_card[s||"en"]),d("promo_offer",l?.footer?.offer_card[s||"en"]),d("footer_block_one_title",l?.footer?.block1_title[s||"en"]),d("footer_block_one_link_one",l?.footer?.block1_sub_link1),d("footer_block_one_link_two",l?.footer?.block1_sub_link2),d("footer_block_one_link_three",l?.footer?.block1_sub_link3),d("footer_block_one_link_four",l?.footer?.block1_sub_link4),d("footer_block_one_link_one_title",l?.footer?.block1_sub_title1[s||"en"]),d("footer_block_one_link_two_title",l?.footer?.block1_sub_title2[s||"en"]),d("footer_block_one_link_three_title",l?.footer?.block1_sub_title3[s||"en"]),d("footer_block_one_link_four_title",l?.footer?.block1_sub_title4[s||"en"]),d("footer_block_two_title",l?.footer?.block2_title[s||"en"]),d("footer_block_two_link_one",l?.footer?.block2_sub_link1),d("footer_block_two_link_two",l?.footer?.block2_sub_link2),d("footer_block_two_link_three",l?.footer?.block2_sub_link3),d("footer_block_two_link_four",l?.footer?.block2_sub_link4),d("footer_block_two_link_one_title",l?.footer?.block2_sub_title1[s||"en"]),d("footer_block_two_link_two_title",l?.footer?.block2_sub_title2[s||"en"]),d("footer_block_two_link_three_title",l?.footer?.block2_sub_title3[s||"en"]),d("footer_block_two_link_four_title",l?.footer?.block2_sub_title4[s||"en"]),d("footer_block_three_title",l?.footer?.block3_title[s||"en"]),d("footer_block_three_link_one",l?.footer?.block3_sub_link1),d("footer_block_three_link_two",l?.footer?.block3_sub_link2),d("footer_block_three_link_three",l?.footer?.block3_sub_link3),d("footer_block_three_link_four",l?.footer?.block3_sub_link4),d("footer_block_three_link_one_title",l?.footer?.block3_sub_title1[s||"en"]),d("footer_block_three_link_two_title",l?.footer?.block3_sub_title2[s||"en"]),d("footer_block_three_link_three_title",l?.footer?.block3_sub_title3[s||"en"]),d("footer_block_three_link_four_title",l?.footer?.block3_sub_title4[s||"en"]),d("footer_block_four_address",l?.footer?.block4_address[s||"en"]),d("footer_block_four_phone",l?.footer?.block4_phone),d("footer_block_four_email",l?.footer?.block4_email),d("social_facebook",l?.footer?.social_facebook),d("social_twitter",l?.footer?.social_twitter),d("social_pinterest",l?.footer?.social_pinterest),d("social_linkedin",l?.footer?.social_linkedin),d("social_whatsapp",l?.footer?.social_whatsapp),d("footer_Bottom_Contact",l?.footer?.bottom_contact),Re(l?.slug?.right_box_status),d("slug_page_card_icon_one",l?.slug?.slug_page_card_icon_one),d("slug_page_card_description_one",l?.slug?.card_description_one[s||"en"]),d("slug_page_card_icon_two",l?.slug?.slug_page_card_icon_two),d("slug_page_card_description_two",l?.slug?.card_description_two[s||"en"]),d("slug_page_card_icon_three",l?.slug?.slug_page_card_icon_three),d("slug_page_card_description_three",l?.slug?.card_description_three[s||"en"]),d("slug_page_card_icon_four",l?.slug?.slug_page_card_icon_four),d("slug_page_card_description_four",l?.slug?.card_description_four[s||"en"]),d("slug_page_card_icon_five",l?.slug?.slug_page_card_icon_five),d("slug_page_card_description_five",l?.slug?.card_description_five[s||"en"]),d("slug_page_card_icon_six",l?.slug?.slug_page_card_icon_six),d("slug_page_card_description_six",l?.slug?.card_description_six[s||"en"]),d("slug_page_card_icon_seven",l?.slug?.slug_page_card_icon_seven),d("slug_page_card_description_seven",l?.slug?.card_description_seven[s||"en"]),bs(l?.about_us?.header_status),at(l?.about_us?.header_bg),Fs(l?.about_us?.content_left_status),Ps(l?.about_us?.content_right_status),pa(l?.about_us?.content_right_img),Hs(l?.about_us?.content_middle_status),sa(l?.about_us?.content_middle_Img),Ss(l?.about_us?.founder_status),St(l?.about_us?.founder_one_img),Ft(l?.about_us?.founder_two_img),Pt(l?.about_us?.founder_three_img),Bt(l?.about_us?.founder_four_img),Lt(l?.about_us?.founder_five_img),Dt(l?.about_us?.founder_six_img),d("about_page_title",l?.about_us?.title[s||"en"]),d("about_page_Top_title",l?.about_us?.top_title[s||"en"]),d("about_us_top_description",l?.about_us?.top_description[s||"en"]),d("about_page_Top_left_box_one_title",l?.about_us?.card_one_title[s||"en"]),d("about_page_Top_left_box_one_subtitle",l?.about_us?.card_one_sub[s||"en"]),d("about_us_top_box_one_description",l?.about_us?.card_one_description[s||"en"]),d("about_page_Top_left_box_two_title",l?.about_us?.card_two_title[s||"en"]),d("about_page_Top_left_box_two_subtitle",l?.about_us?.card_two_sub[s||"en"]),d("about_us_top_box_two_description",l?.about_us?.card_two_description[s||"en"]),d("about_us_middle_description_one",l?.about_us?.middle_description_one[s||"en"]),d("about_us_middle_description_two",l?.about_us?.middle_description_two[s||"en"]),d("about_page_ourfounder_title",l?.about_us?.founder_title[s||"en"]),d("about_us_ourfounder_description",l?.about_us?.founder_description[s||"en"]),d("about_page_ourfounder_one_title",l?.about_us?.founder_one_name[s||"en"]),d("about_page_ourfounder_one_sub_title",l?.about_us?.founder_one_sub[s||"en"]),d("about_page_ourfounder_two_title",l?.about_us?.founder_two_name[s||"en"]),d("about_page_ourfounder_two_sub_title",l?.about_us?.founder_two_sub[s||"en"]),d("about_page_ourfounder_three_title",l?.about_us?.founder_three_name[s||"en"]),d("about_page_ourfounder_three_sub_title",l?.about_us?.founder_three_sub[s||"en"]),d("about_page_ourfounder_four_title",l?.about_us?.founder_four_name[s||"en"]),d("about_page_ourfounder_four_sub_title",l?.about_us?.founder_four_sub[s||"en"]),d("about_page_ourfounder_five_title",l?.about_us?.founder_five_name[s||"en"]),d("about_page_ourfounder_five_sub_title",l?.about_us?.founder_five_sub[s||"en"]),d("about_page_ourfounder_six_title",l?.about_us?.founder_six_name[s||"en"]),d("about_page_ourfounder_six_sub_title",l?.about_us?.founder_six_sub[s||"en"]),vs(l?.contact_us?.header_status),bt(l?.contact_us?.header_bg),be(l?.contact_us?.email_box_status),We(l?.contact_us?.call_box_status),Ge(l?.contact_us?.address_box_status),Es(l?.contact_us?.left_col_status),As(l?.contact_us?.left_col_img),ws(l?.contact_us?.form_status),d("contact_page_title",l?.contact_us?.title[s||"en"]),d("email_box_title",l?.contact_us?.email_box_title[s||"en"]),d("email_box_email",l?.contact_us?.email_box_email[s||"en"]),d("email_box_text",l?.contact_us?.email_box_text[s||"en"]),d("callUs_box_title",l?.contact_us?.call_box_title[s||"en"]),d("callUs_box_phone",l?.contact_us?.call_box_phone[s||"en"]),d("callUs_box_text",l?.contact_us?.call_box_text[s||"en"]),d("address_box_title",l?.contact_us?.address_box_title[s||"en"]),d("address_box_address_one",l?.contact_us?.address_box_address_one[s||"en"]),d("address_box_address_two",l?.contact_us?.address_box_address_two[s||"en"]),d("address_box_address_three",l?.contact_us?.address_box_address_three[s||"en"]),d("contact_form_title",l?.contact_us?.form_title[s||"en"]),d("contact_form_description",l?.contact_us?.form_description[s||"en"]),ss(l?.privacy_policy?.status),aa(l?.privacy_policy?.header_bg),d("privacy_page_title",l?.privacy_policy?.title[s||"en"]),da(n(l?.privacy_policy?.description)||""),r(l?.term_and_condition?.status),ba(l?.term_and_condition?.header_bg),d("termsConditions_page_title",l?.term_and_condition?.title[s||"en"]),ca(n(l?.term_and_condition?.description)||""),Ze(l?.faq?.page_status),st(l?.faq?.header_bg),pt(l?.faq?.left_img),ds(l?.faq?.leftcol_status),cs(l?.faq?.rightcol_status),d("faq_page_title",l?.faq?.title[s||"en"]),d("faq_title_one",l?.faq?.faq_one[s||"en"]),d("faq_description_one",l?.faq?.description_one[s||"en"]),d("faq_title_two",l?.faq?.faq_two[s||"en"]),d("faq_description_two",l?.faq?.description_two[s||"en"]),d("faq_title_three",l?.faq?.faq_three[s||"en"]),d("faq_description_three",l?.faq?.description_three[s||"en"]),d("faq_title_four",l?.faq?.faq_four[s||"en"]),d("faq_description_four",l?.faq?.description_four[s||"en"]),d("faq_title_five",l?.faq?.faq_five[s||"en"]),d("faq_description_five",l?.faq?.description_five[s||"en"]),d("faq_title_six",l?.faq?.faq_six[s||"en"]),d("faq_description_six",l?.faq?.description_six[s||"en"]),d("faq_title_seven",l?.faq?.faq_seven[s||"en"]),d("faq_description_seven",l?.faq?.description_seven[s||"en"]),d("faq_title_eight",l?.faq?.faq_eight[s||"en"]),d("faq_description_eight",l?.faq?.description_eight[s||"en"]),ps(l?.offers?.header_status),ot(l?.offers?.header_bg),d("offers_page_title",l?.offers?.title[s||"en"]);const Qa=l?.offers?.coupon_code?.map(qe=>({label:qe,value:qe}));k(Qa||[]);const Wa=(await Za.getAllCoupons())?.map(qe=>({label:qe?.couponCode,value:qe?.couponCode}));x(Wa),zs(l.seo.meta_img),Ks(l.seo.favicon),d("meta_title",l.seo.meta_title),d("meta_description",l.seo.meta_description),d("meta_keywords",l.seo.meta_keywords),d("meta_url",l.seo.meta_url)}}catch(l){ua(l?l?.response?.data?.message:l?.message)}})(),()=>{new AbortController().abort()}),[s,d]),{register:Ra,handleSubmit:za,onSubmit:Ka,setValue:d,errors:$a,favicon:$s,coupons:h,setFavicon:Ks,metaImg:Rs,setMetaImg:zs,headerLogo:Vs,setHeaderLogo:Gs,sliderImage:Ys,setSliderImage:Zs,sliderImageTwo:it,setSliderImageTwo:nt,sliderImageThree:_t,setSliderImageThree:ut,sliderImageFour:ht,setSliderImageFour:ft,sliderImageFive:jt,setSliderImageFive:yt,placeholderImage:kt,setPlaceHolderImage:vt,quickSectionImage:Nt,setQuickSectionImage:wt,getYourDailyNeedImageLeft:la,setGetYourDailyNeedImageLeft:oa,getYourDailyNeedImageRight:ia,setGetYourDailyNeedImageRight:na,footerLogo:Qs,setFooterLogo:Ws,paymentImage:Js,setPaymentImage:Xs,allowDiscount:ve,setAllowDiscount:Ne,allowPromotionBanner:Ae,setAllowPromotionBanner:Me,printSlip:B,setPrintSlip:q,orderProcess:Se,setOrderProcess:Ce,paymentOption:we,setPaymentOption:Te,isSave:C,allowOutOfStock:Fe,setAllowOutOfStock:z,isCoupon:Q,isPlaceholder:I,isSliderFullWidth:v,setIsCoupon:T,setDiscount_coupon_status:rs,discount_coupon_status:ns,setIsPlaceholder:f,setIsSliderFullWidth:F,emailSlip:M,setEmailSlip:L,setPromotionBanner:A,promotionBanner:E,featuredCategories:$,setFeaturedCategories:je,popularProducts:Z,setPopularProducts:H,setQuickDelivery:V,quickDelivery:W,setLatestDiscounted:R,latestDiscounted:K,setDailyNeeds:ye,dailyNeeds:J,setFeaturePromo:le,featurePromo:X,setFooterBlock1:oe,footerBlock1:ee,setFooterBlock2:Oe,footerBlock2:te,setFooterBlock3:Be,footerBlock3:ie,setFooterBlock4:Ie,footerBlock4:ne,setFooterSocialLinks:de,footerSocialLinks:re,setFooterPaymentMethod:De,footerPaymentMethod:Le,setSingleProductPageRightBox:Re,singleProductPageRightBox:He,handleSelectLanguage:m=>{w(m)},setLeftRightArrow:$e,leftRightArrow:ze,setBottomDots:me,bottomDots:Ke,setBothSliderOption:ce,bothSliderOption:Qe,getButton1image:rt,setGetButton1image:dt,getButton2image:mt,setGetButton2image:ct,setFooterBottomContact:Ue,footerBottomContact:Ee,setCategoriesMenuLink:us,categoriesMenuLink:_s,setAboutUsMenuLink:fs,aboutUsMenuLink:hs,setContactUsMenuLink:ys,contactUsMenuLink:js,setOffersMenuLink:Xe,offersMenuLink:Je,setFaqMenuLink:pe,faqMenuLink:xe,setPrivacyPolicyMenuLink:Bs,privacyPolicyMenuLink:Os,setTermsConditionsMenuLink:Ls,termsConditionsMenuLink:Is,setAboutPageHeader:bs,aboutPageHeader:gs,setAboutTopContentLeft:Fs,aboutTopContentLeft:Cs,setAboutHeaderBg:at,aboutHeaderBg:tt,setAboutTopContentRight:Ps,aboutTopContentRight:qs,setAboutTopContentRightImage:pa,aboutTopContentRightImage:xa,setAboutMiddleContentSection:Hs,aboutMiddleContentSection:Ms,setAboutMiddleContentImage:sa,aboutMiddleContentImage:ea,setOurFounderSection:Ss,ourFounderSection:Ts,setOurFounderOneImage:St,ourFounderOneImage:Tt,setOurFounderTwoImage:Ft,ourFounderTwoImage:Ct,setOurFounderThreeImage:Pt,ourFounderThreeImage:qt,setOurFounderFourImage:Bt,ourFounderFourImage:Ot,setOurFounderFiveImage:Lt,ourFounderFiveImage:It,setOurFounderSixImage:Dt,ourFounderSixImage:se,setPrivacyPolicy:ss,privacyPolicy:es,setPrivacyPolicyHeaderBg:aa,privacyPolicyHeaderBg:ta,setTermsConditions:r,termsConditions:ts,setTermsConditionsHeaderBg:ba,termsConditionsHeaderBg:ga,setFaqStatus:Ze,faqStatus:Ye,setFaqLeftColStatus:ds,faqLeftColStatus:as,setFaqRightColStatus:cs,faqRightColStatus:ms,setFaqHeaderBg:st,faqHeaderBg:et,setFaqLeftColImage:pt,faqLeftColImage:xt,setOffersPageHeader:ps,offersPageHeader:xs,setOffersHeaderBg:ot,offersHeaderBg:lt,setContactPageHeader:vs,contactPageHeader:ks,setContactHeaderBg:bt,contactHeaderBg:gt,setEmailUsBox:be,emailUsBox:ge,setCallUsBox:We,callUsBox:_e,setAddressBox:Ge,addressBox:Ve,setContactMidLeftColStatus:Es,contactMidLeftColStatus:Ds,setContactMidLeftColImage:As,contactMidLeftColImage:Us,setContactFormStatus:ws,contactFormStatus:Ns,couponList:j,setCouponList:u,couponList1:N,setCouponList1:k,textEdit:ra,setTextEdit:da,termsConditionsTextEdit:ma,setTermsConditionsTextEdit:ca,isSubmitting:Ma}};var Gt=function(){return Gt=Object.assign||function(i){for(var t,n=1,a=arguments.length;n<a;n++){t=arguments[n];for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&(i[o]=t[o])}return i},Gt.apply(this,arguments)},Ea={onActivate:_.func,onAddUndo:_.func,onBeforeAddUndo:_.func,onBeforeExecCommand:_.func,onBeforeGetContent:_.func,onBeforeRenderUI:_.func,onBeforeSetContent:_.func,onBeforePaste:_.func,onBlur:_.func,onChange:_.func,onClearUndos:_.func,onClick:_.func,onContextMenu:_.func,onCommentChange:_.func,onCompositionEnd:_.func,onCompositionStart:_.func,onCompositionUpdate:_.func,onCopy:_.func,onCut:_.func,onDblclick:_.func,onDeactivate:_.func,onDirty:_.func,onDrag:_.func,onDragDrop:_.func,onDragEnd:_.func,onDragGesture:_.func,onDragOver:_.func,onDrop:_.func,onExecCommand:_.func,onFocus:_.func,onFocusIn:_.func,onFocusOut:_.func,onGetContent:_.func,onHide:_.func,onInit:_.func,onInput:_.func,onKeyDown:_.func,onKeyPress:_.func,onKeyUp:_.func,onLoadContent:_.func,onMouseDown:_.func,onMouseEnter:_.func,onMouseLeave:_.func,onMouseMove:_.func,onMouseOut:_.func,onMouseOver:_.func,onMouseUp:_.func,onNodeChange:_.func,onObjectResizeStart:_.func,onObjectResized:_.func,onObjectSelected:_.func,onPaste:_.func,onPostProcess:_.func,onPostRender:_.func,onPreProcess:_.func,onProgressState:_.func,onRedo:_.func,onRemove:_.func,onReset:_.func,onSaveContent:_.func,onSelectionChange:_.func,onSetAttrib:_.func,onSetContent:_.func,onShow:_.func,onSubmit:_.func,onUndo:_.func,onVisualAid:_.func,onSkinLoadError:_.func,onThemeLoadError:_.func,onModelLoadError:_.func,onPluginLoadError:_.func,onIconsLoadError:_.func,onLanguageLoadError:_.func,onScriptsLoad:_.func,onScriptsLoadError:_.func},bl=Gt({apiKey:_.string,licenseKey:_.string,id:_.string,inline:_.bool,init:_.object,initialValue:_.string,onEditorChange:_.func,value:_.string,tagName:_.string,tabIndex:_.number,cloudChannel:_.string,plugins:_.oneOfType([_.string,_.array]),toolbar:_.oneOfType([_.string,_.array]),disabled:_.bool,readonly:_.bool,textareaName:_.string,tinymceScriptSrc:_.oneOfType([_.string,_.arrayOf(_.string),_.arrayOf(_.shape({src:_.string,async:_.bool,defer:_.bool}))]),rollback:_.oneOfType([_.number,_.oneOf([!1])]),scriptLoading:_.shape({async:_.bool,defer:_.bool,delay:_.number})},Ea),Ua=function(i){var t=i;return t&&t.tinymce?t.tinymce:null},Kt=function(i){return typeof i=="function"},ya=function(i){return i in Ea},ka=function(i){return i.substr(2)},_l=function(i,t,n,a,o,h,x){var j=Object.keys(o).filter(ya),u=Object.keys(h).filter(ya),N=j.filter(function(s){return h[s]===void 0}),k=u.filter(function(s){return o[s]===void 0});N.forEach(function(s){var w=ka(s),C=x[w];n(w,C),delete x[w]}),k.forEach(function(s){var w=a(i,s),C=ka(s);x[C]=w,t(C,w)})},ul=function(i,t,n,a,o){return _l(o,i.on.bind(i),i.off.bind(i),function(h,x){return function(j){var u;return(u=h(x))===null||u===void 0?void 0:u(j,i)}},t,n,a)},va=0,Aa=function(i){var t=Date.now(),n=Math.floor(Math.random()*1e9);return va++,i+"_"+n+va+String(t)},Na=function(i){return i!==null&&(i.tagName.toLowerCase()==="textarea"||i.tagName.toLowerCase()==="input")},wa=function(i){return typeof i>"u"||i===""?[]:Array.isArray(i)?i:i.split(" ")},hl=function(i,t){return wa(i).concat(wa(t))},fl=function(){return window.InputEvent&&typeof InputEvent.prototype.getTargetRanges=="function"},jl=function(i){if(!("isConnected"in Node.prototype)){for(var t=i,n=i.parentNode;n!=null;)t=n,n=t.parentNode;return t===i.ownerDocument}return i.isConnected},Ta=function(i,t){i!==void 0&&(i.mode!=null&&typeof i.mode=="object"&&typeof i.mode.set=="function"?i.mode.set(t):i.setMode(t))},yl=function(i){var t=Ua(i);if(!t)throw new Error("tinymce should have been loaded into global scope");return t},Sa=function(i){return i.options&&i.options.isRegistered("disabled")},Yt=function(){return Yt=Object.assign||function(i){for(var t,n=1,a=arguments.length;n<a;n++){t=arguments[n];for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&(i[o]=t[o])}return i},Yt.apply(this,arguments)},kl=function(i,t,n){var a,o,h=i.createElement("script");h.referrerPolicy="origin",h.type="application/javascript",h.id=t.id,h.src=t.src,h.async=(a=t.async)!==null&&a!==void 0?a:!1,h.defer=(o=t.defer)!==null&&o!==void 0?o:!1;var x=function(){h.removeEventListener("load",x),h.removeEventListener("error",j),n(t.src)},j=function(u){h.removeEventListener("load",x),h.removeEventListener("error",j),n(t.src,u)};h.addEventListener("load",x),h.addEventListener("error",j),i.head&&i.head.appendChild(h)},vl=function(i){var t={},n=function(x,j){var u=t[x];u.done=!0,u.error=j;for(var N=0,k=u.handlers;N<k.length;N++){var s=k[N];s(x,j)}u.handlers=[]},a=function(x,j,u){var N=function(L){return u!==void 0?u(L):console.error(L)};if(x.length===0){N(new Error("At least one script must be provided"));return}for(var k=0,s=!1,w=function(L,Q){s||(Q?(s=!0,N(Q)):++k===x.length&&j())},C=0,y=x;C<y.length;C++){var B=y[C],q=t[B.src];if(q)q.done?w(B.src,q.error):q.handlers.push(w);else{var M=Aa("tiny-");t[B.src]={id:M,src:B.src,done:!1,error:null,handlers:[w]},kl(i,Yt({id:M},B),n)}}},o=function(){for(var x,j=0,u=Object.values(t);j<u.length;j++){var N=u[j],k=i.getElementById(N.id);k!=null&&k.tagName==="SCRIPT"&&((x=k.parentNode)===null||x===void 0||x.removeChild(k))}t={}},h=function(){return i};return{loadScripts:a,deleteScripts:o,getDocument:h}},Nl=function(){var i=[],t=function(o){var h=i.find(function(x){return x.getDocument()===o});return h===void 0&&(h=vl(o),i.push(h)),h},n=function(o,h,x,j,u){var N=function(){return t(o).loadScripts(h,j,u)};x>0?setTimeout(N,x):N()},a=function(){for(var o=i.pop();o!=null;o=i.pop())o.deleteScripts()};return{loadList:n,reinitialize:a}},wl=Nl(),Tl=function(){var i=function(t,n){return i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(a,o){a.__proto__=o}||function(a,o){for(var h in o)Object.prototype.hasOwnProperty.call(o,h)&&(a[h]=o[h])},i(t,n)};return function(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");i(t,n);function a(){this.constructor=t}t.prototype=n===null?Object.create(n):(a.prototype=n.prototype,new a)}}(),ls=function(){return ls=Object.assign||function(i){for(var t,n=1,a=arguments.length;n<a;n++){t=arguments[n];for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&(i[o]=t[o])}return i},ls.apply(this,arguments)},Qt="change keyup compositionend setcontent CommentChange",Ca=function(i){Tl(t,i);function t(n){var a,o,h,x=i.call(this,n)||this;return x.rollbackTimer=void 0,x.valueCursor=void 0,x.rollbackChange=function(){var j=x.editor,u=x.props.value;j&&u&&u!==x.currentContent&&j.undoManager.ignore(function(){if(j.setContent(u),x.valueCursor&&(!x.inline||j.hasFocus()))try{j.selection.moveToBookmark(x.valueCursor)}catch{}}),x.rollbackTimer=void 0},x.handleBeforeInput=function(j){if(x.props.value!==void 0&&x.props.value===x.currentContent&&x.editor&&(!x.inline||x.editor.hasFocus()))try{x.valueCursor=x.editor.selection.getBookmark(3)}catch{}},x.handleBeforeInputSpecial=function(j){(j.key==="Enter"||j.key==="Backspace"||j.key==="Delete")&&x.handleBeforeInput(j)},x.handleEditorChange=function(j){var u=x.editor;if(u&&u.initialized){var N=u.getContent();x.props.value!==void 0&&x.props.value!==N&&x.props.rollback!==!1&&(x.rollbackTimer||(x.rollbackTimer=window.setTimeout(x.rollbackChange,typeof x.props.rollback=="number"?x.props.rollback:200))),N!==x.currentContent&&(x.currentContent=N,Kt(x.props.onEditorChange)&&x.props.onEditorChange(N,u))}},x.handleEditorChangeSpecial=function(j){(j.key==="Backspace"||j.key==="Delete")&&x.handleEditorChange(j)},x.initialise=function(j){var u,N,k;j===void 0&&(j=0);var s=x.elementRef.current;if(s){if(!jl(s)){if(j===0)setTimeout(function(){return x.initialise(1)},1);else if(j<100)setTimeout(function(){return x.initialise(j+1)},100);else throw new Error("tinymce can only be initialised when in a document");return}var w=yl(x.view),C=ls(ls(ls(ls({},x.props.init),{selector:void 0,target:s,disabled:x.props.disabled,readonly:x.props.readonly,inline:x.inline,plugins:hl((u=x.props.init)===null||u===void 0?void 0:u.plugins,x.props.plugins),toolbar:(N=x.props.toolbar)!==null&&N!==void 0?N:(k=x.props.init)===null||k===void 0?void 0:k.toolbar}),x.props.licenseKey?{license_key:x.props.licenseKey}:{}),{setup:function(y){x.editor=y,x.bindHandlers({}),x.inline&&!Na(s)&&y.once("PostRender",function(B){y.setContent(x.getInitialValue(),{no_events:!0})}),x.props.init&&Kt(x.props.init.setup)&&x.props.init.setup(y),x.props.disabled&&(Sa(x.editor)?x.editor.options.set("disabled",x.props.disabled):x.editor.mode.set("readonly"))},init_instance_callback:function(y){var B,q=x.getInitialValue();x.currentContent=(B=x.currentContent)!==null&&B!==void 0?B:y.getContent(),x.currentContent!==q&&(x.currentContent=q,y.setContent(q),y.undoManager.clear(),y.undoManager.add(),y.setDirty(!1)),x.props.init&&Kt(x.props.init.init_instance_callback)&&x.props.init.init_instance_callback(y)}});x.inline||(s.style.visibility=""),Na(s)&&(s.value=x.getInitialValue()),w.init(C)}},x.id=x.props.id||Aa("tiny-react"),x.elementRef=b.createRef(),x.inline=(h=(a=x.props.inline)!==null&&a!==void 0?a:(o=x.props.init)===null||o===void 0?void 0:o.inline)!==null&&h!==void 0?h:!1,x.boundHandlers={},x}return Object.defineProperty(t.prototype,"view",{get:function(){var n,a;return(a=(n=this.elementRef.current)===null||n===void 0?void 0:n.ownerDocument.defaultView)!==null&&a!==void 0?a:window},enumerable:!1,configurable:!0}),t.prototype.componentDidUpdate=function(n){var a=this,o,h;if(this.rollbackTimer&&(clearTimeout(this.rollbackTimer),this.rollbackTimer=void 0),this.editor&&(this.bindHandlers(n),this.editor.initialized)){if(this.currentContent=(o=this.currentContent)!==null&&o!==void 0?o:this.editor.getContent(),typeof this.props.initialValue=="string"&&this.props.initialValue!==n.initialValue)this.editor.setContent(this.props.initialValue),this.editor.undoManager.clear(),this.editor.undoManager.add(),this.editor.setDirty(!1);else if(typeof this.props.value=="string"&&this.props.value!==this.currentContent){var x=this.editor;x.undoManager.transact(function(){var u;if(!a.inline||x.hasFocus())try{u=x.selection.getBookmark(3)}catch{}var N=a.valueCursor;if(x.setContent(a.props.value),!a.inline||x.hasFocus())for(var k=0,s=[u,N];k<s.length;k++){var w=s[k];if(w)try{x.selection.moveToBookmark(w),a.valueCursor=w;break}catch{}}})}if(this.props.readonly!==n.readonly){var j=(h=this.props.readonly)!==null&&h!==void 0?h:!1;Ta(this.editor,j?"readonly":"design")}this.props.disabled!==n.disabled&&(Sa(this.editor)?this.editor.options.set("disabled",this.props.disabled):Ta(this.editor,this.props.disabled?"readonly":"design"))}},t.prototype.componentDidMount=function(){var n=this,a,o,h,x,j;if(Ua(this.view)!==null)this.initialise();else if(Array.isArray(this.props.tinymceScriptSrc)&&this.props.tinymceScriptSrc.length===0)(o=(a=this.props).onScriptsLoadError)===null||o===void 0||o.call(a,new Error("No `tinymce` global is present but the `tinymceScriptSrc` prop was an empty array."));else if(!((h=this.elementRef.current)===null||h===void 0)&&h.ownerDocument){var u=function(){var k,s;(s=(k=n.props).onScriptsLoad)===null||s===void 0||s.call(k),n.initialise()},N=function(k){var s,w;(w=(s=n.props).onScriptsLoadError)===null||w===void 0||w.call(s,k)};wl.loadList(this.elementRef.current.ownerDocument,this.getScriptSources(),(j=(x=this.props.scriptLoading)===null||x===void 0?void 0:x.delay)!==null&&j!==void 0?j:0,u,N)}},t.prototype.componentWillUnmount=function(){var n=this,a=this.editor;a&&(a.off(Qt,this.handleEditorChange),a.off(this.beforeInputEvent(),this.handleBeforeInput),a.off("keypress",this.handleEditorChangeSpecial),a.off("keydown",this.handleBeforeInputSpecial),a.off("NewBlock",this.handleEditorChange),Object.keys(this.boundHandlers).forEach(function(o){a.off(o,n.boundHandlers[o])}),this.boundHandlers={},a.remove(),this.editor=void 0)},t.prototype.render=function(){return this.inline?this.renderInline():this.renderIframe()},t.prototype.beforeInputEvent=function(){return fl()?"beforeinput SelectionChange":"SelectionChange"},t.prototype.renderInline=function(){var n=this.props.tagName,a=n===void 0?"div":n;return b.createElement(a,{ref:this.elementRef,id:this.id,tabIndex:this.props.tabIndex})},t.prototype.renderIframe=function(){return b.createElement("textarea",{ref:this.elementRef,style:{visibility:"hidden"},name:this.props.textareaName,id:this.id,tabIndex:this.props.tabIndex})},t.prototype.getScriptSources=function(){var n,a,o=(n=this.props.scriptLoading)===null||n===void 0?void 0:n.async,h=(a=this.props.scriptLoading)===null||a===void 0?void 0:a.defer;if(this.props.tinymceScriptSrc!==void 0)return typeof this.props.tinymceScriptSrc=="string"?[{src:this.props.tinymceScriptSrc,async:o,defer:h}]:this.props.tinymceScriptSrc.map(function(N){return typeof N=="string"?{src:N,async:o,defer:h}:N});var x=this.props.cloudChannel,j=this.props.apiKey?this.props.apiKey:"no-api-key",u="https://cdn.tiny.cloud/1/".concat(j,"/tinymce/").concat(x,"/tinymce.min.js");return[{src:u,async:o,defer:h}]},t.prototype.getInitialValue=function(){return typeof this.props.initialValue=="string"?this.props.initialValue:typeof this.props.value=="string"?this.props.value:""},t.prototype.bindHandlers=function(n){var a=this;if(this.editor!==void 0){ul(this.editor,n,this.props,this.boundHandlers,function(j){return a.props[j]});var o=function(j){return j.onEditorChange!==void 0||j.value!==void 0},h=o(n),x=o(this.props);!h&&x?(this.editor.on(Qt,this.handleEditorChange),this.editor.on(this.beforeInputEvent(),this.handleBeforeInput),this.editor.on("keydown",this.handleBeforeInputSpecial),this.editor.on("keyup",this.handleEditorChangeSpecial),this.editor.on("NewBlock",this.handleEditorChange)):h&&!x&&(this.editor.off(Qt,this.handleEditorChange),this.editor.off(this.beforeInputEvent(),this.handleBeforeInput),this.editor.off("keydown",this.handleBeforeInputSpecial),this.editor.off("keyup",this.handleEditorChangeSpecial),this.editor.off("NewBlock",this.handleEditorChange))}},t.propTypes=bl,t.defaultProps={cloudChannel:"8"},t}(b.Component);const Sl=({isSave:i,errors:t,register:n,textEdit:a,setTextEdit:o,privacyPolicy:h,setPrivacyPolicy:x,setPrivacyPolicyHeaderBg:j,privacyPolicyHeaderBg:u,setTermsConditions:N,termsConditions:k,setTermsConditionsHeaderBg:s,termsConditionsHeaderBg:w,termsConditionsTextEdit:C,setTermsConditionsTextEdit:y,isSubmitting:B})=>{const{t:q}=ae();return e.jsx(e.Fragment,{children:e.jsxs("div",{className:"col-span-12 md:col-span-12 lg:col-span-12 pr-4",children:[e.jsx("div",{className:"sticky top-0 z-20 flex justify-end",children:B?e.jsxs(D.Button,{disabled:!0,type:"button",className:"h-10 px-6",children:[e.jsx("img",{src:fe,alt:"Loading",width:20,height:10})," ",e.jsxs("span",{className:"font-serif ml-2 font-light",children:[" ",q("Processing")]})]}):e.jsxs(D.Button,{type:"submit",className:"h-10 px-6 ",children:[" ",q(i?"SaveBtn":"UpdateBtn")]})}),e.jsxs("div",{className:"inline-flex md:text-lg text-base text-gray-800 font-semibold dark:text-gray-400 md:mb-3 mb-1",children:[e.jsx(U,{className:"mt-1 mx-2"}),q("PrivacyPolicyTermsTitle")]}),e.jsx("hr",{className:"md:mb-10 mb-4"}),e.jsxs("div",{className:"xl:px-10 flex-grow scrollbar-hide w-full max-h-full",children:[e.jsx("div",{className:"inline-flex md:text-base text-sm md:mb-3 text-gray-500 dark:text-gray-400",children:e.jsx("strong",{children:q("PrivacyPolicy")})}),e.jsx("hr",{className:"md:mb-12 mb-3"}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:q("EnableThisBlock")}),e.jsx("div",{className:"sm:col-span-4",children:e.jsx(S,{title:"",handleProcess:x,processOption:h,name:h})})]}),e.jsxs("div",{id:"description",className:"mb-height-0",style:{height:h?"auto":0,transition:"all 0.5s",visibility:h?"visible":"hidden",opacity:h?"1":"0"},children:[e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3 relative",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:q("PageHeaderBg")}),e.jsx("div",{className:"sm:col-span-4",children:e.jsx(O,{imageUrl:u,setImageUrl:j})})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3 relative",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:q("PageTitle")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:n,label:"Page Title",name:"privacy_page_title",type:"text",placeholder:q("PageTitle")}),e.jsx(c,{errorName:t.privacy_page_title})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3 relative",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:q("PageText")}),e.jsx("div",{className:"sm:col-span-4",children:e.jsx(Ca,{apiKey:"your-tinymce-api-key",value:a,onEditorChange:M=>o(M),init:{height:300,menubar:!1,plugins:["advlist autolink lists link image charmap print preview anchor","searchreplace visualblocks code fullscreen","insertdatetime media table paste code help wordcount"],toolbar:"undo redo | formatselect | bold italic backcolor |                     alignleft aligncenter alignright alignjustify |                     bullist numlist outdent indent | removeformat | help",content_style:"body { font-family:Helvetica,Arial,sans-serif; font-size:14px }"}})})]})]})]}),e.jsx("hr",{className:"md:mb-12 mb-3"}),e.jsxs("div",{className:"xl:px-10 flex-grow scrollbar-hide w-full max-h-full",children:[e.jsx("div",{className:"inline-flex md:text-base text-sm mb-3 text-gray-500 dark:text-gray-400",children:e.jsx("strong",{children:q("TermsConditions")})}),e.jsx("hr",{className:"md:mb-10 mb-3"}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:q("EnableThisBlock")}),e.jsx("div",{className:"sm:col-span-4",children:e.jsx(S,{title:"",handleProcess:N,processOption:k,name:k})})]}),e.jsxs("div",{style:{height:k?"auto":0,transition:"all 0.5s",visibility:k?"visible":"hidden",opacity:k?"1":"0"},children:[e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:q("PageHeaderBg")}),e.jsx("div",{className:"sm:col-span-4",children:e.jsx(O,{imageUrl:w,setImageUrl:s})})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:q("PageTitle")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:n,label:"Page Title",name:"termsConditions_page_title",type:"text",placeholder:q("PageTitle")}),e.jsx(c,{errorName:t.termsConditions_page_title})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:q("PageText")}),e.jsx("div",{className:"sm:col-span-4",children:e.jsx(Ca,{apiKey:"your-tinymce-api-key",value:C,onEditorChange:M=>y(M),init:{height:300,menubar:!1,plugins:["advlist autolink lists link image charmap print preview anchor","searchreplace visualblocks code fullscreen","insertdatetime media table paste code help wordcount"],toolbar:"undo redo | formatselect | bold italic backcolor |                     alignleft aligncenter alignright alignjustify |                     bullist numlist outdent indent | removeformat | help",content_style:"body { font-family:Helvetica,Arial,sans-serif; font-size:14px }"}})})]})]})]})]})})},Wt=({register:i,name:t,label:n,required:a})=>e.jsx(e.Fragment,{children:e.jsxs(D.Select,{name:t,...i(`${t}`,{required:a?`${n} is required!`:!1}),children:[e.jsx("option",{value:"",defaultValue:!0,hidden:!0,children:"Select Products Limit"}),e.jsx("option",{value:"6",children:"6"}),e.jsx("option",{value:"12",children:"12"}),e.jsx("option",{value:"18",children:"18"})]})}),Cl=({register:i,errors:t,coupons:n,headerLogo:a,setHeaderLogo:o,sliderImage:h,setSliderImage:x,sliderImageTwo:j,setSliderImageTwo:u,sliderImageThree:N,setSliderImageThree:k,sliderImageFour:s,setSliderImageFour:w,sliderImageFive:C,setSliderImageFive:y,placeholderImage:B,setPlaceHolderImage:q,quickSectionImage:M,setQuickSectionImage:L,getYourDailyNeedImageLeft:Q,setGetYourDailyNeedImageLeft:T,getYourDailyNeedImageRight:ve,setGetYourDailyNeedImageRight:Ne,footerLogo:we,setFooterLogo:Te,paymentImage:Se,setPaymentImage:Ce,isSave:Fe,isCoupon:z,isSliderFullWidth:v,setIsCoupon:F,setIsSliderFullWidth:I,featuredCategories:f,setFeaturedCategories:E,popularProducts:A,setPopularProducts:$,setQuickDelivery:je,quickDelivery:Z,setLatestDiscounted:H,latestDiscounted:W,setDailyNeeds:V,dailyNeeds:K,setFeaturePromo:R,featurePromo:J,setFooterBlock1:ye,footerBlock1:X,setFooterBlock2:le,footerBlock2:ee,setFooterBlock3:oe,footerBlock3:te,setFooterBlock4:Oe,footerBlock4:ie,setFooterSocialLinks:Be,footerSocialLinks:ne,setFooterPaymentMethod:Ie,footerPaymentMethod:re,allowPromotionBanner:de,setAllowPromotionBanner:Le,isSubmitting:De,setLeftRightArrow:Ee,leftRightArrow:Ue,setBottomDots:Ae,bottomDots:Me,setBothSliderOption:ns,bothSliderOption:rs,getButton1image:He,setGetButton1image:Re,getButton2image:ze,setGetButton2image:$e,setFooterBottomContact:Ke,footerBottomContact:me,setCategoriesMenuLink:Qe,categoriesMenuLink:ce,setAboutUsMenuLink:xe,aboutUsMenuLink:pe,setContactUsMenuLink:ge,contactUsMenuLink:be,setOffersMenuLink:_e,offersMenuLink:We,setFaqMenuLink:Ve,faqMenuLink:Ge,setPrivacyPolicyMenuLink:Ye,privacyPolicyMenuLink:Ze,setTermsConditionsMenuLink:Je,termsConditionsMenuLink:Xe,couponList:es,setCouponList:ss})=>{const{mode:ts}=b.useContext(D.WindmillContext),{t:r}=ae();return e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"sticky top-0 z-20 flex justify-end",children:De?e.jsxs(D.Button,{disabled:!0,type:"button",className:"h-10 px-6",children:[e.jsx("img",{src:fe,alt:"Loading",width:20,height:10})," ",e.jsxs("span",{className:"font-serif ml-2 font-light",children:[" ",r("Processing")]})]}):e.jsxs(D.Button,{type:"submit",className:"h-10 px-6 ",children:[" ",r(Fe?"SaveBtn":"UpdateBtn")]})}),e.jsxs("div",{className:"grid grid-cols-12 font-sans pr-4",children:[e.jsxs("div",{className:"col-span-12 md:col-span-12 lg:col-span-12",children:[e.jsxs("div",{className:"inline-flex md:text-lg text-base text-gray-800 font-semibold dark:text-gray-400 mb-3",children:[e.jsx(U,{className:"mt-1 mr-2"}),r("Header")]}),e.jsx("hr",{className:"md:mb-6 mb-3"}),e.jsxs("div",{className:"flex-grow scrollbar-hide w-full max-h-full xl:px-10",children:[e.jsx("div",{className:"inline-flex md:text-base text-sm my-3 text-gray-500 dark:text-gray-400",children:e.jsx("strong",{children:r("HeaderContacts")})}),e.jsx("hr",{className:"md:mb-12 mb-3"}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3 relative",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("HeaderText")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:i,label:r("HeaderText"),name:"help_text",type:"text",placeholder:r("weAreAvailable")}),e.jsx(c,{errorName:t.help_text})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3 relative",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("PhoneNumber")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:i,label:r("PhoneNumber"),name:"phone_number",type:"text",placeholder:"+01234560352"}),e.jsx(c,{errorName:t.phone_number})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3 relative",children:[e.jsx("label",{className:"block md:text-sm  md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("HeaderLogo")}),e.jsx("div",{className:"sm:col-span-4",children:e.jsx(O,{imageUrl:a,setImageUrl:o,targetWidth:87,targetHeight:25})})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 scrollbar-hide w-full max-h-full pb-0",children:[e.jsx("div",{className:"md:col-span-1 sm:col-span-2"}),e.jsxs("div",{className:"sm:col-span-4 md:pl-3 sm:pl-2",children:[e.jsx("div",{className:"inline-flex md:text-base text-sm mb-3 text-gray-500 dark:text-gray-400 relative",children:e.jsx("strong",{children:r("MenuEditor")})}),e.jsx("hr",{className:"md:mb-12 mb-3"}),e.jsxs("div",{className:"grid grid-cols-12 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsxs("div",{className:"col-span-4",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("Categories")}),e.jsx(g,{register:i,label:r("Categories"),name:"categories",type:"text",placeholder:r("Categories")}),e.jsx(c,{errorName:t.categories})]}),e.jsxs("div",{className:"col-span-4",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("AboutUs")}),e.jsx(g,{register:i,label:r("AboutUs"),name:"about_us",type:"text",placeholder:r("AboutUs")}),e.jsx(c,{errorName:t.about_us})]}),e.jsxs("div",{className:"col-span-4",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("ContactUs")}),e.jsx(g,{register:i,label:r("ContactUs"),name:"contact_us",type:"text",placeholder:r("ContactUs")}),e.jsx(c,{errorName:t.contact_us})]}),e.jsxs("div",{className:"col-span-4",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("Offers")}),e.jsx(g,{register:i,label:r("Offers"),name:"offers",type:"text",placeholder:r("Offers")}),e.jsx(c,{errorName:t.offers})]}),e.jsxs("div",{className:"col-span-4",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("FAQ")}),e.jsx(g,{register:i,label:r("FAQ"),name:"faq",type:"text",placeholder:r("FAQ")}),e.jsx(c,{errorName:t.faq})]}),e.jsxs("div",{className:"col-span-4",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("PrivacyPolicy")}),e.jsx(g,{register:i,label:r("PrivacyPolicy"),name:"privacy_policy",type:"text",placeholder:r("PrivacyPolicy")}),e.jsx(c,{errorName:t.privacy_policy})]}),e.jsxs("div",{className:"col-span-4",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("TermsConditions")}),e.jsx(g,{register:i,label:r("TermsConditions"),name:"term_and_condition",type:"text",placeholder:r("TermsConditions")}),e.jsx(c,{errorName:t.term_and_condition})]}),e.jsxs("div",{className:"col-span-4",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("Pages")}),e.jsx(g,{register:i,label:r("Pages"),name:"pages",type:"text",placeholder:r("Pages")}),e.jsx(c,{errorName:t.pages})]}),e.jsxs("div",{className:"col-span-4",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("MyAccount")}),e.jsx(g,{register:i,label:r("MyAccount"),name:"my_account",type:"text",placeholder:r("MyAccount")}),e.jsx(c,{errorName:t.my_account})]}),e.jsxs("div",{className:"col-span-4",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("Login")}),e.jsx(g,{register:i,label:r("Login"),name:"login",type:"text",placeholder:r("Login")}),e.jsx(c,{errorName:t.login})]}),e.jsxs("div",{className:"col-span-4",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("Logout")}),e.jsx(g,{register:i,label:r("Logout"),name:"logout",type:"text",placeholder:r("Logout")}),e.jsx(c,{errorName:t.logout})]}),e.jsxs("div",{className:"col-span-4",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("CheckOut")}),e.jsx(g,{register:i,label:r("CheckOut"),name:"checkout",type:"text",placeholder:r("CheckOut")}),e.jsx(c,{errorName:t.checkout})]})]}),e.jsxs("div",{className:"grid xl:grid-cols-4 md:grid-cols-3 grid-cols-2 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium font-serif md:text-base text-sm mb-2 dark:text-gray-300",children:r("Categories")}),e.jsx(S,{title:"",handleProcess:Qe,processOption:ce})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium font-serif md:text-base text-sm mb-2 dark:text-gray-300",children:r("AboutUs")}),e.jsx(S,{title:"",handleProcess:xe,processOption:pe})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium font-serif md:text-base text-sm mb-2 dark:text-gray-300",children:r("ContactUs")}),e.jsx(S,{title:"",handleProcess:ge,processOption:be})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium font-serif md:text-base text-sm mb-2 dark:text-gray-300",children:r("Offers")}),e.jsx(S,{title:"",handleProcess:_e,processOption:We})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium font-serif md:text-base text-sm mb-2 dark:text-gray-300",children:r("FAQ")}),e.jsx(S,{title:"",handleProcess:Ve,processOption:Ge})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium font-serif md:text-base text-sm mb-2 dark:text-gray-300",children:r("PrivacyPolicy")}),e.jsx(S,{title:"",handleProcess:Ye,processOption:Ze})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium font-serif md:text-base text-sm mb-2 dark:text-gray-300",children:r("TermsConditions")}),e.jsx(S,{title:"",handleProcess:Je,processOption:Xe})]})]})]})]})]}),e.jsxs("div",{className:"col-span-12 md:col-span-12 lg:col-span-12 mt-5",children:[e.jsxs("div",{className:"inline-flex md:text-lg text-base text-gray-800 font-semibold dark:text-gray-400 mb-3",children:[e.jsx(U,{className:"mt-1 mr-2"})," ",r("MainSlider")]}),e.jsx("hr",{className:"mb-3"}),e.jsx("div",{className:"flex-grow scrollbar-hide w-full max-h-full xl:px-10",children:e.jsx(is,{children:e.jsxs(is,{children:[e.jsxs(Rt,{children:[e.jsxs(G,{children:[r("Slider")," 1"]}),e.jsxs(G,{children:[r("Slider")," 2"]}),e.jsxs(G,{children:[r("Slider")," 3"]}),e.jsxs(G,{children:[r("Slider")," 4"]}),e.jsxs(G,{children:[r("Slider")," 5"]}),e.jsx(G,{children:r("Options")})]}),e.jsxs(Y,{className:"md:mt-10 mt-3",children:[e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3 relative",children:[e.jsx("label",{className:"block md:text-sm  md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("SliderImages")}),e.jsx("div",{className:"sm:col-span-4",children:e.jsx(O,{imageUrl:h,setImageUrl:x,targetWidth:950,targetHeight:400})})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3 relative",children:[e.jsx("label",{className:"block md:text-sm  md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1 ",children:r("SliderTitle")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{required:!0,register:i,label:r("SliderTitle"),name:"slider_title",type:"text",placeholder:r("SliderTitle")}),e.jsx(c,{errorName:t.slider_title})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3 relative",children:[e.jsx("label",{className:"block md:text-sm  md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("SliderDescription")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(P,{required:!0,register:i,label:"Slider Description",name:"slider_description",type:"text",placeholder:"Slider Description"}),e.jsx(c,{errorName:t.slider_description})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3 relative",children:[e.jsx("label",{className:"block md:text-sm  md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("SliderButtonName")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{required:!0,register:i,label:r("SliderButtonName"),name:"slider_button_name",type:"text",placeholder:r("SliderButtonName")}),e.jsx(c,{errorName:t.slider_button_name})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3 relative",children:[e.jsx("label",{className:"block md:text-sm  md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("SliderButtonLink")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{required:!0,register:i,label:"Slider Button Link",name:"slider_button_link",type:"text",placeholder:"Slider Button Link"}),e.jsx(c,{errorName:t.slider_button_link})]})]})]}),e.jsxs(Y,{children:[e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm  md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("SliderImages")}),e.jsx("div",{className:"sm:col-span-4",children:e.jsx(O,{imageUrl:j,setImageUrl:u,targetWidth:950,targetHeight:400})})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm  md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("SliderTitle")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:i,label:"Slider Title",name:"slider_title_two",type:"text",placeholder:r("SliderTitle")}),e.jsx(c,{errorName:t.slider_title_two})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm  md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("SliderDescription")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(P,{register:i,label:"Slider Description Two",name:"slider_description_two",type:"text",placeholder:r("SliderDescription")}),e.jsx(c,{errorName:t.slider_description_two})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm  md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("SliderButtonName")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:i,label:"Slider Button Name",name:"slider_button_name_two",type:"text",placeholder:r("SliderButtonName")}),e.jsx(c,{errorName:t.slider_button_name_two})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm  md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("SliderButtonLink")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:i,label:"Slider Button Link",name:"slider_button_link_two",type:"text",placeholder:r("SliderButtonLink")}),e.jsx(c,{errorName:t.slider_button_link_two})]})]})]}),e.jsxs(Y,{children:[e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm  md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("SliderImages")}),e.jsx("div",{className:"sm:col-span-4",children:e.jsx(O,{imageUrl:N,setImageUrl:k,targetWidth:950,targetHeight:400})})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("SliderTitle")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:i,label:" Slider Title",name:"slider_title_three",type:"text",placeholder:r("SliderTitle")}),e.jsx(c,{errorName:t.slider_title_three})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("SliderDescription")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(P,{register:i,label:"Slider Description",name:"slider_description_three",type:"text",placeholder:r("SliderDescription")}),e.jsx(c,{errorName:t.slider_description_three})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("SliderButtonName")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:i,label:"Slider Button Name",name:"slider_button_name_three",type:"text",placeholder:r("SliderButtonName")}),e.jsx(c,{errorName:t.slider_button_name_three})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("SliderButtonLink")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:i,label:"Slider Button Link",name:"slider_button_link_three",type:"text",placeholder:r("SliderButtonLink")}),e.jsx(c,{errorName:t.slider_button_link_three})]})]})]}),e.jsxs(Y,{children:[e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("SliderImages")}),e.jsx("div",{className:"sm:col-span-4",children:e.jsx(O,{imageUrl:s,setImageUrl:w,targetWidth:950,targetHeight:400})})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("SliderTitle")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:i,label:" Slider Title",name:"slider_title_four",type:"text",placeholder:r("SliderTitle")}),e.jsx(c,{errorName:t.slider_title_four})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("SliderDescription")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(P,{register:i,label:"Slider Description",name:"slider_description_four",type:"text",placeholder:r("SliderDescription")}),e.jsx(c,{errorName:t.slider_description_four})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("SliderButtonName")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:i,label:"Slider Button Name",name:"slider_button_name_four",type:"text",placeholder:r("SliderButtonName")}),e.jsx(c,{errorName:t.slider_button_name_four})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("SliderButtonLink")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:i,label:"Slider Button Link",name:"slider_button_link_four",type:"text",placeholder:r("SliderButtonLink")}),e.jsx(c,{errorName:t.slider_button_link_four})]})]})]}),e.jsxs(Y,{children:[e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("SliderImages")}),e.jsx("div",{className:"sm:col-span-4",children:e.jsx(O,{imageUrl:C,setImageUrl:y,targetWidth:950,targetHeight:400})})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("SliderTitle")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:i,label:" Slider Title",name:"slider_title_five",type:"text",placeholder:r("SliderTitle")}),e.jsx(c,{errorName:t.slider_title_five})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("SliderDescription")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(P,{register:i,label:"Slider Description",name:"slider_description_five",type:"text",placeholder:r("SliderDescription")}),e.jsx(c,{errorName:t.slider_description_five})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("SliderButtonName")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:i,label:"Slider Button Name",name:"slider_button_name_five",type:"text",placeholder:r("SliderButtonName")}),e.jsx(c,{errorName:t.slider_button_name_five})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("SliderButtonLink")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:i,label:"Slider Button Link",name:"slider_button_link_five",type:"text",placeholder:r("SliderButtonLink")}),e.jsx(c,{errorName:t.slider_button_link_five})]})]})]}),e.jsx(Y,{children:e.jsxs("div",{className:"grid md:grid-cols-3 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsxs("div",{children:[e.jsx("div",{className:"relative",children:e.jsxs("h4",{className:"font-medium md:text-base text-sm mb-2 dark:text-gray-400",children:[" ",r("LeftRighArrows")]})}),e.jsx(S,{title:"",handleProcess:Ee,processOption:Ue})]}),e.jsxs("div",{children:[e.jsx("div",{className:"relative",children:e.jsx("h4",{className:"font-medium md:text-base text-sm mb-2 dark:text-gray-400",children:r("BottomDots")})}),e.jsx(S,{title:"",handleProcess:Ae,processOption:Me})]}),e.jsxs("div",{children:[e.jsx("div",{className:"relative",children:e.jsx("h4",{className:"font-medium md:text-base text-sm mb-2 dark:text-gray-400",children:r("Both")})}),e.jsx(S,{title:"",handleProcess:ns,processOption:rs})]})]})})]})})})]}),e.jsxs("div",{className:"col-span-12 md:col-span-12 lg:col-span-12 mt-5",children:[e.jsxs("div",{className:"inline-flex md:text-lg text-base text-gray-800 font-semibold dark:text-gray-400 mb-3",children:[e.jsx(U,{className:"mt-1 mr-2"}),r("DiscountCouponTitle1")]}),e.jsx("hr",{className:"md:mb-12 mb-3"}),e.jsxs("div",{className:"xl:px-10 flex-grow scrollbar-hide w-full max-h-full",children:[e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("ShowHide")}),e.jsx("div",{className:"sm:col-span-4",children:e.jsx(S,{title:"",handleProcess:F,processOption:z,name:"isCoupon"})})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 relative transition-2",style:{height:z?"auto":0,transition:"ease-out 0.4s",visibility:z?"visible":"hidden",opacity:z?"1":"0"},children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("HomePageDiscountTitle")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:i,label:r("HomePageDiscountTitle"),name:"discount_title",type:"text",placeholder:r("HomePageDiscountTitle")}),e.jsx(c,{errorName:t.phone_number})]}),e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("SuperDiscountActiveCouponCode")}),e.jsx("div",{className:"sm:col-span-4",children:e.jsx(Pa,{options:n,value:es,className:ts,onChange:as=>ss(as),labelledBy:"Select Coupon"})})]}),e.jsxs("div",{style:{height:"auto",transition:"all 0.5s",visibility:z?"hidden":"visible",opacity:z?"0":"1"},children:[e.jsxs("div",{children:[e.jsx("div",{className:"inline-flex md:text-base text-sm mb-3 text-gray-500 dark:text-gray-400",children:e.jsx("div",{className:"relative",children:e.jsx("strong",{children:r("SliderFullWidth")})})}),e.jsx("hr",{className:"mb-4 mt-2"}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3 ",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("SliderFullWidth")}),e.jsx("div",{className:"sm:col-span-4 ",children:e.jsx(S,{title:"",handleProcess:I,processOption:v,name:v})})]})]}),!v&&!z&&e.jsxs("div",{children:[e.jsx("div",{className:"inline-flex md:text-base text-sm mb-3 text-gray-500 dark:text-gray-400 mt-5 ",children:e.jsx("div",{className:"relative",children:e.jsxs("strong",{children:[" ",r("PlaceHolderImage")," "]})})}),e.jsx("hr",{className:"mb-4 mt-2"}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mt-4 md:mb-6 mb-3 pb-2",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("PlaceHolderImage")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(O,{imageUrl:B,setImageUrl:q}),e.jsx("div",{className:"text-xs text-center text-gray-400",children:e.jsxs("em",{children:["( ",r("ImagesResolution")," )"]})})]})]})]})]})]})]}),e.jsxs("div",{className:"col-span-12 md:col-span-12 lg:col-span-12",children:[e.jsxs("div",{className:"inline-flex md:text-lg text-base text-gray-800 font-semibold dark:text-gray-400 mb-3 md:mt-0 mt-10",children:[e.jsx(U,{className:"mt-1 mr-2"})," ",r("PromotionBanner")]}),e.jsx("hr",{className:"md:mb-12 mb-3"}),e.jsxs("div",{className:"xl:px-10 flex-grow scrollbar-hide w-full max-h-full pb-0",children:[e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("EnableThisBlock")}),e.jsx("div",{className:"sm:col-span-4",children:e.jsx(S,{title:"",handleProcess:Le,processOption:de,name:de})})]}),e.jsxs("div",{style:{height:de?"auto":0,transition:"all 0.4s",visibility:de?"visible":"hidden",opacity:de?"1":"0"},children:[e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3 relative",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("Title")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:i,label:"Title",name:"promotion_title",type:"text",placeholder:r("Title")}),e.jsx(c,{errorName:t.promotion_title})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3 relative",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("Description")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(P,{register:i,label:"Promotion Description",name:"promotion_description",type:"text",placeholder:r("PromotionDescription")}),e.jsx(c,{errorName:t.promotion_description})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3 relative",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("ButtonName")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:i,label:"Button Name",name:"promotion_button_name",type:"text",placeholder:r("ButtonName")}),e.jsx(c,{errorName:t.promotion_button_name})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3 relative",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("ButtonLink")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:i,label:"Button Link ",name:"promotion_button_link",type:"text",placeholder:"https://gloopi-store.vercel.app/search?category=fruits-vegetable&_id=632aca2b4d87ff2494210be8"}),e.jsx(c,{errorName:t.promotion_button_link})]})]})]})]})]}),e.jsxs("div",{className:"col-span-12 md:col-span-12 lg:col-span-12 md:mt-0 mt-15",children:[e.jsxs("div",{className:"inline-flex md:text-lg text-base text-gray-800 font-semibold dark:text-gray-400 mb-3 relative",children:[e.jsx(U,{className:"mt-1 mr-2"})," ",r("FeaturedCategories")]}),e.jsx("hr",{className:"md:mb-12 mb-3"}),e.jsxs("div",{className:"xl:px-10 flex-grow scrollbar-hide w-full max-h-full pb-0",children:[e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("EnableThisBlock")}),e.jsx("div",{className:"sm:col-span-4",children:e.jsx(S,{title:"",handleProcess:E,processOption:f,name:f})})]}),e.jsxs("div",{style:{height:f?"auto":0,transition:"all 0.5s",visibility:f?"visible":"hidden",opacity:f?"1":"0"},children:[e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("Title")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:i,label:"Title",name:"feature_title",type:"text",placeholder:r("Title")}),e.jsx(c,{errorName:t.feature_title})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("FeaturedCategories")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(P,{register:i,label:"Feature Description",name:"feature_description",type:"text",placeholder:r("FeaturedCategories")}),e.jsx(c,{errorName:t.feature_description})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("ProductsLimit")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(Wt,{register:i,required:!0,label:"Feature Products Limit",name:"feature_product_limit"}),e.jsx(c,{errorName:t.feature_product_limit})]})]})]})]})]}),e.jsxs("div",{className:"col-span-12 md:col-span-12 lg:col-span-12 md:mt-0 mt-15",children:[e.jsxs("div",{className:"inline-flex md:text-lg text-base text-gray-800 font-semibold dark:text-gray-400 mb-3 relative",children:[e.jsx(U,{className:"mt-1 mr-2"}),r("PopularProductsTitle")]}),e.jsx("hr",{className:"md:mb-12 mb-3"}),e.jsxs("div",{className:"xl:px-10 flex-grow scrollbar-hide w-full max-h-full pb-0",children:[e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("EnableThisBlock")}),e.jsx("div",{className:"sm:col-span-4",children:e.jsx(S,{title:"",handleProcess:$,processOption:A,name:A})})]}),e.jsxs("div",{style:{height:A?"auto":0,transition:"all 0.5s",visibility:A?"visible":"hidden",opacity:A?"1":"0"},children:[e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("Title")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:i,label:"Title",name:"popular_title",type:"text",placeholder:r("Title")}),e.jsx(c,{errorName:t.popular_title})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("Description")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(P,{register:i,label:"Popular Description",name:"popular_description",type:"text",placeholder:r("PopularDescription")}),e.jsx(c,{errorName:t.popular_description})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("ProductsLimit")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(Wt,{register:i,required:!0,label:"Popular Products Limit",name:"popular_product_limit"}),e.jsx(c,{errorName:t.popular_product_limit})]})]})]})]})]}),e.jsxs("div",{className:"col-span-12 md:col-span-12 lg:col-span-12 mt-15",children:[e.jsxs("div",{className:"inline-flex md:text-lg text-base text-gray-800 font-semibold dark:text-gray-400 mb-3",children:[e.jsx(U,{className:"mt-1 mr-2"})," ",r("QuickDeliverySectionTitle")]}),e.jsx("hr",{className:"md:mb-12 mb-3"}),e.jsxs("div",{className:"xl:px-10 flex-grow scrollbar-hide w-full max-h-full pb-0",children:[e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("EnableThisBlock")}),e.jsx("div",{className:"sm:col-span-4",children:e.jsx(S,{title:"",handleProcess:je,processOption:Z,name:Z})})]}),e.jsxs("div",{style:{height:Z?"auto":0,transition:"all 0.5s",visibility:Z?"visible":"hidden",opacity:Z?"1":"0"},children:[e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3 relative",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("SubTitle")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:i,label:"Title",name:"quick_delivery_subtitle",type:"text",placeholder:r("SubTitle")}),e.jsx(c,{errorName:t.quick_delivery_subtitle})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3 relative",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("Title")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:i,label:"Title",name:"quick_delivery_title",type:"text",placeholder:r("Title")}),e.jsx(c,{errorName:t.quick_delivery_title})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3 relative",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("Description")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(P,{register:i,label:"Quick Delivery Description",name:"quick_delivery_description",type:"text",placeholder:r("QuickDeliverySectionTitle")}),e.jsx(c,{errorName:t.quick_delivery_description})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3 relative",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("ButtonName")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:i,label:"Button Name ",name:"quick_delivery_button",type:"text",placeholder:r("ButtonName")}),e.jsx(c,{errorName:t.quick_delivery_button})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3 relative",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("ButtonLink")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:i,label:"Button Link",name:"quick_delivery_link",type:"text",placeholder:"https://gloopi-store.vercel.app/search?category=fruits-vegetable&_id=632aca2b4d87ff2494210be8"}),e.jsx(c,{errorName:t.quick_delivery_link})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3 relative",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("Image")}),e.jsx("div",{className:"sm:col-span-4",children:e.jsx(O,{imageUrl:M,setImageUrl:L})})]})]})]})]}),e.jsxs("div",{className:"col-span-12 md:col-span-12 lg:col-span-12 md:mt-0 mt-10",children:[e.jsxs("div",{className:"inline-flex md:text-lg text-base text-gray-800 font-semibold dark:text-gray-400 mb-3",children:[e.jsx(U,{className:"mt-1 mr-2"})," ",r("LatestDiscountedProductsTitle")]}),e.jsx("hr",{className:"md:mb-12 mb-3"}),e.jsxs("div",{className:"xl:px-10 flex-grow scrollbar-hide w-full max-h-full pb-0",children:[e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("EnableThisBlock")}),e.jsx("div",{className:"sm:col-span-4",children:e.jsx(S,{title:"",handleProcess:H,processOption:W,name:W})})]}),e.jsxs("div",{style:{height:W?"auto":0,transition:"all 0.5s",visibility:W?"visible":"hidden",opacity:W?"1":"0"},children:[e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("Title")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:i,label:"Title",name:"latest_discount_title",type:"text",placeholder:r("Title")}),e.jsx(c,{errorName:t.latest_discount_title})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("Description")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(P,{register:i,label:"Latest Discount Description",name:"latest_discount_description",type:"text",placeholder:r("LatestDiscountDescription")}),e.jsx(c,{errorName:t.latest_discount_description})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("ProductsLimit")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(Wt,{register:i,required:!0,label:"Latest Discount Products Limit",name:"latest_discount_product_limit"}),e.jsx(c,{errorName:t.latest_discount_product_limit})]})]})]})]})]}),e.jsxs("div",{className:`col-span-12 md:col-span-12 lg:col-span-12 ${window.innerWidth<400,"md:my-0 my-24"}`,children:[e.jsxs("div",{className:"inline-flex md:text-lg text-base text-gray-800 font-semibold dark:text-gray-400 mb-3",children:[e.jsx(U,{className:"mt-1 mr-2"})," ",r("GetYourDailyNeedsTitle")]}),e.jsx("hr",{className:"md:mb-12 mb-3"}),e.jsxs("div",{className:"xl:px-10 flex-grow scrollbar-hide w-full max-h-full pb-0",children:[e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("EnableThisBlock")}),e.jsx("div",{className:"sm:col-span-4",children:e.jsx(S,{title:"",handleProcess:V,processOption:K,name:K})})]}),e.jsxs("div",{style:{height:K?"auto":0,transition:"all 0.5s",visibility:K?"visible":"hidden",opacity:K?"1":"0"},children:[e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3 relative",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("Title")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:i,label:"Title",name:"daily_need_title",type:"text",placeholder:r("Title")}),e.jsx(c,{errorName:t.daily_need_title})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3 relative",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("Description")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(P,{register:i,label:"Daily Need Description",name:"daily_need_description",type:"text",placeholder:r("DailyNeedDescription")}),e.jsx(c,{errorName:t.daily_need_description})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3 relative",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("ImageLeft")}),e.jsx("div",{className:"sm:col-span-4",children:e.jsx(O,{imageUrl:Q,setImageUrl:T})})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3 relative",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("ImageRight")}),e.jsx("div",{className:"sm:col-span-4",children:e.jsx(O,{imageUrl:ve,setImageUrl:Ne})})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3 relative",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("Button1image")}),e.jsx("div",{className:"sm:col-span-4",children:e.jsx(O,{imageUrl:He,setImageUrl:Re})})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("Button1Link")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:i,label:"Button Link ",name:"daily_need_app_link",type:"text",placeholder:"https://gloopi-store.vercel.app/search?category=fruits-vegetable&_id=632aca2b4d87ff2494210be8"}),e.jsx(c,{errorName:t.daily_need_app_link})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3 relative",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("Button2image")}),e.jsx("div",{className:"sm:col-span-4",children:e.jsx(O,{imageUrl:ze,setImageUrl:$e})})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("Button2Link")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:i,label:"Button Link ",name:"daily_need_google_link",type:"text",placeholder:"https://gloopi-store.vercel.app/search?category=fruits-vegetable&_id=632aca2b4d87ff2494210be8"}),e.jsx(c,{errorName:t.daily_need_google_link})]})]})]})]})]}),e.jsxs("div",{className:`col-span-12 md:col-span-12 lg:col-span-12 ${window.innerWidth<400?"md:mt-0 mt-40":"md:mt-0 mt-10"}`,children:[e.jsxs("div",{className:"inline-flex md:text-lg text-base text-gray-800 font-semibold dark:text-gray-400 mb-3",children:[e.jsx(U,{className:"mt-1 mr-2"})," ",r("FeaturePromoSectionTitle")]}),e.jsx("hr",{className:"md:mb-12 mb-3"}),e.jsxs("div",{className:"xl:px-10 flex-grow scrollbar-hide w-full max-h-full pb-0",children:[e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("EnableThisBlock")}),e.jsx("div",{className:"sm:col-span-4",children:e.jsx(S,{title:"",handleProcess:R,processOption:J,name:J})})]}),e.jsxs("div",{style:{height:J?"auto":0,transition:"all 0.5s",visibility:J?"visible":"hidden",opacity:J?"1":"0"},children:[e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3 relative",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("FreeShipping")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:i,label:"Title",name:"promo_free_shipping",type:"text",placeholder:"From $500.00"}),e.jsx(c,{errorName:t.promo_free_shipping})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3 relative",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("Support")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:i,label:"Title",name:"promo_support",type:"text",placeholder:"24/7 At Anytime"}),e.jsx(c,{errorName:t.promo_support})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3 relative",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("SecurePayment")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:i,label:"Title",name:"promo_payment",type:"text",placeholder:r("SecurePayment")}),e.jsx(c,{errorName:t.promo_payment})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3 relative",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("LatestOffer")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:i,label:"Title",name:"promo_offer",type:"text",placeholder:"Upto 20% Off"}),e.jsx(c,{errorName:t.promo_offer})]})]})]})]})]}),e.jsxs("div",{className:"col-span-12 md:col-span-12 lg:col-span-12 md:mt-0 mt-10",children:[e.jsxs("div",{className:"inline-flex md:text-lg text-base text-gray-800 font-semibold dark:text-gray-400 mb-3",children:[e.jsx(U,{className:"mt-1 mr-2"})," ",r("FooterTitle")]}),e.jsx("hr",{className:"md:mb-12 mb-3"}),e.jsxs("div",{className:"xl:px-10 flex-grow scrollbar-hide w-full max-h-full pb-0",children:[e.jsx("div",{className:"inline-flex md:text-base text-sm mb-3 text-gray-500 dark:text-gray-400 relative",children:e.jsxs("strong",{children:[r("Block")," 1"]})}),e.jsx("hr",{className:"md:mb-12 mb-3"}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("EnableThisBlock")}),e.jsx("div",{className:"sm:col-span-4",children:e.jsx(S,{title:"",handleProcess:ye,processOption:X,name:X})})]}),e.jsxs("div",{style:{height:X?"auto":0,transition:"all 0.5s",visibility:X?"visible":"hidden",opacity:X?"1":"0"},children:[e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-4",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("Title")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:i,label:"Title",name:"footer_block_one_title",type:"text",placeholder:"Company"}),e.jsx(c,{errorName:t.footer_block_one_title})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-1",children:[e.jsxs("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:[r("Link")," 1"]}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:i,label:"Title",name:"footer_block_one_link_one_title",type:"text",placeholder:r("AboutUs")}),e.jsx(c,{errorName:t.footer_block_one_link_one_title})]}),e.jsx("label",{className:"md:col-span-1 sm:col-span-2"}),e.jsxs("div",{className:"sm:col-span-4 mb-5",children:[e.jsx(g,{register:i,label:"Title",name:"footer_block_one_link_one",type:"text",placeholder:r("Link")}),e.jsx(c,{errorName:t.footer_block_one_link_one})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-1",children:[e.jsxs("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:[r("Link")," 2"]}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:i,label:"Title",name:"footer_block_one_link_two_title",type:"text",placeholder:r("ContactUs")}),e.jsx(c,{errorName:t.footer_block_one_link_two_title})]}),e.jsx("label",{className:"md:col-span-1 sm:col-span-2"}),e.jsxs("div",{className:"sm:col-span-4  mb-5",children:[e.jsx(g,{register:i,label:"Title",name:"footer_block_one_link_two",type:"text",placeholder:r("Link")}),e.jsx(c,{errorName:t.footer_block_one_link_two})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-1",children:[e.jsxs("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:[r("Link")," 3"]}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:i,label:"Title",name:"footer_block_one_link_three_title",type:"text",placeholder:r("Careers")}),e.jsx(c,{errorName:t.footer_block_one_link_three_title})]}),e.jsx("label",{className:"md:col-span-1 sm:col-span-2"}),e.jsxs("div",{className:"sm:col-span-4  mb-5",children:[e.jsx(g,{register:i,label:"Title",name:"footer_block_one_link_three",type:"text",placeholder:r("Link")}),e.jsx(c,{errorName:t.footer_block_one_link_three})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-1",children:[e.jsxs("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:[r("Link")," 4"]}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:i,label:"Title",name:"footer_block_one_link_four_title",type:"text",placeholder:r("LatestNews")}),e.jsx(c,{errorName:t.footer_block_one_link_four_title})]}),e.jsx("label",{className:"md:col-span-1 sm:col-span-2"}),e.jsxs("div",{className:"sm:col-span-4 mb-5",children:[e.jsx(g,{register:i,label:"Title",name:"footer_block_one_link_four",type:"text",placeholder:r("Link")}),e.jsx(c,{errorName:t.footer_block_one_link_four})]})]})]}),e.jsx("div",{className:"inline-flex md:text-base text-sm mb-3 text-gray-500 dark:text-gray-400 relative md:mt-0 mt-24",children:e.jsxs("strong",{children:[r("Block")," 2"]})}),e.jsx("hr",{className:"md:mb-12 mb-3"}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("EnableThisBlock")}),e.jsx("div",{className:"sm:col-span-4",children:e.jsx(S,{title:"",handleProcess:le,processOption:ee,name:ee})})]}),e.jsxs("div",{style:{height:ee?"auto":0,transition:"all 0.5s",visibility:ee?"visible":"hidden",opacity:ee?"1":"0"},children:[e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("Title")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:i,label:"Title",name:"footer_block_two_title",type:"text",placeholder:r("TopCategory")}),e.jsx(c,{errorName:t.footer_block_two_title})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-1",children:[e.jsxs("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:[r("Link")," 1"]}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:i,label:"Title",name:"footer_block_two_link_one_title",type:"text",placeholder:r("FishAndMeat")}),e.jsx(c,{errorName:t.footer_block_two_link_one_title})]}),e.jsx("label",{className:"md:col-span-1 sm:col-span-2"}),e.jsxs("div",{className:"sm:col-span-4  mb-5",children:[e.jsx(g,{register:i,label:"Title",name:"footer_block_two_link_one",type:"text",placeholder:r("Link")}),e.jsx(c,{errorName:t.footer_block_two_link_one})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-1",children:[e.jsxs("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:[r("Link")," 2"]}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:i,label:"Title",name:"footer_block_two_link_two_title",type:"text",placeholder:r("SoftDrinks")}),e.jsx(c,{errorName:t.footer_block_two_link_two_title})]}),e.jsx("label",{className:"md:col-span-1 sm:col-span-2"}),e.jsxs("div",{className:"sm:col-span-4  mb-5",children:[e.jsx(g,{register:i,label:"Title",name:"footer_block_two_link_two",type:"text",placeholder:r("Link")}),e.jsx(c,{errorName:t.footer_block_two_link_two})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-1",children:[e.jsxs("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:[r("Link")," 3"]}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:i,label:"Title",name:"footer_block_two_link_three_title",type:"text",placeholder:r("BabyCare")}),e.jsx(c,{errorName:t.footer_block_two_link_three_title})]}),e.jsx("label",{className:"md:col-span-1 sm:col-span-2"}),e.jsxs("div",{className:"sm:col-span-4  mb-5",children:[e.jsx(g,{register:i,label:"Title",name:"footer_block_two_link_three",type:"text",placeholder:r("Link")}),e.jsx(c,{errorName:t.footer_block_two_link_three})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-1",children:[e.jsxs("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:[r("Link")," 4"]}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:i,label:"Title",name:"footer_block_two_link_four_title",type:"text",placeholder:r("BeautyAndHealth")}),e.jsx(c,{errorName:t.footer_block_two_link_four_title})]}),e.jsx("label",{className:"md:col-span-1 sm:col-span-2"}),e.jsxs("div",{className:"sm:col-span-4  mb-5",children:[e.jsx(g,{register:i,label:"Title",name:"footer_block_two_link_four",type:"text",placeholder:r("Link")}),e.jsx(c,{errorName:t.footer_block_two_link_four})]})]})]}),e.jsx("div",{className:"inline-flex md:text-base text-sm mb-3 text-gray-500 dark:text-gray-400 relative md:mt-0 mt-24",children:e.jsxs("strong",{children:[r("Block")," 3"]})}),e.jsx("hr",{className:"md:mb-12 mb-3"}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("EnableThisBlock")}),e.jsx("div",{className:"sm:col-span-4",children:e.jsx(S,{title:"",handleProcess:oe,processOption:te,name:te})})]}),e.jsxs("div",{style:{height:te?"auto":0,transition:"all 0.5s",visibility:te?"visible":"hidden",opacity:te?"1":"0"},children:[e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("Title")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:i,label:"Title",name:"footer_block_three_title",type:"text",placeholder:"My Account"}),e.jsx(c,{errorName:t.footer_block_three_title})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-1",children:[e.jsxs("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:[r("Link")," 1"]}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:i,label:"Title",name:"footer_block_three_link_one_title",type:"text",placeholder:r("Dashboard")}),e.jsx(c,{errorName:t.footer_block_three_link_one_title})]}),e.jsx("label",{className:"md:col-span-1 sm:col-span-2"}),e.jsxs("div",{className:"sm:col-span-4  mb-5",children:[e.jsx(g,{register:i,label:"Title",name:"footer_block_three_link_one",type:"text",placeholder:r("Link")}),e.jsx(c,{errorName:t.footer_block_three_link_one})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-1",children:[e.jsxs("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:[r("Link")," 2"]}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:i,label:"Title",name:"footer_block_three_link_two_title",type:"text",placeholder:r("MyOrders")}),e.jsx(c,{errorName:t.footer_block_three_link_two_title})]}),e.jsx("label",{className:"md:col-span-1 sm:col-span-2"}),e.jsxs("div",{className:"sm:col-span-4  mb-5",children:[e.jsx(g,{register:i,label:"Title",name:"footer_block_three_link_two",type:"text",placeholder:r("Link")}),e.jsx(c,{errorName:t.footer_block_three_link_two})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-1",children:[e.jsxs("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:[r("Link")," 3"]}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:i,label:"Title",name:"footer_block_three_link_three_title",type:"text",placeholder:"Recent Orders"}),e.jsx(c,{errorName:t.footer_block_three_link_three_title})]}),e.jsx("label",{className:"md:col-span-1 sm:col-span-2"}),e.jsxs("div",{className:"sm:col-span-4  mb-5",children:[e.jsx(g,{register:i,label:"Title",name:"footer_block_three_link_three",type:"text",placeholder:r("Link")}),e.jsx(c,{errorName:t.footer_block_three_link_three})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-1",children:[e.jsxs("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:[r("Link")," 4"]}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:i,label:"Title",name:"footer_block_three_link_four_title",type:"text",placeholder:"Updated Profile"}),e.jsx(c,{errorName:t.footer_block_three_link_four_title})]}),e.jsx("label",{className:"md:col-span-1 sm:col-span-2"}),e.jsxs("div",{className:"sm:col-span-4  mb-5",children:[e.jsx(g,{register:i,label:"Title",name:"footer_block_three_link_four",type:"text",placeholder:r("Link")}),e.jsx(c,{errorName:t.footer_block_three_link_four})]})]})]}),e.jsx("div",{className:"inline-flex md:text-base text-sm mb-3 text-gray-500 dark:text-gray-400 relative md:mt-0 mt-24",children:e.jsxs("strong",{children:[r("Block")," 4"]})}),e.jsx("hr",{className:"md:mb-12 mb-3"}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("EnableThisBlock")}),e.jsx("div",{className:"sm:col-span-4",children:e.jsx(S,{title:"",handleProcess:Oe,processOption:ie,name:ie})})]}),e.jsxs("div",{style:{height:ie?"auto":0,transition:"all 0.5s",visibility:ie?"visible":"hidden",opacity:ie?"1":"0"},children:[e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("FooterLogo")}),e.jsx("div",{className:"sm:col-span-4",children:e.jsx(O,{imageUrl:we,setImageUrl:Te})})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("FooterAddress")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:i,label:"Title",name:"footer_block_four_address",type:"text",placeholder:"Address"}),e.jsx(c,{errorName:t.footer_block_four_address})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("FooterPhone")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:i,label:"Title",name:"footer_block_four_phone",type:"text",placeholder:r("Phone")}),e.jsx(c,{errorName:t.footer_block_four_phone})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("FooterEmail")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:i,label:"Title",name:"footer_block_four_email",type:"text",placeholder:"Email"}),e.jsx(c,{errorName:t.footer_block_four_email})]})]})]}),e.jsx("div",{className:"inline-flex md:text-base text-sm mb-3 text-gray-500 dark:text-gray-400 relative mt-24 md:mt-0",children:e.jsx("strong",{children:r("SocialLinks")})}),e.jsx("hr",{className:"md:mb-12 mb-3"}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("EnableThisBlock")}),e.jsx("div",{className:"sm:col-span-4",children:e.jsx(S,{title:"",handleProcess:Be,processOption:ne,name:ne})})]}),e.jsxs("div",{style:{height:ne?"auto":0,transition:"all 0.5s",visibility:ne?"visible":"hidden",opacity:ne?"1":"0"},children:[e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:"Facebook"}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:i,label:"Title",name:"social_facebook",type:"text",placeholder:"Facebook link"}),e.jsx(c,{errorName:t.social_facebook})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:"Twitter"}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:i,label:"Title",name:"social_twitter",type:"text",placeholder:"Twitter Link"}),e.jsx(c,{errorName:t.social_twitter})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:"Pinterest"}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:i,label:"Title",name:"social_pinterest",type:"text",placeholder:"Pinterest Link"}),e.jsx(c,{errorName:t.social_pinterest})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:"Linkedin"}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:i,label:"Title",name:"social_linkedin",type:"text",placeholder:"Linkedin Link"}),e.jsx(c,{errorName:t.social_linkedin})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:"WhatsApp"}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(g,{register:i,label:"Title",name:"social_whatsapp",type:"text",placeholder:"whatsApp Link"}),e.jsx(c,{errorName:t.social_whatsapp})]})]})]}),e.jsx("div",{className:"inline-flex md:text-base text-sm mb-3 text-gray-500 dark:text-gray-400 relative mt-24 md:mt-0",children:e.jsx("strong",{children:r("PaymentMethod")})}),e.jsx("hr",{className:"md:mb-12 mb-3"}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("EnableThisBlock")}),e.jsx("div",{className:"sm:col-span-4",children:e.jsx(S,{title:"",handleProcess:Ie,processOption:re,name:re})})]}),e.jsx("div",{style:{height:re?"auto":0,transition:"all 0.5s",visibility:re?"visible":"hidden",opacity:re?"1":"0"},children:e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("PaymentMethod")}),e.jsx("div",{className:"sm:col-span-4",children:e.jsx(O,{imageUrl:Se,setImageUrl:Ce})})]})}),e.jsx("div",{className:"inline-flex md:text-base text-sm mb-3 text-gray-500 dark:text-gray-400 relative mt-16 md:mt-0",children:e.jsx("strong",{children:r("FooterBottomContact")})}),e.jsx("hr",{className:"md:mb-12 mb-3"}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("EnableThisBlock")}),e.jsx("div",{className:"sm:col-span-4",children:e.jsx(S,{title:"",handleProcess:Ke,processOption:me,name:me})})]}),e.jsxs("div",{style:{height:me?"auto":0,transition:"all 0.5s",visibility:me?"visible":"hidden",opacity:me?"1":"0"},className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:r("FooterBottomContact")}),e.jsxs("div",{className:"sm:col-span-4 mb-20 md:mb-0",children:[e.jsx(g,{register:i,label:"Title",name:"footer_Bottom_Contact",type:"text",placeholder:r("FooterBottomContact")}),e.jsx(c,{errorName:t.footer_Bottom_Contact})]})]})]})]})]})]})},Fl=({isSave:i,register:t,errors:n,isSubmitting:a,singleProductPageRightBox:o,setSingleProductPageRightBox:h})=>{const{t:x}=ae();return e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"sticky top-0 z-20 flex justify-end",children:a?e.jsxs(D.Button,{disabled:!0,type:"button",className:"h-10 px-6",children:[e.jsx("img",{src:fe,alt:"Loading",width:20,height:10})," ",e.jsxs("span",{className:"font-serif ml-2 font-light",children:[" ",x("Processing")]})]}):e.jsxs(D.Button,{type:"submit",className:"h-10 px-6 ",children:[" ",x(i?"SaveBtn":"UpdateBtn")]})}),e.jsx("div",{className:"grid grid-cols-12 font-sans pr-4",children:e.jsxs("div",{className:"col-span-12 md:col-span-12 lg:col-span-12",children:[e.jsxs("div",{className:"inline-flex md:text-lg text-base text-gray-800 font-semibold dark:text-gray-400 mb-3 relative",children:[e.jsx(U,{className:"mt-1 mr-2"}),x("RightBox")]}),e.jsx("hr",{className:"md:mb-12 mb-2"}),e.jsxs("div",{className:"xl:px-10 flex-grow scrollbar-hide w-full max-h-full",children:[e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-2",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:x("EnableThisBlock")}),e.jsx("div",{className:"sm:col-span-4",children:e.jsx(S,{title:"",handleProcess:h,processOption:o,name:o})})]}),e.jsxs("div",{style:{height:o?940:0,transition:"all 0.5s",visibility:o?"visible":"hidden",opacity:o?"1":"0"},children:[e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-4",children:[e.jsxs("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:[x("Description")," One"]}),e.jsxs("div",{className:" sm:col-span-4",children:[e.jsx(P,{register:t,label:"Description",name:"slug_page_card_description_one",type:"text",placeholder:x("Description")}),e.jsx(c,{errorName:n.slug_page_card_description_one})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-4",children:[e.jsxs("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:[x("Description")," Two"]}),e.jsxs("div",{className:" sm:col-span-4",children:[e.jsx(P,{register:t,label:" Description",name:"slug_page_card_description_two",type:"text",placeholder:x("Description")}),e.jsx(c,{errorName:n.slug_page_card_description_two})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-4",children:[e.jsxs("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:[x("Description")," Three"]}),e.jsxs("div",{className:" sm:col-span-4",children:[e.jsx(P,{register:t,label:"Description",name:"slug_page_card_description_three",type:"text",placeholder:x("Description")}),e.jsx(c,{errorName:n.slug_page_card_description_three})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-4",children:[e.jsxs("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:[x("Description")," Four"]}),e.jsxs("div",{className:" sm:col-span-4",children:[e.jsx(P,{register:t,label:"Description",name:"slug_page_card_description_four",type:"text",placeholder:x("Description")}),e.jsx(c,{errorName:n.slug_page_card_description_four})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-4",children:[e.jsxs("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:[x("Description")," Five"]}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(P,{register:t,label:"slug_page_card_description_five",name:"slug_page_card_description_five",type:"text",placeholder:x("Description")}),e.jsx(c,{errorName:n.slug_page_card_description_five})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-4",children:[e.jsxs("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:[x("Description")," Six"]}),e.jsxs("div",{className:" sm:col-span-4",children:[e.jsx(P,{register:t,label:"Description",name:"slug_page_card_description_six",type:"text",placeholder:x("Description")}),e.jsx(c,{errorName:n.slug_page_card_description_six})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 sm:grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-4",children:[e.jsxs("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 md:mb-1",children:[x("Description")," Seven"]}),e.jsxs("div",{className:" sm:col-span-4",children:[e.jsx(P,{register:t,label:" Description",name:"slug_page_card_description_seven",type:"text",placeholder:x("Description")}),e.jsx(c,{errorName:n.slug_page_card_description_seven})]})]})]})]})]})})]})},ql=({isSave:i,errors:t,register:n,isSubmitting:a})=>{const{t:o}=ae();return e.jsx(e.Fragment,{children:e.jsxs("div",{className:"col-span-12 md:col-span-12 lg:col-span-12 pr-3",children:[e.jsx("div",{className:"sticky top-0 z-20 flex justify-end",children:a?e.jsxs(D.Button,{disabled:!0,type:"button",className:"h-10 px-6",children:[e.jsx("img",{src:fe,alt:"Loading",width:20,height:10})," ",e.jsxs("span",{className:"font-serif ml-2 font-light",children:[" ",o("Processing")]})]}):e.jsxs(D.Button,{type:"submit",className:"h-10 px-6 ",children:[" ",o(i?"SaveBtn":"UpdateBtn")]})}),e.jsxs("div",{className:"inline-flex md:text-lg text-md text-gray-800 font-semibold dark:text-gray-400 md:mb-3 mb-1",children:[e.jsx(U,{className:"mt-1 mr-2"}),o("Checkout")]}),e.jsx("hr",{className:"md:mb-12 mb-3"}),e.jsxs("div",{className:"flex justify-between md:text-base text-sm mb-3  dark:text-gray-400 relative",children:[e.jsx("div",{className:"w-full text-gray-500",children:e.jsx("strong",{children:o("PersonalInfo")})}),e.jsxs("div",{className:"w-full",children:[e.jsx(g,{register:n,label:o("PersonalInfo"),name:"personal_details",type:"text",placeholder:o("PersonalInfo")}),e.jsx(c,{errorName:t.personal_details})]})]}),e.jsx("hr",{className:"md:mb-8 mb-3"}),e.jsxs("div",{className:"grid grid-cols-12 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsxs("div",{className:"col-span-4",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:o("firstName")}),e.jsx(g,{register:n,label:o("firstName"),name:"first_name",type:"text",placeholder:o("firstName")}),e.jsx(c,{errorName:t.first_name})]}),e.jsxs("div",{className:"col-span-4",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:o("firstName")}),e.jsx(g,{register:n,label:o("firstName"),name:"first_name",type:"text",placeholder:o("firstName")}),e.jsx(c,{errorName:t.first_name})]}),e.jsxs("div",{className:"col-span-4",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:o("lastName")}),e.jsx(g,{register:n,label:o("lastName"),name:"last_name",type:"text",placeholder:o("lastName")}),e.jsx(c,{errorName:t.last_name})]}),e.jsxs("div",{className:"col-span-4",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:o("emailAddress")}),e.jsx(g,{register:n,label:o("emailAddress"),name:"email_address",type:"text",placeholder:o("emailAddress")}),e.jsx(c,{errorName:t.email_address})]}),e.jsxs("div",{className:"col-span-4",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:o("Phone")}),e.jsx(g,{register:n,label:o("Phone"),name:"checkout_phone",type:"text",placeholder:o("Phone")}),e.jsx(c,{errorName:t.checkout_phone})]})]}),e.jsxs("div",{className:"flex justify-between md:text-base text-sm mb-3 mt-12 dark:text-gray-400 relative",children:[e.jsx("div",{className:"w-full text-gray-500",children:e.jsx("strong",{children:o("ShippingInfo")})}),e.jsxs("div",{className:"w-full",children:[e.jsx(g,{register:n,label:o("ShippingInfo"),name:"shipping_details",type:"text",placeholder:o("ShippingInfo")}),e.jsx(c,{errorName:t.shipping_details})]})]}),e.jsx("hr",{className:"md:mb-8 mb-3"}),e.jsxs("div",{className:"grid grid-cols-12 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsxs("div",{className:"col-span-4",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:o("streetAddress")}),e.jsx(g,{register:n,label:o("streetAddress"),name:"street_address",type:"text",placeholder:o("streetAddress")}),e.jsx(c,{errorName:t.street_address})]}),e.jsxs("div",{className:"col-span-4",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:o("City")}),e.jsx(g,{register:n,label:o("City"),name:"city",type:"text",placeholder:o("City")}),e.jsx(c,{errorName:t.city})]}),e.jsxs("div",{className:"col-span-4",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:o("Country")}),e.jsx(g,{register:n,label:o("Country"),name:"country",type:"text",placeholder:o("Country")}),e.jsx(c,{errorName:t.country})]}),e.jsxs("div",{className:"col-span-4",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:o("ZipCode")}),e.jsx(g,{register:n,label:o("ZipCode"),name:"zip_code",type:"text",placeholder:o("ZipCode")}),e.jsx(c,{errorName:t.zip_code})]}),e.jsxs("div",{className:"col-span-4",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:o("Shippingcost")}),e.jsx(g,{register:n,label:o("Shippingcost"),name:"shipping_cost",type:"text",placeholder:o("Shippingcost")}),e.jsx(c,{errorName:t.shipping_cost})]}),e.jsxs("div",{className:"col-span-4",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:o("ShippingNameOne")}),e.jsx(g,{register:n,label:o("ShippingNameOne"),name:"shipping_name_one",type:"text",placeholder:o("ShippingNameOne")}),e.jsx(c,{errorName:t.shipping_name_one})]}),e.jsxs("div",{className:"col-span-4",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:o("ShippingOneDes")}),e.jsx(g,{register:n,label:o("ShippingOneDes"),name:"shipping_one_desc",type:"text",placeholder:o("ShippingOneDes")}),e.jsx(c,{errorName:t.shipping_one_desc})]}),e.jsxs("div",{className:"col-span-4",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:o("ShippingOneCost")}),e.jsx(g,{register:n,label:o("ShippingOneCost"),name:"shipping_one_cost",type:"text",placeholder:o("ShippingOneCost")}),e.jsx(c,{errorName:t.shipping_one_cost})]}),e.jsxs("div",{className:"col-span-4",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:o("ShippingNameTwo")}),e.jsx(g,{register:n,label:o("ShippingNameTwo"),name:"shipping_name_two",type:"text",placeholder:o("ShippingNameTwo")}),e.jsx(c,{errorName:t.shipping_name_two})]}),e.jsxs("div",{className:"col-span-4",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:o("ShippingTwoDes")}),e.jsx(g,{register:n,label:o("ShippingTwoDes"),name:"shipping_two_desc",type:"text",placeholder:o("ShippingTwoDes")}),e.jsx(c,{errorName:t.shipping_two_desc})]}),e.jsxs("div",{className:"col-span-4",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:o("ShippingTwoCost")}),e.jsx(g,{register:n,label:o("ShippingTwoCost"),name:"shipping_two_cost",type:"text",placeholder:o("ShippingTwoCost")}),e.jsx(c,{errorName:t.shipping_two_cost})]}),e.jsxs("div",{className:"col-span-4",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:o("PaymentMethod")}),e.jsx(g,{register:n,label:o("PaymentMethod"),name:"payment_method",type:"text",placeholder:o("PaymentMethod")}),e.jsx(c,{errorName:t.payment_method})]}),e.jsxs("div",{className:"col-span-4",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:o("ContinueButton")}),e.jsx(g,{register:n,label:o("ContinueButton"),name:"continue_button",type:"text",placeholder:o("ContinueButton")}),e.jsx(c,{errorName:t.continue_button})]}),e.jsxs("div",{className:"col-span-4",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:o("ConfirmButton")}),e.jsx(g,{register:n,label:o("ConfirmButton"),name:"confirm_button",type:"text",placeholder:o("ConfirmButton")}),e.jsx(c,{errorName:t.confirm_button})]})]}),e.jsx("div",{className:"inline-flex md:text-base text-sm mb-3 text-gray-500 dark:text-gray-400 relative",children:e.jsx("strong",{children:o("CartItemSection")})}),e.jsx("hr",{className:"md:mb-8 mb-3"}),e.jsxs("div",{className:"grid grid-cols-12 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsxs("div",{className:"col-span-4",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:o("OrderSummary")}),e.jsx(g,{register:n,label:o("OrderSummary"),name:"order_summary",type:"text",placeholder:o("OrderSummary")}),e.jsx(c,{errorName:t.order_summary})]}),e.jsxs("div",{className:"col-span-4",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:o("ApplyButton")}),e.jsx(g,{register:n,label:o("ApplyButton"),name:"apply_button",type:"text",placeholder:o("ApplyButton")}),e.jsx(c,{errorName:t.apply_button})]}),e.jsxs("div",{className:"col-span-4",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:o("Subtotal")}),e.jsx(g,{register:n,label:o("Subtotal"),name:"sub_total",type:"text",placeholder:o("Subtotal")}),e.jsx(c,{errorName:t.sub_total})]}),e.jsxs("div",{className:"col-span-4",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:o("DiscountLower")}),e.jsx(g,{register:n,label:o("DiscountLower"),name:"discount",type:"text",placeholder:o("DiscountLower")}),e.jsx(c,{errorName:t.discount})]}),e.jsxs("div",{className:"col-span-4",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:o("TotalCost")}),e.jsx(g,{register:n,label:o("TotalCost"),name:"total_cost",type:"text",placeholder:o("TotalCost")}),e.jsx(c,{errorName:t.total_cost})]})]})]})})},Pl=({errors:i,register:t,isSave:n,favicon:a,setFavicon:o,metaImg:h,setMetaImg:x,isSubmitting:j})=>{const{t:u}=ae();return e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"sticky top-0 z-20 flex justify-end",children:j?e.jsxs(D.Button,{disabled:!0,type:"button",className:"h-10 px-6",children:[e.jsx("img",{src:fe,alt:"Loading",width:20,height:10})," ",e.jsxs("span",{className:"font-serif ml-2 font-light",children:[" ",u("Processing")]})]}):e.jsxs(D.Button,{type:"submit",className:"h-10 px-6 ",children:[" ",u(n?"SaveBtn":"UpdateBtn")]})}),e.jsx("div",{className:"grid grid-cols-12 font-sans",children:e.jsxs("div",{className:"col-span-12 md:col-span-12 lg:col-span-12 mr-3 ",children:[e.jsxs("div",{className:"inline-flex md:text-lg text-base text-gray-800 font-semibold dark:text-gray-400 mb-3 relative",children:[e.jsx(U,{className:"mt-1 mr-2"}),"Seo Settings"]}),e.jsx("hr",{className:"md:mb-12 mb-2"}),e.jsxs("div",{className:"lg:px-6 pt-4 lg:pl-40 lg:pr-40 md:pl-5 md:pr-5 flex-grow scrollbar-hide w-full max-h-full pb-0",children:[e.jsxs("div",{className:"grid md:grid-cols-5 items-center sm:grid-cols-12 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx("label",{className:"block text-sm font-semibold text-gray-600 dark:text-gray-400 mb-1 sm:col-span-2",children:u("Favicon")}),e.jsx("div",{className:"sm:col-span-3",children:e.jsx(O,{imageUrl:a,setImageUrl:o})})]}),e.jsxs("div",{className:"grid md:grid-cols-5 items-center sm:grid-cols-12 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx("label",{className:"block text-sm font-semibold text-gray-600 dark:text-gray-400 mb-1 sm:col-span-2",children:u("MetaTitle")}),e.jsxs("div",{className:"sm:col-span-3",children:[e.jsx(g,{register:t,label:u("MetaTitle"),name:"meta_title",type:"text",placeholder:u("MetaTitle")}),e.jsx(c,{errorName:i.meta_title})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 items-center sm:grid-cols-12 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx("label",{className:"block text-sm font-semibold text-gray-600 dark:text-gray-400 mb-1 sm:col-span-2",children:u("MetaDescription")}),e.jsxs("div",{className:"sm:col-span-3",children:[e.jsx(P,{required:!0,register:t,label:u("MetaDescription"),name:"meta_description",type:"text",placeholder:u("MetaDescription")}),e.jsx(c,{errorName:i.meta_description})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 items-center sm:grid-cols-12 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx("label",{className:"block text-sm font-semibold text-gray-600 dark:text-gray-400 mb-1 sm:col-span-2",children:u("MetaUrl")}),e.jsxs("div",{className:"sm:col-span-3",children:[e.jsx(g,{register:t,label:u("MetaUrl"),name:"meta_url",type:"text",placeholder:u("MetaUrl")}),e.jsx(c,{errorName:i.meta_url})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 items-center sm:grid-cols-12 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx("label",{className:"block text-sm font-semibold text-gray-600 dark:text-gray-400 mb-1 sm:col-span-2",children:u("MetaKeyword")}),e.jsxs("div",{className:"sm:col-span-3",children:[e.jsx(P,{register:t,label:u("MetaKeyword"),name:"meta_keywords",type:"text",placeholder:u("MetaKeyword")}),e.jsx(c,{errorName:i.meta_keywords})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 items-center sm:grid-cols-12 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx("label",{className:"block text-sm font-semibold text-gray-600 dark:text-gray-400 mb-1 sm:col-span-2",children:u("MetaImage")}),e.jsx("div",{className:"sm:col-span-3",children:e.jsx(O,{imageUrl:h,setImageUrl:x})})]})]})]})})]})},Ol=({isSave:i,errors:t,register:n,isSubmitting:a})=>{const{t:o}=ae();return e.jsxs("div",{className:"col-span-12 md:col-span-12 lg:col-span-12 pr-3",children:[e.jsx("div",{className:"sticky top-0 z-20 flex justify-end",children:a?e.jsxs(D.Button,{disabled:!0,type:"button",className:"h-10 px-6",children:[e.jsx("img",{src:fe,alt:"Loading",width:20,height:10})," ",e.jsxs("span",{className:"font-serif ml-2 font-light",children:[" ",o("Processing")]})]}):e.jsxs(D.Button,{type:"submit",className:"h-10 px-6 ",children:[" ",o(i?"SaveBtn":"UpdateBtn")]})}),e.jsxs("div",{className:"inline-flex md:text-lg text-md text-gray-800 font-semibold dark:text-gray-400 md:mb-3 mb-1",children:[e.jsx(U,{className:"mt-1 mr-2"}),o("DashboardSetting")]}),e.jsx("hr",{className:"md:mb-12 mb-3"}),e.jsx("div",{className:"flex justify-between md:text-base text-sm mb-3  dark:text-gray-400 relative",children:e.jsx("div",{className:"w-full text-gray-500",children:e.jsx("strong",{children:o("Dashboard")})})}),e.jsx("hr",{className:"md:mb-8 mb-3"}),e.jsxs("div",{className:"grid grid-cols-12 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsxs("div",{className:"col-span-4",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:o("InvoiceMessage1st")}),e.jsx(g,{register:n,label:o("InvoiceMessage1st"),name:"invoice_message_first",type:"text",placeholder:o("InvoiceMessage1st")}),e.jsx(c,{errorName:t.invoice_message_first})]}),e.jsxs("div",{className:"col-span-4",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:o("InvoiceMessage2nd")}),e.jsx(g,{register:n,label:o("InvoiceMessage2nd"),name:"invoice_message_last",type:"text",placeholder:o("InvoiceMessage2nd")}),e.jsx(c,{errorName:t.invoice_message_last})]}),e.jsxs("div",{className:"col-span-4",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:o("PrintButton")}),e.jsx(g,{register:n,label:o("PrintButton"),name:"print_button",type:"text",placeholder:o("PrintButton")}),e.jsx(c,{errorName:t.print_button})]}),e.jsxs("div",{className:"col-span-4",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:o("DownloadButton")}),e.jsx(g,{register:n,label:o("DownloadButton"),name:"download_button",type:"text",placeholder:o("DownloadButton")}),e.jsx(c,{errorName:t.download_button})]}),e.jsxs("div",{className:"col-span-4",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:o("Dashboard")}),e.jsx(g,{register:n,label:o("Dashboard"),name:"dashboard_title",type:"text",placeholder:o("Dashboard")}),e.jsx(c,{errorName:t.dashboard_title})]}),e.jsxs("div",{className:"col-span-4",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:o("TotalOrder")}),e.jsx(g,{register:n,label:o("TotalOrder"),name:"total_order",type:"text",placeholder:o("TotalOrder")}),e.jsx(c,{errorName:t.total_order})]}),e.jsxs("div",{className:"col-span-4",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:o("PendingOrder")}),e.jsx(g,{register:n,label:o("PendingOrder"),name:"pending_order",type:"text",placeholder:o("PendingOrder")}),e.jsx(c,{errorName:t.pending_order})]}),e.jsxs("div",{className:"col-span-4",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:o("ProcessingOrder")}),e.jsx(g,{register:n,label:o("ProcessingOrder"),name:"processing_order",type:"text",placeholder:o("ProcessingOrder")}),e.jsx(c,{errorName:t.processing_order})]}),e.jsxs("div",{className:"col-span-4",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:o("CompleteOrder")}),e.jsx(g,{register:n,label:o("CompleteOrder"),name:"complete_order",type:"text",placeholder:o("CompleteOrder")}),e.jsx(c,{errorName:t.complete_order})]}),e.jsxs("div",{className:"col-span-4",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:o("RecentOrder")}),e.jsx(g,{register:n,label:o("RecentOrder"),name:"recent_order",type:"text",placeholder:o("RecentOrder")}),e.jsx(c,{errorName:t.recent_order})]}),e.jsxs("div",{className:"col-span-4",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:o("MyOrder")}),e.jsx(g,{register:n,label:o("MyOrder"),name:"my_order",type:"text",placeholder:o("MyOrder")}),e.jsx(c,{errorName:t.my_order})]})]}),e.jsxs("div",{className:"flex justify-between md:text-base text-sm mb-3 mt-12 dark:text-gray-400 relative",children:[e.jsx("div",{className:"w-full text-gray-500",children:e.jsx("strong",{children:o("UpdateProfile")})}),e.jsxs("div",{className:"w-full",children:[e.jsx(g,{register:n,label:o("UpdateProfile"),name:"update_profile",type:"text",placeholder:o("UpdateProfile")}),e.jsx(c,{errorName:t.update_profile})]})]}),e.jsx("hr",{className:"md:mb-8 mb-3"}),e.jsxs("div",{className:"grid grid-cols-12 gap-3 md:gap-5 xl:gap-6 lg:gap-6 md:mb-6 mb-3",children:[e.jsxs("div",{className:"col-span-4",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:o("FullName")}),e.jsx(g,{register:n,label:o("FullName"),name:"full_name",type:"text",placeholder:o("FullName")}),e.jsx(c,{errorName:t.full_name})]}),e.jsxs("div",{className:"col-span-4",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:o("UserAddress")}),e.jsx(g,{register:n,label:o("UserAddress"),name:"address",type:"text",placeholder:o("UserAddress")}),e.jsx(c,{errorName:t.address})]}),e.jsxs("div",{className:"col-span-4",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:o("PhoneMobile")}),e.jsx(g,{register:n,label:o("PhoneMobile"),name:"user_phone",type:"text",placeholder:o("PhoneMobile")}),e.jsx(c,{errorName:t.user_phone})]}),e.jsxs("div",{className:"col-span-4",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:o("EmailAddress")}),e.jsx(g,{register:n,label:o("EmailAddress"),name:"user_email",type:"text",placeholder:o("EmailAddress")}),e.jsx(c,{errorName:t.user_email})]}),e.jsxs("div",{className:"col-span-4",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:o("UpdateButton")}),e.jsx(g,{register:n,label:o("UpdateButton"),name:"update_button",type:"text",placeholder:o("UpdateButton")}),e.jsx(c,{errorName:t.update_button})]}),e.jsxs("div",{className:"col-span-4",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:o("CurrentPassword")}),e.jsx(g,{register:n,label:o("CurrentPassword"),name:"current_password",type:"text",placeholder:o("CurrentPassword")}),e.jsx(c,{errorName:t.current_password})]}),e.jsxs("div",{className:"col-span-4",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:o("NewPassword")}),e.jsx(g,{register:n,label:o("NewPassword"),name:"new_password",type:"text",placeholder:o("NewPassword")}),e.jsx(c,{errorName:t.new_password})]}),e.jsxs("div",{className:"col-span-4",children:[e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:o("ChangePassword")}),e.jsx(g,{register:n,label:o("ChangePassword"),name:"change_password",type:"text",placeholder:o("ChangePassword")}),e.jsx(c,{errorName:t.change_password})]})]})]})},Xl=()=>{let i=Fa(),t=el();const{t:n}=ae(),a=t.get("storeTab"),{setTabIndex:o}=b.useContext(qa),{register:h,handleSubmit:x,onSubmit:j,errors:u,favicon:N,coupons:k,setFavicon:s,metaImg:w,setMetaImg:C,headerLogo:y,setHeaderLogo:B,sliderImage:q,setSliderImage:M,sliderImageTwo:L,setSliderImageTwo:Q,sliderImageThree:T,setSliderImageThree:ve,sliderImageFour:Ne,setSliderImageFour:we,sliderImageFive:Te,setSliderImageFive:Se,placeholderImage:Ce,setPlaceHolderImage:Fe,quickSectionImage:z,setQuickSectionImage:v,getYourDailyNeedImageLeft:F,setGetYourDailyNeedImageLeft:I,getYourDailyNeedImageRight:f,setGetYourDailyNeedImageRight:E,footerLogo:A,setFooterLogo:$,paymentImage:je,setPaymentImage:Z,isSave:H,isCoupon:W,isSliderFullWidth:V,setIsCoupon:K,setIsSliderFullWidth:R,featuredCategories:J,setFeaturedCategories:ye,popularProducts:X,setPopularProducts:le,setQuickDelivery:ee,quickDelivery:oe,setLatestDiscounted:te,latestDiscounted:Oe,setDailyNeeds:ie,dailyNeeds:Be,setFeaturePromo:ne,featurePromo:Ie,setFooterBlock1:re,footerBlock1:de,setFooterBlock2:Le,footerBlock2:De,setFooterBlock3:Ee,footerBlock3:Ue,setFooterBlock4:Ae,footerBlock4:Me,setFooterSocialLinks:ns,footerSocialLinks:rs,setFooterPaymentMethod:He,footerPaymentMethod:Re,allowPromotionBanner:ze,setAllowPromotionBanner:$e,handleSelectLanguage:Ke,singleProductPageRightBox:me,setSingleProductPageRightBox:Qe,setLeftRightArrow:ce,leftRightArrow:xe,setBottomDots:pe,bottomDots:ge,setBothSliderOption:be,bothSliderOption:_e,getButton1image:We,setGetButton1image:Ve,getButton2image:Ge,setGetButton2image:Ye,setFooterBottomContact:Ze,footerBottomContact:Je,setCategoriesMenuLink:Xe,categoriesMenuLink:es,setAboutUsMenuLink:ss,aboutUsMenuLink:ts,setContactUsMenuLink:r,contactUsMenuLink:as,setOffersMenuLink:ds,offersMenuLink:ms,setFaqMenuLink:cs,faqMenuLink:xs,setPrivacyPolicyMenuLink:ps,privacyPolicyMenuLink:gs,setTermsConditionsMenuLink:bs,termsConditionsMenuLink:_s,setAboutHeaderBg:us,aboutHeaderBg:hs,setAboutPageHeader:fs,aboutPageHeader:js,setAboutTopContentLeft:ys,aboutTopContentLeft:ks,setAboutTopContentRight:vs,aboutTopContentRight:Ns,setAboutTopContentRightImage:ws,aboutTopContentRightImage:Ts,setAboutMiddleContentSection:Ss,aboutMiddleContentSection:Cs,setAboutMiddleContentImage:Fs,aboutMiddleContentImage:qs,setOurFounderSection:Ps,ourFounderSection:Os,setOurFounderOneImage:Bs,ourFounderOneImage:Is,setOurFounderTwoImage:Ls,ourFounderTwoImage:Ds,setOurFounderThreeImage:Es,ourFounderThreeImage:Us,setOurFounderFourImage:As,ourFounderFourImage:Ms,setOurFounderFiveImage:Hs,ourFounderFiveImage:Rs,setOurFounderSixImage:zs,ourFounderSixImage:$s,setPrivacyPolicy:Ks,privacyPolicy:Qs,setPrivacyPolicyHeaderBg:Ws,privacyPolicyHeaderBg:Vs,setTermsConditions:Gs,termsConditions:Ys,setTermsConditionsHeaderBg:Zs,termsConditionsHeaderBg:Js,setFaqStatus:Xs,faqStatus:et,setFaqHeaderBg:st,faqHeaderBg:tt,setFaqLeftColImage:at,faqLeftColImage:lt,setOffersPageHeader:ot,offersPageHeader:it,setOffersHeaderBg:nt,offersHeaderBg:rt,setContactPageHeader:dt,contactPageHeader:mt,setContactHeaderBg:ct,contactHeaderBg:xt,setEmailUsBox:pt,emailUsBox:gt,setCallUsBox:bt,callUsBox:_t,setAddressBox:ut,addressBox:ht,setContactMidLeftColStatus:ft,contactMidLeftColStatus:jt,setContactMidLeftColImage:yt,contactMidLeftColImage:kt,setContactFormStatus:vt,contactFormStatus:Nt,couponList:wt,setCouponList:Tt,couponList1:St,setCouponList1:Ct,setFaqLeftColStatus:Ft,faqLeftColStatus:qt,setFaqRightColStatus:Pt,faqRightColStatus:Ot,textEdit:Bt,setTextEdit:It,termsConditionsTextEdit:Lt,isSubmitting:se,setTermsConditionsTextEdit:Dt}=gl();return b.useEffect(()=>{o(a==="seo-setting"?9:a==="dashboard-setting"?8:a==="checkout-setting"?7:a==="contact-us-setting"?6:a==="offers-setting"?5:a==="FAQ-setting"?4:a==="privacy-setting"?3:a==="about-us-setting"?2:a==="single-setting"?1:0)},[a,o]),b.useEffect(()=>{W&&R(!1)},[W,R]),b.useEffect(()=>{xe&&pe(!1)},[xe,pe]),b.useEffect(()=>{xe&&be(!1)},[xe,be]),b.useEffect(()=>{ge&&be(!1)},[ge,be]),b.useEffect(()=>{ge&&ce(!1)},[ge,ce]),b.useEffect(()=>{_e&&ce(!1)},[_e,ce]),b.useEffect(()=>{_e&&pe(!1)},[_e,pe]),e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"flex justify-between text-center items-center",children:[e.jsx("div",{children:e.jsx(Ja,{children:n("StoreCustomizationPageTitle")})}),e.jsx("div",{className:"pb-4",children:e.jsx(Xa,{register:h,handleSelectLanguage:Ke})})]}),e.jsxs(e.Fragment,{children:[e.jsxs("ul",{className:"sm:flex grid grid-cols-3 text-sm font-medium text-center text-gray-500 sm:divide-x divide-gray-200 rounded-lg dark:divide-gray-700 dark:text-gray-400 mb-5",children:[e.jsx("li",{children:e.jsxs(ue,{to:"/store/customization?storeTab=home-settings",className:`inline-block w-full px-4 py-3 shadow-md ${a==="home-settings"||i.search===""?"bg-emerald-500 text-white dark:bg-emerald-500 dark:text-white":"bg-gray-200 text-gray-800 font-medium dark:bg-gray-600 dark:text-gray-300"} hover:text-white hover:bg-emerald-500  focus:outline-none dark:hover:text-white dark:hover:bg-emerald-500 sm:rounded-l-md rounded-tl-md`,children:[e.jsx("span",{className:"text-sm font-medium font-serif xl:inline-block hidden",children:n("HomeSettings")}),e.jsx("span",{className:"text-sm font-medium font-serif xl:hidden",children:(a==="home-settings"||i.search==="","Home")})]})}),e.jsx("li",{children:e.jsxs(ue,{to:"/store/customization?storeTab=single-setting",className:`inline-block w-full py-3 px-4 shadow-md ${a==="single-setting"?"bg-emerald-500 text-white dark:bg-emerald-500 dark:text-white":"bg-gray-200 text-gray-800 font-medium dark:bg-gray-600 dark:text-gray-300"} hover:text-white hover:bg-emerald-500  focus:outline-none dark:hover:text-white dark:hover:bg-emerald-500`,children:[e.jsx("span",{className:"text-sm font-medium font-serif xl:inline-block hidden",children:n("SingleSetting")}),e.jsx("span",{className:"text-sm font-medium font-serif xl:hidden",children:"Setting"})]})}),e.jsx("li",{children:e.jsxs(ue,{to:"/store/customization?storeTab=about-us-setting",className:`inline-block w-full py-3 px-4 shadow-md ${a==="about-us-setting"?"bg-emerald-500 text-white dark:bg-emerald-500 dark:text-white":"bg-gray-200 text-gray-800 font-medium dark:bg-gray-600 dark:text-gray-300"} hover:text-white hover:bg-emerald-500 focus:outline-none dark:hover:text-white dark:hover:bg-emerald-500 sm:rounded-tr-none rounded-tr-md`,children:[e.jsx("span",{className:"text-sm font-medium font-serif xl:inline-block hidden",children:n("AboutUsSetting")}),e.jsx("span",{className:"text-sm font-medium font-serif xl:hidden",children:"About"})]})}),e.jsx("li",{children:e.jsxs(ue,{to:"/store/customization?storeTab=privacy-setting",className:`inline-block w-full py-3 px-4 shadow-md ${a==="privacy-setting"?"bg-emerald-500 text-white dark:bg-emerald-500 dark:text-white":"bg-gray-200 text-gray-800 font-medium dark:bg-gray-600 dark:text-gray-300"} hover:text-white hover:bg-emerald-500  focus:outline-none dark:hover:text-white dark:hover:bg-emerald-500`,children:[e.jsx("span",{className:"text-sm font-medium font-serif xl:inline-block hidden",children:n("PrivacyTCSetting")}),e.jsx("span",{className:"text-sm font-medium font-serif xl:hidden",children:"Privacy"})]})}),e.jsx("li",{children:e.jsxs(ue,{to:"/store/customization?storeTab=FAQ-setting",className:`inline-block w-full py-3 px-4 shadow-md ${a==="FAQ-setting"?"bg-emerald-500 text-white dark:bg-emerald-500 dark:text-white":"bg-gray-200 text-gray-800 font-medium dark:bg-gray-600 dark:text-gray-300"} hover:text-white hover:bg-emerald-500  focus:outline-none dark:hover:text-white dark:hover:bg-emerald-500`,children:[e.jsx("span",{className:"text-sm font-medium font-serif xl:inline-block hidden",children:n("FAQSetting")}),e.jsx("span",{className:"text-sm font-medium font-serif xl:hidden",children:n("FAQSetting")})]})}),e.jsx("li",{children:e.jsxs(ue,{to:"/store/customization?storeTab=offers-setting",className:`inline-block w-full py-3 px-4 shadow-md ${a==="offers-setting"?"bg-emerald-500 text-white dark:bg-emerald-500 dark:text-white":"bg-gray-200 text-gray-800 font-medium dark:bg-gray-600 dark:text-gray-300"} hover:text-white hover:bg-emerald-500  focus:outline-none dark:hover:text-white dark:hover:bg-emerald-500`,children:[e.jsx("span",{className:"text-sm font-medium font-serif xl:inline-block hidden",children:n("OffersStting")}),e.jsx("span",{className:"text-sm font-medium font-serif xl:hidden",children:n("OffersStting")})]})}),e.jsx("li",{children:e.jsxs(ue,{to:"/store/customization?storeTab=contact-us-setting",className:`inline-block w-full py-3 px-4 shadow-md ${a==="contact-us-setting"?"bg-emerald-500 text-white dark:bg-emerald-500 dark:text-white":"bg-gray-200 text-gray-800 font-medium dark:bg-gray-600 dark:text-gray-300"} hover:text-white hover:bg-emerald-500  focus:outline-none dark:hover:text-white dark:hover:bg-emerald-500 sm:rounded-bl-none rounded-bl-md`,children:[e.jsx("span",{className:"text-sm font-medium font-serif xl:inline-block hidden",children:n("ContactUsStting")}),e.jsx("span",{className:"text-sm font-medium font-serif xl:hidden",children:"Contact"})]})}),e.jsx("li",{children:e.jsxs(ue,{to:"/store/customization?storeTab=checkout-setting",className:`inline-block w-full py-3 px-4 shadow-md ${a==="checkout-setting"?"bg-emerald-500 text-white dark:bg-emerald-500 dark:text-white":"bg-gray-200 text-gray-800 font-medium dark:bg-gray-600 dark:text-gray-300"} hover:text-white hover:bg-emerald-500  focus:outline-none dark:hover:text-white dark:hover:bg-emerald-500 sm:rounded-bl-none rounded-bl-md`,children:[e.jsx("span",{className:"text-sm font-medium font-serif xl:inline-block hidden",children:n("Checkout")}),e.jsx("span",{className:"text-sm font-medium font-serif xl:hidden",children:n("Checkout")})]})}),e.jsx("li",{children:e.jsxs(ue,{to:"/store/customization?storeTab=dashboard-setting",className:`inline-block w-full py-3 px-4 shadow-md ${a==="dashboard-setting"?"bg-emerald-500 text-white dark:bg-emerald-500 dark:text-white":"bg-gray-200 text-gray-800 font-medium dark:bg-gray-600 dark:text-gray-300"} hover:text-white hover:bg-emerald-500  focus:outline-none dark:hover:text-white dark:hover:bg-emerald-500 sm:rounded-bl-none rounded-bl-md`,children:[e.jsx("span",{className:"text-sm font-medium font-serif xl:inline-block hidden",children:n("DashboardSetting")}),e.jsx("span",{className:"text-sm font-medium font-serif xl:hidden",children:n("Dashboard")})]})}),e.jsx("li",{children:e.jsxs(ue,{to:"/store/customization?storeTab=seo-settings",className:`inline-block w-full py-3 px-4 shadow-md ${a==="seo-settings"?"bg-emerald-500 text-white dark:bg-emerald-500 dark:text-white":"bg-gray-200 text-gray-800 font-medium dark:bg-gray-600 dark:text-gray-300"} hover:text-white hover:bg-emerald-500  focus:outline-none dark:hover:text-white dark:hover:bg-emerald-500`,children:[e.jsx("span",{className:"text-sm font-medium font-serif xl:inline-block hidden",children:n("SeoSetting")}),e.jsx("span",{className:"text-sm font-medium font-serif xl:hidden",children:"Seo"})]})})]}),(a==="home-settings"||a===null)&&e.jsx(he,{children:e.jsx("div",{className:"sm:container md:p-6 p-4 mx-auto bg-white dark:bg-gray-800 dark:text-gray-200 rounded-lg",children:e.jsx("form",{onSubmit:x(j),children:e.jsx(Cl,{errors:u,isSave:H,coupons:k,isCoupon:W,register:h,headerLogo:y,footerLogo:A,setFooterLogo:$,paymentImage:je,setPaymentImage:Z,setHeaderLogo:B,sliderImage:q,setSliderImage:M,sliderImageTwo:L,setSliderImageTwo:Q,sliderImageThree:T,setSliderImageThree:ve,sliderImageFour:Ne,setSliderImageFour:we,sliderImageFive:Te,setSliderImageFive:Se,placeholderImage:Ce,setPlaceHolderImage:Fe,quickSectionImage:z,setQuickSectionImage:v,getYourDailyNeedImageLeft:F,setGetYourDailyNeedImageLeft:I,getYourDailyNeedImageRight:f,setGetYourDailyNeedImageRight:E,isSliderFullWidth:V,setIsCoupon:K,setIsSliderFullWidth:R,featuredCategories:J,setFeaturedCategories:ye,popularProducts:X,setPopularProducts:le,setQuickDelivery:ee,quickDelivery:oe,setLatestDiscounted:te,latestDiscounted:Oe,setDailyNeeds:ie,dailyNeeds:Be,setFeaturePromo:ne,featurePromo:Ie,setFooterBlock1:re,footerBlock1:de,setFooterBlock2:Le,footerBlock2:De,setFooterBlock3:Ee,footerBlock3:Ue,setFooterBlock4:Ae,footerBlock4:Me,setFooterSocialLinks:ns,footerSocialLinks:rs,setFooterPaymentMethod:He,footerPaymentMethod:Re,allowPromotionBanner:ze,setAllowPromotionBanner:$e,setLeftRightArrow:ce,leftRightArrow:xe,setBottomDots:pe,bottomDots:ge,setBothSliderOption:be,bothSliderOption:_e,getButton1image:We,setGetButton1image:Ve,getButton2image:Ge,setGetButton2image:Ye,setFooterBottomContact:Ze,footerBottomContact:Je,setCategoriesMenuLink:Xe,categoriesMenuLink:es,setAboutUsMenuLink:ss,aboutUsMenuLink:ts,setContactUsMenuLink:r,contactUsMenuLink:as,setOffersMenuLink:ds,offersMenuLink:ms,setFaqMenuLink:cs,faqMenuLink:xs,setPrivacyPolicyMenuLink:ps,privacyPolicyMenuLink:gs,setTermsConditionsMenuLink:bs,termsConditionsMenuLink:_s,couponList:wt,setCouponList:Tt,isSubmitting:se})})})}),a==="single-setting"&&e.jsx(he,{children:e.jsx("div",{className:"sm:container w-full md:p-6 p-4 mx-auto bg-white dark:bg-gray-800 dark:text-gray-200 rounded-lg",children:e.jsx("form",{onSubmit:x(j),children:e.jsx(Fl,{isSave:H,errors:u,register:h,isSubmitting:se,singleProductPageRightBox:me,setSingleProductPageRightBox:Qe})})})}),a==="about-us-setting"&&e.jsx(he,{children:e.jsx("div",{className:"sm:container md:p-6 p-4 mx-auto bg-white  dark:bg-gray-800 dark:text-gray-200 rounded-lg",children:e.jsx("form",{onSubmit:x(j),children:e.jsx(xl,{isSave:H,register:h,errors:u,setAboutHeaderBg:us,aboutHeaderBg:hs,setAboutPageHeader:fs,aboutPageHeader:js,setAboutTopContentLeft:ys,aboutTopContentLeft:ks,setAboutTopContentRight:vs,aboutTopContentRight:Ns,setAboutTopContentRightImage:ws,aboutTopContentRightImage:Ts,setAboutMiddleContentSection:Ss,aboutMiddleContentSection:Cs,setAboutMiddleContentImage:Fs,aboutMiddleContentImage:qs,setOurFounderSection:Ps,ourFounderSection:Os,setOurFounderOneImage:Bs,ourFounderOneImage:Is,setOurFounderTwoImage:Ls,ourFounderTwoImage:Ds,setOurFounderThreeImage:Es,ourFounderThreeImage:Us,setOurFounderFourImage:As,ourFounderFourImage:Ms,setOurFounderFiveImage:Hs,ourFounderFiveImage:Rs,setOurFounderSixImage:zs,ourFounderSixImage:$s,isSubmitting:se})})})}),a==="privacy-setting"&&e.jsx(he,{children:e.jsx("div",{className:"sm:container md:p-6 p-4 mx-auto bg-white dark:bg-gray-800 dark:text-gray-200 rounded-lg",children:e.jsx("form",{onSubmit:x(j),children:e.jsx(Sl,{isSave:H,errors:u,register:h,textEdit:Bt,setTextEdit:It,privacyPolicy:Qs,setPrivacyPolicy:Ks,setPrivacyPolicyHeaderBg:Ws,privacyPolicyHeaderBg:Vs,setTermsConditions:Gs,termsConditions:Ys,setTermsConditionsHeaderBg:Zs,termsConditionsHeaderBg:Js,termsConditionsTextEdit:Lt,setTermsConditionsTextEdit:Dt,isSubmitting:se})})})}),a==="FAQ-setting"&&e.jsx(he,{children:e.jsx("div",{className:"sm:container md:p-6 p-4 mx-auto w-full bg-white  dark:bg-gray-800 dark:text-gray-200 rounded-lg",children:e.jsx("form",{onSubmit:x(j),children:e.jsx(sl,{isSave:H,errors:u,register:h,setFaqStatus:Xs,faqStatus:et,setFaqHeaderBg:st,faqHeaderBg:tt,setFaqLeftColImage:at,faqLeftColImage:lt,setFaqLeftColStatus:Ft,faqLeftColStatus:qt,setFaqRightColStatus:Pt,faqRightColStatus:Ot,isSubmitting:se})})})}),a==="offers-setting"&&e.jsx(he,{children:e.jsx("div",{className:"sm:container md:p-6 p-4 mx-auto bg-white  dark:bg-gray-800 dark:text-gray-200 rounded-lg",children:e.jsx("form",{onSubmit:x(j),children:e.jsx(tl,{errors:u,isSave:H,register:h,coupons:k,setOffersPageHeader:ot,offersPageHeader:it,setOffersHeaderBg:nt,offersHeaderBg:rt,couponList1:St,setCouponList1:Ct,isSubmitting:se})})})}),a==="contact-us-setting"&&e.jsx(he,{children:e.jsx("div",{className:"sm:container md:p-6 p-4 w-full mx-auto bg-white  dark:bg-gray-800 dark:text-gray-200 rounded-lg",children:e.jsx("form",{onSubmit:x(j),children:e.jsx(pl,{isSave:H,errors:u,register:h,setContactPageHeader:dt,contactPageHeader:mt,setContactHeaderBg:ct,contactHeaderBg:xt,setEmailUsBox:pt,emailUsBox:gt,setCallUsBox:bt,callUsBox:_t,setAddressBox:ut,addressBox:ht,setContactMidLeftColStatus:ft,contactMidLeftColStatus:jt,setContactMidLeftColImage:yt,contactMidLeftColImage:kt,setContactFormStatus:vt,contactFormStatus:Nt,isSubmitting:se})})})}),a==="checkout-setting"&&e.jsx(he,{children:e.jsx("div",{className:"sm:container md:p-6 p-4 w-full mx-auto bg-white  dark:bg-gray-800 dark:text-gray-200 rounded-lg",children:e.jsx("form",{onSubmit:x(j),children:e.jsx(ql,{isSave:H,errors:u,register:h,isSubmitting:se})})})}),a==="dashboard-setting"&&e.jsx(he,{children:e.jsx("div",{className:"sm:container md:p-6 p-4 w-full mx-auto bg-white  dark:bg-gray-800 dark:text-gray-200 rounded-lg",children:e.jsx("form",{onSubmit:x(j),children:e.jsx(Ol,{isSave:H,errors:u,register:h,isSubmitting:se})})})}),a==="seo-settings"&&e.jsx(he,{children:e.jsx("div",{className:"sm:container md:p-6 p-4 w-full mx-auto bg-white  dark:bg-gray-800 dark:text-gray-200 rounded-lg",children:e.jsx("form",{onSubmit:x(j),children:e.jsx(Pl,{isSave:H,errors:u,register:h,favicon:N,setFavicon:s,metaImg:w,setMetaImg:C,isSubmitting:se})})})})]})]})};export{Xl as default};
