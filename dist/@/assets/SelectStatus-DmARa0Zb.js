import{r as i,S as o,j as e,h as c}from"./index-BnbL29JP.js";import{O as d}from"./OrderServices-CpvSXYO1.js";import{n as h,a as p}from"./toast-DZMsp61l.js";const f=({id:n,order:t})=>{const{setIsUpdate:l}=i.useContext(o),r=(s,u)=>{d.updateOrder(s,{status:u}).then(a=>{h(a.message),l(!0)}).catch(a=>p(a.message))};return e.jsx(e.Fragment,{children:e.jsxs(c.Select,{onChange:s=>r(n,s.target.value),className:"h-8",children:[e.jsx("option",{value:"status",defaultValue:!0,hidden:!0,children:t?.status}),e.jsx("option",{defaultValue:t?.status==="Delivered",value:"Delivered",children:"Delivered"}),e.jsx("option",{defaultValue:t?.status==="Pending",value:"Pending",children:"Pending"}),e.jsx("option",{defaultValue:t?.status==="Processing",value:"Processing",children:"Processing"}),e.jsx("option",{defaultValue:t?.status==="Cancel",value:"Cancel",children:"Cancel"})]})})};export{f as S};
