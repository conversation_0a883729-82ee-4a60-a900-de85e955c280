import{j as e,k as T,h as l,l as H,r as f,S as z}from"./index-BnbL29JP.js";import{l as K,u as $,e as C,f as G,g as J}from"./Layout-pFzaaQDc.js";import{T as Q}from"./Tooltip-CCK2Gwc2.js";import{T as v,D as W,u as D,M as S}from"./DrawerButton-BQHT-xfW.js";import{D as k,E as X}from"./EditDeleteButton-BfkOlssy.js";import{C as _,S as Y,B as Z}from"./BulkActionDrawer-0F-ByaaS.js";import{E as w}from"./index.esm-ClJnGQn6.js";import{L as u}from"./LabelArea-DQFDcuEN.js";import{I as A}from"./InputArea-B_vEs1uv.js";import{u as ee}from"./useAttributeSubmit-DQ8-wYS4.js";import{U as le}from"./UploadMany-DQiD3frs.js";import{T as se}from"./TableLoading-BGBA3G2p.js";import{N as ae}from"./NotFound-H45Wi1lW.js";import{P as te}from"./PageTitle-FuOKSvYQ.js";import{u as re}from"./useAsync-DWXVKl2F.js";import{u as ie}from"./useFilter-7smJgyBE.js";import{A as ne}from"./ProductServices-CXwJ-2YB.js";import{A as de}from"./AnimatedContent-0V4vlNfe.js";import"./iconBase-CKOh_aia.js";import"./SelectLanguageTwo-KDdaD-gj.js";import"./spinner-CkndCogW.js";import"./AdminServices-DjQuFfvs.js";import"./CouponServices-BN-cEYCp.js";import"./CurrencyServices-3wuDp8cZ.js";import"./toast-DZMsp61l.js";import"./useDisableForDemo-Bu4HEiKz.js";import"./index.prod-BbOPZ2BB.js";import"./ParentCategory-BlfDuDd5.js";import"./SwitchToggle-CGiqq8S_.js";import"./useTranslationValue-DCEiB8J2.js";import"./exportFromJSON-fDIoOtpr.js";import"./index-C148XJoK.js";const oe=({notes:i,addNote:d,removeNote:o})=>e.jsxs("div",{className:"react-tag-input",children:[e.jsx("ul",{id:"tags",children:i.map((a,n)=>e.jsxs("li",{className:"react-tag-input__tag",children:[e.jsx("span",{className:"tag-title react-tag-input__tag__content",children:a}),e.jsx("span",{className:"react-tag-input__tag__remove",onClick:()=>o(n)})]},n))}),e.jsx("input",{name:"note",className:"react-tag-input__input",type:"text",onBlur:a=>d(a),onKeyDown:a=>a.key==="Enter"?d(a):null,placeholder:"Press enter to add variant"})]}),B=({id:i})=>{const{handleSubmit:d,onSubmit:o,register:a,errors:n,variants:x,addVariant:m,isSubmitting:c,removeVariant:t,handleSelectLanguage:s}=ee(i),{t:r}=T();return e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"w-full relative p-6 border-b border-gray-100 bg-gray-50 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-300",children:i?e.jsx(v,{register:a,handleSelectLanguage:s,title:r("UpdateAttribute"),description:r("UpdateAttributeDesc")}):e.jsx(v,{register:a,handleSelectLanguage:s,title:r("AddAttribute"),description:r("AddAttributeDesc")})}),e.jsxs(K.Scrollbars,{className:"w-full md:w-7/12 lg:w-8/12 xl:w-8/12 relative dark:bg-gray-700 dark:text-gray-200",children:[e.jsxs("form",{onSubmit:d(o),children:[e.jsxs("div",{className:"px-6 pt-8 flex-grow scrollbar-hide w-full max-h-full",children:[e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(u,{label:r("DrawerAttributeTitle")}),e.jsxs("div",{className:"col-span-8 sm:col-span-4",children:[e.jsx(A,{required:!0,register:a,label:"Attribute Title",name:"title",type:"text",placeholder:"Color or Size or Dimension or Material or Fabric"}),e.jsx(w,{errorName:n.title})]})]}),e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6 relative",children:[e.jsx(u,{label:r("DisplayName")}),e.jsxs("div",{className:"col-span-8 sm:col-span-4",children:[e.jsx(A,{required:!0,register:a,label:"Display Name",name:"name",type:"text",placeholder:"Display Name"}),e.jsx(w,{errorName:n.name})]})]}),e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 relative",children:[e.jsx(u,{label:r("DrawerOptions")}),e.jsxs("div",{className:"col-span-8 sm:col-span-4 ",children:[e.jsxs(l.Select,{name:"option",...a("option",{required:"Option is required!"}),children:[e.jsx("option",{value:"",defaultValue:!0,hidden:!0,children:r("DrawerSelecttype")}),e.jsx("option",{value:"Dropdown",children:r("Dropdown")}),e.jsx("option",{value:"Radio",children:r("Radio")})]}),e.jsx(w,{errorName:n.option})]})]})]}),e.jsx(W,{id:i,title:"Attribute",isSubmitting:c})]}),e.jsx("div",{className:"px-6 pt-8 flex-grow scrollbar-hide w-full max-h-full pb-40 ",children:!i&&e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6 relative",children:[e.jsx(u,{label:r("Variants")}),e.jsx("div",{className:"col-span-8 sm:col-span-4",children:e.jsx(oe,{notes:x,addNote:m,removeNote:t})})]})})]})]})},ce=({isCheck:i,setIsCheck:d,attributes:o})=>{const{title:a,serviceId:n,handleModalOpen:x,handleUpdate:m}=D(),{showingTranslateValue:c}=$(),t=s=>{const{id:r,checked:g}=s.target;d([...i,r]),g||d(i.filter(j=>j!==r))};return e.jsxs(e.Fragment,{children:[i.length<1&&e.jsx(k,{id:n,title:a}),i.length<2&&e.jsx(S,{children:e.jsx(B,{id:n})}),e.jsx(l.TableBody,{children:o?.map(s=>e.jsxs(l.TableRow,{children:[e.jsx(l.TableCell,{children:e.jsx(_,{type:"checkbox",name:"attribute",id:s._id,handleClick:t,isChecked:i?.includes(s._id)})}),e.jsx(l.TableCell,{className:"font-semibold uppercase text-xs",children:s?._id?.substring(20,24)}),e.jsx(l.TableCell,{className:"font-medium text-sm",children:c(s.title)}),e.jsx(l.TableCell,{className:"font-medium text-sm",children:c(s.name)}),e.jsx(l.TableCell,{className:"font-medium text-sm",children:s.option}),e.jsx(l.TableCell,{className:"text-center",children:e.jsx(Y,{id:s._id,status:s.status})}),e.jsx(l.TableCell,{className:"flex justify-center",children:e.jsx(H,{to:`/attributes/${s._id}`,className:"p-2 cursor-pointer text-gray-400 hover:text-emerald-600 focus:outline-none",children:e.jsx(Q,{id:"edit values",Icon:C,title:"Edit Values",bgColor:"#10B981"})})}),e.jsx(l.TableCell,{children:e.jsx(X,{id:s._id,isCheck:i,setIsCheck:d,handleUpdate:m,handleModalOpen:x,title:c(s.title)})})]},s._id))})]})},He=()=>{const{toggleDrawer:i,lang:d}=f.useContext(z),{data:o,loading:a,error:n}=re(()=>ne.getAllAttributes({type:"attribute",option:"Dropdown",option1:"Radio"})),{handleDeleteMany:x,allId:m,handleUpdateMany:c}=D(),{t}=T(),{filename:s,isDisabled:r,dataTable:g,serviceData:j,totalResults:F,attributeRef:N,resultsPerPage:E,handleSelectFile:P,handleChangePage:R,setAttributeTitle:M,handleSubmitAttribute:y,handleUploadMultiple:I,handleRemoveSelectFile:U}=ie(o),[b,V]=f.useState(!1),[p,h]=f.useState([]),L=()=>{V(!b),h(o.map(O=>O._id)),b&&h([])},q=()=>{M(""),N.current.value=""};return e.jsxs(e.Fragment,{children:[e.jsx(te,{children:t("AttributeTitle")}),e.jsx(k,{ids:m,setIsCheck:h,title:"Selected Attributes"}),e.jsx(Z,{ids:m,title:"Attributes"}),e.jsx(S,{children:e.jsx(B,{})}),e.jsxs(de,{children:[e.jsx(l.Card,{className:"min-w-0 shadow-xs overflow-hidden bg-white dark:bg-gray-800 mb-5",children:e.jsx(l.CardBody,{children:e.jsxs("form",{onSubmit:y,className:"py-3  grid gap-4 lg:gap-6 xl:gap-6  xl:flex",children:[e.jsx("div",{className:"flex justify-start xl:w-1/2  md:w-full",children:e.jsx(le,{title:"Attribute",exportData:o,filename:s,isDisabled:r,handleSelectFile:P,handleUploadMultiple:I,handleRemoveSelectFile:U})}),e.jsxs("div",{className:"lg:flex  md:flex xl:justify-end xl:w-1/2  md:w-full md:justify-start flex-grow-0",children:[e.jsx("div",{className:"w-full md:w-40 lg:w-40 xl:w-40 mr-3 mb-3 lg:mb-0",children:e.jsxs(l.Button,{disabled:p.length<1,onClick:()=>c(p),className:"w-full rounded-md h-12 btn-gray text-gray-600",children:[e.jsx("span",{className:"mr-2",children:e.jsx(C,{})}),t("BulkAction")]})}),e.jsx("div",{className:"w-full md:w-32 lg:w-32 xl:w-32 mr-3 mb-3 lg:mb-0",children:e.jsxs(l.Button,{disabled:p.length<1,onClick:()=>x(p),className:"w-full rounded-md h-12 bg-red-500 btn-red",children:[e.jsx("span",{className:"mr-2",children:e.jsx(G,{})}),t("Delete")]})}),e.jsx("div",{className:"w-full md:w-48 lg:w-48 xl:w-48",children:e.jsxs(l.Button,{onClick:i,className:"w-full rounded-md h-12 ",children:[e.jsx("span",{className:"mr-2",children:e.jsx(J,{})}),t("CouponsAddAttributeBtn")]})})]})]})})}),e.jsx(l.Card,{className:"min-w-0 shadow-xs overflow-hidden bg-white dark:bg-gray-800 mb-5",children:e.jsx(l.CardBody,{children:e.jsxs("form",{onSubmit:y,className:"py-3 grid gap-4 lg:gap-6 xl:gap-6 md:flex xl:flex",children:[e.jsx("div",{className:"flex-grow-0 md:flex-grow lg:flex-grow xl:flex-grow",children:e.jsx(l.Input,{ref:N,type:"search",placeholder:t("SearchAttributePlaceholder")})}),e.jsxs("div",{className:"flex items-center gap-2 flex-grow-0 md:flex-grow lg:flex-grow xl:flex-grow",children:[e.jsx("div",{className:"w-full mx-1",children:e.jsx(l.Button,{type:"submit",className:"h-12 w-full bg-emerald-700",children:"Filter"})}),e.jsx("div",{className:"w-full mx-1",children:e.jsx(l.Button,{layout:"outline",onClick:q,type:"reset",className:"px-4 md:py-1 py-2 h-12 text-sm dark:bg-gray-700",children:e.jsx("span",{className:"text-black dark:text-gray-200",children:"Reset"})})})]})]})})})]}),a?e.jsx(se,{row:12,col:6,width:180,height:20}):n?e.jsx("span",{className:"text-center mx-auto text-red-500",children:n}):j?.length!==0?e.jsxs(l.TableContainer,{className:"mb-8",children:[e.jsxs(l.Table,{children:[e.jsx(l.TableHeader,{children:e.jsxs("tr",{children:[e.jsx(l.TableCell,{children:e.jsx(_,{type:"checkbox",name:"selectAll",id:"selectAll",handleClick:L,isChecked:b})}),e.jsxs(l.TableCell,{children:[" ",t("Id")," "]}),e.jsxs(l.TableCell,{children:[" ",t("AName")]}),e.jsxs(l.TableCell,{children:[" ",t("ADisplayName")]}),e.jsx(l.TableCell,{children:t("AOption")}),e.jsx(l.TableCell,{className:"text-center",children:t("catPublishedTbl")}),e.jsx(l.TableCell,{className:"text-center",children:t("Avalues")}),e.jsx(l.TableCell,{className:"text-right",children:t("AAction")})]})}),e.jsx(ce,{lang:d,isCheck:p,setIsCheck:h,attributes:g})]}),e.jsx(l.TableFooter,{children:e.jsx(l.Pagination,{totalResults:F,resultsPerPage:E,onChange:R,label:"Table navigation"})})]}):e.jsx(ae,{title:"Sorry, There are no attributes right now."})]})};export{He as default};
