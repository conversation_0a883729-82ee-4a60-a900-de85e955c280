import{a as ne,g as Z,p as _,P as h,j as d,k as oe,r as ae,A as ie,h as se}from"./index-BnbL29JP.js";import{u as ce}from"./useStaffSubmit-D-tFnEKy.js";import{o as le}from"./index-OcClbx-5.js";import{L as N}from"./LabelArea-DQFDcuEN.js";import{U as ue}from"./Uploader-COz_-nhd.js";import{I as k}from"./InputArea-B_vEs1uv.js";import{E as H}from"./index.esm-ClJnGQn6.js";import{S as fe}from"./SelectRole-DrH9K_iP.js";import{A as pe}from"./AnimatedContent-0V4vlNfe.js";import"./Layout-pFzaaQDc.js";import"./iconBase-CKOh_aia.js";import"./AdminServices-DjQuFfvs.js";import"./toast-DZMsp61l.js";import"./useTranslationValue-DCEiB8J2.js";import"./_commonjs-dynamic-modules-BJDvAndU.js";import"./index-C148XJoK.js";var U,X;function de(){if(X)return U;X=1;function s(i){return i&&typeof i=="object"&&"default"in i?i.default:i}var e=ne(),o=s(e);function n(i,t,l){return t in i?Object.defineProperty(i,t,{value:l,enumerable:!0,configurable:!0,writable:!0}):i[t]=l,i}function a(i,t){i.prototype=Object.create(t.prototype),i.prototype.constructor=i,i.__proto__=t}var r=!!(typeof window<"u"&&window.document&&window.document.createElement);function c(i,t,l){if(typeof i!="function")throw new Error("Expected reducePropsToState to be a function.");if(typeof t!="function")throw new Error("Expected handleStateChangeOnClient to be a function.");if(typeof l<"u"&&typeof l!="function")throw new Error("Expected mapStateOnServer to either be undefined or a function.");function u(p){return p.displayName||p.name||"Component"}return function(m){if(typeof m!="function")throw new Error("Expected WrappedComponent to be a React component.");var T=[],g;function b(){g=i(T.map(function(y){return y.props})),E.canUseDOM?t(g):l&&(g=l(g))}var E=function(y){a(S,y);function S(){return y.apply(this,arguments)||this}S.peek=function(){return g},S.rewind=function(){if(S.canUseDOM)throw new Error("You may only call rewind() on the server. Call peek() to read the current state.");var D=g;return g=void 0,T=[],D};var O=S.prototype;return O.UNSAFE_componentWillMount=function(){T.push(this),b()},O.componentDidUpdate=function(){b()},O.componentWillUnmount=function(){var D=T.indexOf(this);T.splice(D,1),b()},O.render=function(){return o.createElement(m,this.props)},S}(e.PureComponent);return n(E,"displayName","SideEffect("+u(m)+")"),n(E,"canUseDOM",r),E}}return U=c,U}var me=de();const Te=Z(me);var q,V;function he(){if(V)return q;V=1;var s=typeof Element<"u",e=typeof Map=="function",o=typeof Set=="function",n=typeof ArrayBuffer=="function"&&!!ArrayBuffer.isView;function a(r,c){if(r===c)return!0;if(r&&c&&typeof r=="object"&&typeof c=="object"){if(r.constructor!==c.constructor)return!1;var i,t,l;if(Array.isArray(r)){if(i=r.length,i!=c.length)return!1;for(t=i;t--!==0;)if(!a(r[t],c[t]))return!1;return!0}var u;if(e&&r instanceof Map&&c instanceof Map){if(r.size!==c.size)return!1;for(u=r.entries();!(t=u.next()).done;)if(!c.has(t.value[0]))return!1;for(u=r.entries();!(t=u.next()).done;)if(!a(t.value[1],c.get(t.value[0])))return!1;return!0}if(o&&r instanceof Set&&c instanceof Set){if(r.size!==c.size)return!1;for(u=r.entries();!(t=u.next()).done;)if(!c.has(t.value[0]))return!1;return!0}if(n&&ArrayBuffer.isView(r)&&ArrayBuffer.isView(c)){if(i=r.length,i!=c.length)return!1;for(t=i;t--!==0;)if(r[t]!==c[t])return!1;return!0}if(r.constructor===RegExp)return r.source===c.source&&r.flags===c.flags;if(r.valueOf!==Object.prototype.valueOf&&typeof r.valueOf=="function"&&typeof c.valueOf=="function")return r.valueOf()===c.valueOf();if(r.toString!==Object.prototype.toString&&typeof r.toString=="function"&&typeof c.toString=="function")return r.toString()===c.toString();if(l=Object.keys(r),i=l.length,i!==Object.keys(c).length)return!1;for(t=i;t--!==0;)if(!Object.prototype.hasOwnProperty.call(c,l[t]))return!1;if(s&&r instanceof Element)return!1;for(t=i;t--!==0;)if(!((l[t]==="_owner"||l[t]==="__v"||l[t]==="__o")&&r.$$typeof)&&!a(r[l[t]],c[l[t]]))return!1;return!0}return r!==r&&c!==c}return q=function(c,i){try{return a(c,i)}catch(t){if((t.message||"").match(/stack|recursion/i))return console.warn("react-fast-compare cannot handle circular refs"),!1;throw t}},q}var ve=he();const ge=Z(ve);var w={BODY:"bodyAttributes",HTML:"htmlAttributes",TITLE:"titleAttributes"},f={BASE:"base",BODY:"body",HEAD:"head",HTML:"html",LINK:"link",META:"meta",NOSCRIPT:"noscript",SCRIPT:"script",STYLE:"style",TITLE:"title"};Object.keys(f).map(function(s){return f[s]});var v={CHARSET:"charset",CSS_TEXT:"cssText",HREF:"href",HTTPEQUIV:"http-equiv",INNER_HTML:"innerHTML",ITEM_PROP:"itemprop",NAME:"name",PROPERTY:"property",REL:"rel",SRC:"src",TARGET:"target"},F={accesskey:"accessKey",charset:"charSet",class:"className",contenteditable:"contentEditable",contextmenu:"contextMenu","http-equiv":"httpEquiv",itemprop:"itemProp",tabindex:"tabIndex"},M={DEFAULT_TITLE:"defaultTitle",DEFER:"defer",ENCODE_SPECIAL_CHARACTERS:"encodeSpecialCharacters",ON_CHANGE_CLIENT_STATE:"onChangeClientState",TITLE_TEMPLATE:"titleTemplate"},Ee=Object.keys(F).reduce(function(s,e){return s[F[e]]=e,s},{}),ye=[f.NOSCRIPT,f.SCRIPT,f.STYLE],C="data-react-helmet",Ae=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(s){return typeof s}:function(s){return s&&typeof Symbol=="function"&&s.constructor===Symbol&&s!==Symbol.prototype?"symbol":typeof s},be=function(s,e){if(!(s instanceof e))throw new TypeError("Cannot call a class as a function")},Se=function(){function s(e,o){for(var n=0;n<o.length;n++){var a=o[n];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(e,a.key,a)}}return function(e,o,n){return o&&s(e.prototype,o),n&&s(e,n),e}}(),A=Object.assign||function(s){for(var e=1;e<arguments.length;e++){var o=arguments[e];for(var n in o)Object.prototype.hasOwnProperty.call(o,n)&&(s[n]=o[n])}return s},Ce=function(s,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof e);s.prototype=Object.create(e&&e.prototype,{constructor:{value:s,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(s,e):s.__proto__=e)},W=function(s,e){var o={};for(var n in s)e.indexOf(n)>=0||Object.prototype.hasOwnProperty.call(s,n)&&(o[n]=s[n]);return o},Pe=function(s,e){if(!s)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&(typeof e=="object"||typeof e=="function")?e:s},Y=function(e){var o=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;return o===!1?String(e):String(e).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#x27;")},Oe=function(e){var o=j(e,f.TITLE),n=j(e,M.TITLE_TEMPLATE);if(n&&o)return n.replace(/%s/g,function(){return Array.isArray(o)?o.join(""):o});var a=j(e,M.DEFAULT_TITLE);return o||a||void 0},xe=function(e){return j(e,M.ON_CHANGE_CLIENT_STATE)||function(){}},B=function(e,o){return o.filter(function(n){return typeof n[e]<"u"}).map(function(n){return n[e]}).reduce(function(n,a){return A({},n,a)},{})},we=function(e,o){return o.filter(function(n){return typeof n[f.BASE]<"u"}).map(function(n){return n[f.BASE]}).reverse().reduce(function(n,a){if(!n.length)for(var r=Object.keys(a),c=0;c<r.length;c++){var i=r[c],t=i.toLowerCase();if(e.indexOf(t)!==-1&&a[t])return n.concat(a)}return n},[])},I=function(e,o,n){var a={};return n.filter(function(r){return Array.isArray(r[e])?!0:(typeof r[e]<"u"&&Ie("Helmet: "+e+' should be of type "Array". Instead found type "'+Ae(r[e])+'"'),!1)}).map(function(r){return r[e]}).reverse().reduce(function(r,c){var i={};c.filter(function(m){for(var T=void 0,g=Object.keys(m),b=0;b<g.length;b++){var E=g[b],y=E.toLowerCase();o.indexOf(y)!==-1&&!(T===v.REL&&m[T].toLowerCase()==="canonical")&&!(y===v.REL&&m[y].toLowerCase()==="stylesheet")&&(T=y),o.indexOf(E)!==-1&&(E===v.INNER_HTML||E===v.CSS_TEXT||E===v.ITEM_PROP)&&(T=E)}if(!T||!m[T])return!1;var S=m[T].toLowerCase();return a[T]||(a[T]={}),i[T]||(i[T]={}),a[T][S]?!1:(i[T][S]=!0,!0)}).reverse().forEach(function(m){return r.push(m)});for(var t=Object.keys(i),l=0;l<t.length;l++){var u=t[l],p=le({},a[u],i[u]);a[u]=p}return r},[]).reverse()},j=function(e,o){for(var n=e.length-1;n>=0;n--){var a=e[n];if(a.hasOwnProperty(o))return a[o]}return null},Re=function(e){return{baseTag:we([v.HREF,v.TARGET],e),bodyAttributes:B(w.BODY,e),defer:j(e,M.DEFER),encode:j(e,M.ENCODE_SPECIAL_CHARACTERS),htmlAttributes:B(w.HTML,e),linkTags:I(f.LINK,[v.REL,v.HREF],e),metaTags:I(f.META,[v.NAME,v.CHARSET,v.HTTPEQUIV,v.PROPERTY,v.ITEM_PROP],e),noscriptTags:I(f.NOSCRIPT,[v.INNER_HTML],e),onChangeClientState:xe(e),scriptTags:I(f.SCRIPT,[v.SRC,v.INNER_HTML],e),styleTags:I(f.STYLE,[v.CSS_TEXT],e),title:Oe(e),titleAttributes:B(w.TITLE,e)}},G=function(){var s=Date.now();return function(e){var o=Date.now();o-s>16?(s=o,e(o)):setTimeout(function(){G(e)},0)}}(),Q=function(e){return clearTimeout(e)},je=typeof window<"u"?window.requestAnimationFrame&&window.requestAnimationFrame.bind(window)||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||G:global.requestAnimationFrame||G,Ne=typeof window<"u"?window.cancelAnimationFrame||window.webkitCancelAnimationFrame||window.mozCancelAnimationFrame||Q:global.cancelAnimationFrame||Q,Ie=function(e){return console&&typeof console.warn=="function"&&console.warn(e)},L=null,Le=function(e){L&&Ne(L),e.defer?L=je(function(){J(e,function(){L=null})}):(J(e),L=null)},J=function(e,o){var n=e.baseTag,a=e.bodyAttributes,r=e.htmlAttributes,c=e.linkTags,i=e.metaTags,t=e.noscriptTags,l=e.onChangeClientState,u=e.scriptTags,p=e.styleTags,m=e.title,T=e.titleAttributes;$(f.BODY,a),$(f.HTML,r),_e(m,T);var g={baseTag:R(f.BASE,n),linkTags:R(f.LINK,c),metaTags:R(f.META,i),noscriptTags:R(f.NOSCRIPT,t),scriptTags:R(f.SCRIPT,u),styleTags:R(f.STYLE,p)},b={},E={};Object.keys(g).forEach(function(y){var S=g[y],O=S.newTags,x=S.oldTags;O.length&&(b[y]=O),x.length&&(E[y]=g[y].oldTags)}),o&&o(),l(e,b,E)},K=function(e){return Array.isArray(e)?e.join(""):e},_e=function(e,o){typeof e<"u"&&document.title!==e&&(document.title=K(e)),$(f.TITLE,o)},$=function(e,o){var n=document.getElementsByTagName(e)[0];if(n){for(var a=n.getAttribute(C),r=a?a.split(","):[],c=[].concat(r),i=Object.keys(o),t=0;t<i.length;t++){var l=i[t],u=o[l]||"";n.getAttribute(l)!==u&&n.setAttribute(l,u),r.indexOf(l)===-1&&r.push(l);var p=c.indexOf(l);p!==-1&&c.splice(p,1)}for(var m=c.length-1;m>=0;m--)n.removeAttribute(c[m]);r.length===c.length?n.removeAttribute(C):n.getAttribute(C)!==i.join(",")&&n.setAttribute(C,i.join(","))}},R=function(e,o){var n=document.head||document.querySelector(f.HEAD),a=n.querySelectorAll(e+"["+C+"]"),r=Array.prototype.slice.call(a),c=[],i=void 0;return o&&o.length&&o.forEach(function(t){var l=document.createElement(e);for(var u in t)if(t.hasOwnProperty(u))if(u===v.INNER_HTML)l.innerHTML=t.innerHTML;else if(u===v.CSS_TEXT)l.styleSheet?l.styleSheet.cssText=t.cssText:l.appendChild(document.createTextNode(t.cssText));else{var p=typeof t[u]>"u"?"":t[u];l.setAttribute(u,p)}l.setAttribute(C,"true"),r.some(function(m,T){return i=T,l.isEqualNode(m)})?r.splice(i,1):c.push(l)}),r.forEach(function(t){return t.parentNode.removeChild(t)}),c.forEach(function(t){return n.appendChild(t)}),{oldTags:r,newTags:c}},ee=function(e){return Object.keys(e).reduce(function(o,n){var a=typeof e[n]<"u"?n+'="'+e[n]+'"':""+n;return o?o+" "+a:a},"")},Me=function(e,o,n,a){var r=ee(n),c=K(o);return r?"<"+e+" "+C+'="true" '+r+">"+Y(c,a)+"</"+e+">":"<"+e+" "+C+'="true">'+Y(c,a)+"</"+e+">"},He=function(e,o,n){return o.reduce(function(a,r){var c=Object.keys(r).filter(function(l){return!(l===v.INNER_HTML||l===v.CSS_TEXT)}).reduce(function(l,u){var p=typeof r[u]>"u"?u:u+'="'+Y(r[u],n)+'"';return l?l+" "+p:p},""),i=r.innerHTML||r.cssText||"",t=ye.indexOf(e)===-1;return a+"<"+e+" "+C+'="true" '+c+(t?"/>":">"+i+"</"+e+">")},"")},te=function(e){var o=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};return Object.keys(e).reduce(function(n,a){return n[F[a]||a]=e[a],n},o)},Fe=function(e){var o=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};return Object.keys(e).reduce(function(n,a){return n[Ee[a]||a]=e[a],n},o)},De=function(e,o,n){var a,r=(a={key:o},a[C]=!0,a),c=te(n,r);return[_.createElement(f.TITLE,c,o)]},ke=function(e,o){return o.map(function(n,a){var r,c=(r={key:a},r[C]=!0,r);return Object.keys(n).forEach(function(i){var t=F[i]||i;if(t===v.INNER_HTML||t===v.CSS_TEXT){var l=n.innerHTML||n.cssText;c.dangerouslySetInnerHTML={__html:l}}else c[t]=n[i]}),_.createElement(e,c)})},P=function(e,o,n){switch(e){case f.TITLE:return{toComponent:function(){return De(e,o.title,o.titleAttributes)},toString:function(){return Me(e,o.title,o.titleAttributes,n)}};case w.BODY:case w.HTML:return{toComponent:function(){return te(o)},toString:function(){return ee(o)}};default:return{toComponent:function(){return ke(e,o)},toString:function(){return He(e,o,n)}}}},re=function(e){var o=e.baseTag,n=e.bodyAttributes,a=e.encode,r=e.htmlAttributes,c=e.linkTags,i=e.metaTags,t=e.noscriptTags,l=e.scriptTags,u=e.styleTags,p=e.title,m=p===void 0?"":p,T=e.titleAttributes;return{base:P(f.BASE,o,a),bodyAttributes:P(w.BODY,n,a),htmlAttributes:P(w.HTML,r,a),link:P(f.LINK,c,a),meta:P(f.META,i,a),noscript:P(f.NOSCRIPT,t,a),script:P(f.SCRIPT,l,a),style:P(f.STYLE,u,a),title:P(f.TITLE,{title:m,titleAttributes:T},a)}},Ue=function(e){var o,n;return n=o=function(a){Ce(r,a);function r(){return be(this,r),Pe(this,a.apply(this,arguments))}return r.prototype.shouldComponentUpdate=function(i){return!ge(this.props,i)},r.prototype.mapNestedChildrenToProps=function(i,t){if(!t)return null;switch(i.type){case f.SCRIPT:case f.NOSCRIPT:return{innerHTML:t};case f.STYLE:return{cssText:t}}throw new Error("<"+i.type+" /> elements are self-closing and can not contain children. Refer to our API for more information.")},r.prototype.flattenArrayTypeChildren=function(i){var t,l=i.child,u=i.arrayTypeChildren,p=i.newChildProps,m=i.nestedChildren;return A({},u,(t={},t[l.type]=[].concat(u[l.type]||[],[A({},p,this.mapNestedChildrenToProps(l,m))]),t))},r.prototype.mapObjectTypeChildren=function(i){var t,l,u=i.child,p=i.newProps,m=i.newChildProps,T=i.nestedChildren;switch(u.type){case f.TITLE:return A({},p,(t={},t[u.type]=T,t.titleAttributes=A({},m),t));case f.BODY:return A({},p,{bodyAttributes:A({},m)});case f.HTML:return A({},p,{htmlAttributes:A({},m)})}return A({},p,(l={},l[u.type]=A({},m),l))},r.prototype.mapArrayTypeChildrenToProps=function(i,t){var l=A({},t);return Object.keys(i).forEach(function(u){var p;l=A({},l,(p={},p[u]=i[u],p))}),l},r.prototype.warnOnInvalidChildren=function(i,t){return!0},r.prototype.mapChildrenToProps=function(i,t){var l=this,u={};return _.Children.forEach(i,function(p){if(!(!p||!p.props)){var m=p.props,T=m.children,g=W(m,["children"]),b=Fe(g);switch(l.warnOnInvalidChildren(p,T),p.type){case f.LINK:case f.META:case f.NOSCRIPT:case f.SCRIPT:case f.STYLE:u=l.flattenArrayTypeChildren({child:p,arrayTypeChildren:u,newChildProps:b,nestedChildren:T});break;default:t=l.mapObjectTypeChildren({child:p,newProps:t,newChildProps:b,nestedChildren:T});break}}}),t=this.mapArrayTypeChildrenToProps(u,t),t},r.prototype.render=function(){var i=this.props,t=i.children,l=W(i,["children"]),u=A({},l);return t&&(u=this.mapChildrenToProps(t,u)),_.createElement(e,u)},Se(r,null,[{key:"canUseDOM",set:function(i){e.canUseDOM=i}}]),r}(_.Component),o.propTypes={base:h.object,bodyAttributes:h.object,children:h.oneOfType([h.arrayOf(h.node),h.node]),defaultTitle:h.string,defer:h.bool,encodeSpecialCharacters:h.bool,htmlAttributes:h.object,link:h.arrayOf(h.object),meta:h.arrayOf(h.object),noscript:h.arrayOf(h.object),onChangeClientState:h.func,script:h.arrayOf(h.object),style:h.arrayOf(h.object),title:h.string,titleAttributes:h.object,titleTemplate:h.string},o.defaultProps={defer:!0,encodeSpecialCharacters:!0},o.peek=e.peek,o.rewind=function(){var a=e.rewind();return a||(a=re({baseTag:[],bodyAttributes:{},htmlAttributes:{},linkTags:[],metaTags:[],noscriptTags:[],scriptTags:[],styleTags:[],title:"",titleAttributes:{}})),a},n},qe=function(){return null},Be=Te(Re,Le,re)(qe),z=Ue(Be);z.renderStatic=z.rewind;const Ye=({title:s,description:e})=>d.jsxs(z,{children:[d.jsxs("title",{children:[" ",s?`${s} | React eCommerce Admin Dashboard`:"Gloopi | React eCommerce Admin Dashboard"]}),d.jsx("meta",{name:"description",content:e?` ${e} `:"Gloopi : React Grocery & Organic Food Store e-commerce Admin Dashboard"})]}),it=()=>{const{t:s}=oe(),{state:{adminInfo:e}}=ae.useContext(ie),{register:o,handleSubmit:n,onSubmit:a,errors:r,imageUrl:c,setImageUrl:i}=ce(e._id);return d.jsxs(d.Fragment,{children:[d.jsxs(Ye,{children:[" ",s("EditProfile")," "]}),d.jsx(pe,{children:d.jsx("div",{className:"container p-6 mx-auto bg-white  dark:bg-gray-800 dark:text-gray-200 rounded-lg",children:d.jsxs("form",{onSubmit:n(a),children:[d.jsxs("div",{className:"p-6 flex-grow scrollbar-hide w-full max-h-full",children:[d.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[d.jsx(N,{label:s("ProfilePicture")}),d.jsx("div",{className:"col-span-8 sm:col-span-4",children:d.jsx(ue,{imageUrl:c,setImageUrl:i,folder:"customer"})})]}),d.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[d.jsx(N,{label:s("ProfileName")}),d.jsxs("div",{className:"col-span-8 sm:col-span-4",children:[d.jsx(k,{required:!0,register:o,label:"Name",name:"name",type:"text",placeholder:"Your Name"}),d.jsx(H,{errorName:r.name})]})]}),d.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[d.jsx(N,{label:s("ProfileEmail")}),d.jsxs("div",{className:"col-span-8 sm:col-span-4",children:[d.jsx(k,{required:!0,register:o,label:"Email",name:"email",type:"text",placeholder:"Email"}),d.jsx(H,{errorName:r.email})]})]}),d.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[d.jsx(N,{label:s("ProfileContactNumber")}),d.jsxs("div",{className:"col-span-8 sm:col-span-4",children:[d.jsx(k,{required:!0,register:o,label:"Contact Number",name:"phone",type:"text",placeholder:"Contact Number"}),d.jsx(H,{errorName:r.phone})]})]}),d.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[d.jsx(N,{label:s("ProfileYourRole")}),d.jsxs("div",{className:"col-span-8 sm:col-span-4",children:[d.jsx(fe,{register:o,label:"Role",name:"role"}),d.jsx(H,{errorName:r.role})]})]})]}),d.jsx("div",{className:"flex flex-row-reverse pr-6 pb-6",children:d.jsx(se.Button,{type:"submit",className:"h-12 px-6",children:s("updateProfile")})})]})})})]})};export{it as default};
