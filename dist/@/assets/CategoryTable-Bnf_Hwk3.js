import{r as g,S as Y,k as G,j as e,h as o,l as F}from"./index-BnbL29JP.js";import{u as V,S as J,I as Q}from"./Layout-pFzaaQDc.js";import{T as X,C as Z,S as ee}from"./BulkActionDrawer-0F-ByaaS.js";import{T as O,D as se,u as ae,M as te}from"./DrawerButton-BQHT-xfW.js";import{D as re,E as ne}from"./EditDeleteButton-BfkOlssy.js";import{T as le}from"./ParentCategory-BlfDuDd5.js";import{a as k,n as M}from"./toast-DZMsp61l.js";import{u as ie,E as R}from"./index.esm-ClJnGQn6.js";import{I as oe}from"./InputArea-B_vEs1uv.js";import{L as A}from"./LabelArea-DQFDcuEN.js";import{S as ce}from"./SwitchToggle-CGiqq8S_.js";import{U as de}from"./Uploader-COz_-nhd.js";import{C as L}from"./ProductServices-CXwJ-2YB.js";import{u as me}from"./useTranslationValue-DCEiB8J2.js";const pe=(i,c)=>{const{isDrawerOpen:l,closeDrawer:b,setIsUpdate:d,lang:S}=g.useContext(Y),[h,f]=g.useState({}),[x,y]=g.useState(""),[v,m]=g.useState(""),[T,s]=g.useState([]),[n,C]=g.useState("en"),[w,N]=g.useState(!0),[I,D]=g.useState(""),[E,j]=g.useState(!1),{handlerTextTranslateHandler:U}=me(),{register:t,handleSubmit:p,setValue:a,clearErrors:u,reset:q,formState:{errors:$}}=ie(),z=async({name:r,description:H})=>{try{j(!0);const _=await U(r,n,h?.name),W=await U(H,n,h?.description),B={name:{..._,[n]:r},description:{...W,[n]:H||""},parentId:x||void 0,parentName:I||"Home",icon:v,status:w?"show":"hide",lang:n};if(i){const P=await L.updateCategory(i,B);d(!0),j(!1),M(P.message),b(),q()}else{const P=await L.addCategory(B);d(!0),j(!1),M(P.message),b()}}catch(_){j(!1),k(_?_?.response?.data?.message:_?.message),b()}},K=r=>{C(r),Object.keys(h).length>0&&(a("name",h.name[r||"en"]),a("description",h.description[r||"en"]))};return g.useEffect(()=>{if(!l){f({}),a("name"),a("parentId"),a("parentName"),a("description"),a("icon"),m(""),N(!0),u("name"),u("parentId"),u("parentName"),u("description"),D("Home"),C(S),a("language",n),c!==void 0&&c[0]?._id!==void 0&&y(c[0]._id);return}i&&(async()=>{try{const r=await L.getCategoryById(i);r&&(f(r),a("name",r.name[n||"en"]),a("description",r.description[n||"en"]),a("language",n),a("parentId",r.parentId),a("parentName",r.parentName),D(r.parentName),y(r.parentId),m(r.icon),N(r.status==="show"))}catch(r){k(r?r.response.data.message:r.message)}})()},[i,a,l,n,u,c,S]),{register:t,handleSubmit:p,onSubmit:z,errors:$,imageUrl:v,setImageUrl:m,children:T,setChildren:s,published:w,setPublished:N,checked:x,setChecked:y,isSubmitting:E,selectCategoryName:I,setSelectCategoryName:D,handleSelectLanguage:K}},ge=({id:i,data:c})=>{const{t:l}=G(),{checked:b,register:d,onSubmit:S,handleSubmit:h,errors:f,imageUrl:x,setImageUrl:y,published:v,setPublished:m,setChecked:T,selectCategoryName:s,setSelectCategoryName:n,handleSelectLanguage:C,isSubmitting:w}=pe(i,c),{showingTranslateValue:N}=V(),I=`
  .rc-tree-child-tree {
    display: hidden;
  }
  .node-motion {
    transition: all .3s;
    overflow-y: hidden;
  }
`,D={motionName:"node-motion",motionAppear:!1,onAppearStart:t=>({height:0}),onAppearActive:t=>({height:t.scrollHeight}),onLeaveStart:t=>({height:t.offsetHeight}),onLeaveActive:()=>({height:0})},E=t=>{let p=[];for(let a of t)p.push({title:N(a.name),key:a._id,children:a.children.length>0&&E(a.children)});return p},j=(t,p)=>t._id===p?t:t?.children?.reduce((a,u)=>a??j(u,p),void 0),U=async t=>{if(t!==void 0)if(i){const p=await L.getCategoryById(t);if(i===t)return k("This can't be select as a parent category!");if(i===p.parentId)return k("This can't be select as a parent category!");{if(t===void 0)return;T(t);const a=c[0],u=j(a,t);n(N(u?.name))}}else{if(t===void 0)return;T(t);const p=c[0],a=j(p,t);n(N(a?.name))}};return e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"w-full relative p-6 border-b border-gray-100 bg-gray-50 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-300",children:i?e.jsx(O,{register:d,handleSelectLanguage:C,title:l("UpdateCategory"),description:l("UpdateCategoryDescription")}):e.jsx(O,{register:d,handleSelectLanguage:C,title:l("AddCategoryTitle"),description:l("AddCategoryDescription")})}),e.jsx(J,{className:"w-full md:w-7/12 lg:w-8/12 xl:w-8/12 relative dark:bg-gray-700 dark:text-gray-200",children:e.jsxs("form",{onSubmit:h(S),children:[e.jsxs("div",{className:"p-6 flex-grow scrollbar-hide w-full max-h-full pb-40",children:[e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(A,{label:l("Name")}),e.jsxs("div",{className:"col-span-8 sm:col-span-4",children:[e.jsx(oe,{required:!0,register:d,label:"Category title",name:"name",type:"text",placeholder:l("ParentCategoryPlaceholder")}),e.jsx(R,{errorName:f.name})]})]}),e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(A,{label:l("Description")}),e.jsxs("div",{className:"col-span-8 sm:col-span-4",children:[e.jsx(X,{register:d,label:"Description",name:"description",type:"text",placeholder:"Category Description"}),e.jsx(R,{errorName:f.description})]})]}),e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(A,{label:l("ParentCategory")}),e.jsxs("div",{className:"col-span-8 sm:col-span-4 relative",children:[e.jsx(o.Input,{readOnly:!0,...d("parent",{required:!1}),name:"parent",value:s||"Home",placeholder:l("ParentCategory"),type:"text"}),e.jsxs("div",{className:"draggable-demo capitalize",children:[e.jsx("style",{dangerouslySetInnerHTML:{__html:I}}),e.jsx(le,{expandAction:"click",treeData:E(c),selectedKeys:[b],onSelect:t=>U(t[0]),motion:D,animation:"slide-up"})]})]})]}),e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(A,{label:l("CategoryIcon")}),e.jsx("div",{className:"col-span-8 sm:col-span-4",children:e.jsx(de,{imageUrl:x,setImageUrl:y,folder:"category",targetWidth:238,targetHeight:238})})]}),e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(A,{label:l("Published")}),e.jsx("div",{className:"col-span-8 sm:col-span-4",children:e.jsx(ce,{handleProcess:m,processOption:v})})]})]}),e.jsx(se,{id:i,title:"Category",isSubmitting:w})]})})]})},De=({data:i,lang:c,isCheck:l,categories:b,setIsCheck:d,useParamId:S,showChild:h})=>{const{title:f,serviceId:x,handleModalOpen:y,handleUpdate:v}=ae(),{showingTranslateValue:m}=V(),T=s=>{const{id:n,checked:C}=s.target;d([...l,n]),C||d(l.filter(w=>w!==n))};return e.jsxs(e.Fragment,{children:[l?.length<1&&e.jsx(re,{useParamId:S,id:x,title:f}),e.jsx(te,{children:e.jsx(ge,{id:x,data:i,lang:c})}),e.jsx(o.TableBody,{children:b?.map(s=>e.jsxs(o.TableRow,{children:[e.jsx(o.TableCell,{children:e.jsx(Z,{type:"checkbox",name:"category",id:s._id,handleClick:T,isChecked:l?.includes(s._id)})}),e.jsx(o.TableCell,{className:"font-semibold uppercase text-xs",children:s?._id?.substring(20,24)}),e.jsx(o.TableCell,{children:s?.icon?e.jsx(o.Avatar,{className:"hidden mr-3 md:block bg-gray-50 p-1",src:s?.icon,alt:s?.parent}):e.jsx(o.Avatar,{src:"https://res.cloudinary.com/ahossain/image/upload/v1655097002/placeholder_kvepfp.png",alt:"product",className:"hidden p-1 mr-2 md:block bg-gray-50 shadow-none"})}),e.jsx(o.TableCell,{className:"font-medium text-sm ",children:s?.children.length>0?e.jsxs(F,{to:`/categories/${s?._id}`,className:"text-blue-700",children:[m(s?.name),e.jsx(e.Fragment,{children:h&&e.jsxs(e.Fragment,{children:[" ",e.jsx("div",{className:"pl-2 ",children:s?.children?.map(n=>e.jsx("div",{children:e.jsx(F,{to:`/categories/${n?._id}`,className:"text-blue-700",children:e.jsxs("div",{className:"flex text-xs items-center  text-blue-800",children:[e.jsx("span",{className:" text-xs text-gray-500 pr-1",children:e.jsx(Q,{})}),e.jsx("span",{className:"text-gray-500",children:m(n.name)})]})})},n._id))})]})})]}):e.jsx("span",{children:m(s?.name)})}),e.jsx(o.TableCell,{className:"text-sm",children:m(s?.description)}),e.jsx(o.TableCell,{className:"text-center",children:e.jsx(ee,{id:s._id,category:!0,status:s.status})}),e.jsx(o.TableCell,{children:e.jsx(ne,{id:s?._id,parent:s,isCheck:l,children:s?.children,handleUpdate:v,handleModalOpen:y,title:m(s?.name)})})]},s._id))})]})};export{ge as C,De as a};
