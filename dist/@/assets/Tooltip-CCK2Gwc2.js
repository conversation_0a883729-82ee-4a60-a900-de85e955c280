import{g as go,p as _e,r as p,j as $e}from"./index-BnbL29JP.js";const Me=Math.min,Se=Math.max,ht=Math.round,mt=Math.floor,ue=e=>({x:e,y:e}),wo={left:"right",right:"left",bottom:"top",top:"bottom"},bo={start:"end",end:"start"};function Ct(e,t,n){return Se(e,Me(t,n))}function rt(e,t){return typeof e=="function"?e(t):e}function Ae(e){return e.split("-")[0]}function lt(e){return e.split("-")[1]}function no(e){return e==="x"?"y":"x"}function Dt(e){return e==="y"?"height":"width"}function Fe(e){return["top","bottom"].includes(Ae(e))?"y":"x"}function jt(e){return no(Fe(e))}function xo(e,t,n){n===void 0&&(n=!1);const o=lt(e),r=jt(e),l=Dt(r);let s=r==="x"?o===(n?"end":"start")?"right":"left":o==="start"?"bottom":"top";return t.reference[l]>t.floating[l]&&(s=vt(s)),[s,vt(s)]}function Eo(e){const t=vt(e);return[Tt(e),t,Tt(t)]}function Tt(e){return e.replace(/start|end/g,t=>bo[t])}function _o(e,t,n){const o=["left","right"],r=["right","left"],l=["top","bottom"],s=["bottom","top"];switch(e){case"top":case"bottom":return n?t?r:o:t?o:r;case"left":case"right":return t?l:s;default:return[]}}function So(e,t,n,o){const r=lt(e);let l=_o(Ae(e),n==="start",o);return r&&(l=l.map(s=>s+"-"+r),t&&(l=l.concat(l.map(Tt)))),l}function vt(e){return e.replace(/left|right|bottom|top/g,t=>wo[t])}function Ao(e){return{top:0,right:0,bottom:0,left:0,...e}}function ro(e){return typeof e!="number"?Ao(e):{top:e,right:e,bottom:e,left:e}}function yt(e){const{x:t,y:n,width:o,height:r}=e;return{width:o,height:r,top:n,left:t,right:t+o,bottom:n+r,x:t,y:n}}function Vt(e,t,n){let{reference:o,floating:r}=e;const l=Fe(t),s=jt(t),i=Dt(s),c=Ae(t),a=l==="y",m=o.x+o.width/2-r.width/2,d=o.y+o.height/2-r.height/2,g=o[i]/2-r[i]/2;let f;switch(c){case"top":f={x:m,y:o.y-r.height};break;case"bottom":f={x:m,y:o.y+o.height};break;case"right":f={x:o.x+o.width,y:d};break;case"left":f={x:o.x-r.width,y:d};break;default:f={x:o.x,y:o.y}}switch(lt(t)){case"start":f[s]-=g*(n&&a?-1:1);break;case"end":f[s]+=g*(n&&a?-1:1);break}return f}const Oo=async(e,t,n)=>{const{placement:o="bottom",strategy:r="absolute",middleware:l=[],platform:s}=n,i=l.filter(Boolean),c=await(s.isRTL==null?void 0:s.isRTL(t));let a=await s.getElementRects({reference:e,floating:t,strategy:r}),{x:m,y:d}=Vt(a,o,c),g=o,f={},h=0;for(let w=0;w<i.length;w++){const{name:E,fn:b}=i[w],{x:S,y:_,data:C,reset:O}=await b({x:m,y:d,initialPlacement:o,placement:g,strategy:r,middlewareData:f,rects:a,platform:s,elements:{reference:e,floating:t}});m=S??m,d=_??d,f={...f,[E]:{...f[E],...C}},O&&h<=50&&(h++,typeof O=="object"&&(O.placement&&(g=O.placement),O.rects&&(a=O.rects===!0?await s.getElementRects({reference:e,floating:t,strategy:r}):O.rects),{x:m,y:d}=Vt(a,g,c)),w=-1)}return{x:m,y:d,placement:g,strategy:r,middlewareData:f}};async function lo(e,t){var n;t===void 0&&(t={});const{x:o,y:r,platform:l,rects:s,elements:i,strategy:c}=e,{boundary:a="clippingAncestors",rootBoundary:m="viewport",elementContext:d="floating",altBoundary:g=!1,padding:f=0}=rt(t,e),h=ro(f),E=i[g?d==="floating"?"reference":"floating":d],b=yt(await l.getClippingRect({element:(n=await(l.isElement==null?void 0:l.isElement(E)))==null||n?E:E.contextElement||await(l.getDocumentElement==null?void 0:l.getDocumentElement(i.floating)),boundary:a,rootBoundary:m,strategy:c})),S=d==="floating"?{x:o,y:r,width:s.floating.width,height:s.floating.height}:s.reference,_=await(l.getOffsetParent==null?void 0:l.getOffsetParent(i.floating)),C=await(l.isElement==null?void 0:l.isElement(_))?await(l.getScale==null?void 0:l.getScale(_))||{x:1,y:1}:{x:1,y:1},O=yt(l.convertOffsetParentRelativeRectToViewportRelativeRect?await l.convertOffsetParentRelativeRectToViewportRelativeRect({elements:i,rect:S,offsetParent:_,strategy:c}):S);return{top:(b.top-O.top+h.top)/C.y,bottom:(O.bottom-b.bottom+h.bottom)/C.y,left:(b.left-O.left+h.left)/C.x,right:(O.right-b.right+h.right)/C.x}}const Ro=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:o,placement:r,rects:l,platform:s,elements:i,middlewareData:c}=t,{element:a,padding:m=0}=rt(e,t)||{};if(a==null)return{};const d=ro(m),g={x:n,y:o},f=jt(r),h=Dt(f),w=await s.getDimensions(a),E=f==="y",b=E?"top":"left",S=E?"bottom":"right",_=E?"clientHeight":"clientWidth",C=l.reference[h]+l.reference[f]-g[f]-l.floating[h],O=g[f]-l.reference[f],q=await(s.getOffsetParent==null?void 0:s.getOffsetParent(a));let L=q?q[_]:0;(!L||!await(s.isElement==null?void 0:s.isElement(q)))&&(L=i.floating[_]||l.floating[h]);const J=C/2-O/2,H=L/2-w[h]/2-1,Q=Me(d[b],H),se=Me(d[S],H),Z=Q,ie=L-w[h]-se,T=L/2-w[h]/2+J,K=Ct(Z,T,ie),F=!c.arrow&&lt(r)!=null&&T!==K&&l.reference[h]/2-(T<Z?Q:se)-w[h]/2<0,B=F?T<Z?T-Z:T-ie:0;return{[f]:g[f]+B,data:{[f]:K,centerOffset:T-K-B,...F&&{alignmentOffset:B}},reset:F}}}),Co=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n,o;const{placement:r,middlewareData:l,rects:s,initialPlacement:i,platform:c,elements:a}=t,{mainAxis:m=!0,crossAxis:d=!0,fallbackPlacements:g,fallbackStrategy:f="bestFit",fallbackAxisSideDirection:h="none",flipAlignment:w=!0,...E}=rt(e,t);if((n=l.arrow)!=null&&n.alignmentOffset)return{};const b=Ae(r),S=Fe(i),_=Ae(i)===i,C=await(c.isRTL==null?void 0:c.isRTL(a.floating)),O=g||(_||!w?[vt(i)]:Eo(i)),q=h!=="none";!g&&q&&O.push(...So(i,w,h,C));const L=[i,...O],J=await lo(t,E),H=[];let Q=((o=l.flip)==null?void 0:o.overflows)||[];if(m&&H.push(J[b]),d){const T=xo(r,s,C);H.push(J[T[0]],J[T[1]])}if(Q=[...Q,{placement:r,overflows:H}],!H.every(T=>T<=0)){var se,Z;const T=(((se=l.flip)==null?void 0:se.index)||0)+1,K=L[T];if(K)return{data:{index:T,overflows:Q},reset:{placement:K}};let F=(Z=Q.filter(B=>B.overflows[0]<=0).sort((B,D)=>B.overflows[1]-D.overflows[1])[0])==null?void 0:Z.placement;if(!F)switch(f){case"bestFit":{var ie;const B=(ie=Q.filter(D=>{if(q){const W=Fe(D.placement);return W===S||W==="y"}return!0}).map(D=>[D.placement,D.overflows.filter(W=>W>0).reduce((W,pe)=>W+pe,0)]).sort((D,W)=>D[1]-W[1])[0])==null?void 0:ie[0];B&&(F=B);break}case"initialPlacement":F=i;break}if(r!==F)return{reset:{placement:F}}}return{}}}};async function To(e,t){const{placement:n,platform:o,elements:r}=e,l=await(o.isRTL==null?void 0:o.isRTL(r.floating)),s=Ae(n),i=lt(n),c=Fe(n)==="y",a=["left","top"].includes(s)?-1:1,m=l&&c?-1:1,d=rt(t,e);let{mainAxis:g,crossAxis:f,alignmentAxis:h}=typeof d=="number"?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return i&&typeof h=="number"&&(f=i==="end"?h*-1:h),c?{x:f*m,y:g*a}:{x:g*a,y:f*m}}const Lo=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var n,o;const{x:r,y:l,placement:s,middlewareData:i}=t,c=await To(t,e);return s===((n=i.offset)==null?void 0:n.placement)&&(o=i.arrow)!=null&&o.alignmentOffset?{}:{x:r+c.x,y:l+c.y,data:{...c,placement:s}}}}},ko=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:o,placement:r}=t,{mainAxis:l=!0,crossAxis:s=!1,limiter:i={fn:E=>{let{x:b,y:S}=E;return{x:b,y:S}}},...c}=rt(e,t),a={x:n,y:o},m=await lo(t,c),d=Fe(Ae(r)),g=no(d);let f=a[g],h=a[d];if(l){const E=g==="y"?"top":"left",b=g==="y"?"bottom":"right",S=f+m[E],_=f-m[b];f=Ct(S,f,_)}if(s){const E=d==="y"?"top":"left",b=d==="y"?"bottom":"right",S=h+m[E],_=h-m[b];h=Ct(S,h,_)}const w=i.fn({...t,[g]:f,[d]:h});return{...w,data:{x:w.x-n,y:w.y-o,enabled:{[g]:l,[d]:s}}}}}};function gt(){return typeof window<"u"}function ze(e){return so(e)?(e.nodeName||"").toLowerCase():"#document"}function U(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function de(e){var t;return(t=(so(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function so(e){return gt()?e instanceof Node||e instanceof U(e).Node:!1}function re(e){return gt()?e instanceof Element||e instanceof U(e).Element:!1}function fe(e){return gt()?e instanceof HTMLElement||e instanceof U(e).HTMLElement:!1}function qt(e){return!gt()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof U(e).ShadowRoot}function st(e){const{overflow:t,overflowX:n,overflowY:o,display:r}=le(e);return/auto|scroll|overlay|hidden|clip/.test(t+o+n)&&!["inline","contents"].includes(r)}function No(e){return["table","td","th"].includes(ze(e))}function wt(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch{return!1}})}function Pt(e){const t=$t(),n=re(e)?le(e):e;return["transform","translate","scale","rotate","perspective"].some(o=>n[o]?n[o]!=="none":!1)||(n.containerType?n.containerType!=="normal":!1)||!t&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!t&&(n.filter?n.filter!=="none":!1)||["transform","translate","scale","rotate","perspective","filter"].some(o=>(n.willChange||"").includes(o))||["paint","layout","strict","content"].some(o=>(n.contain||"").includes(o))}function Do(e){let t=ge(e);for(;fe(t)&&!We(t);){if(Pt(t))return t;if(wt(t))return null;t=ge(t)}return null}function $t(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function We(e){return["html","body","#document"].includes(ze(e))}function le(e){return U(e).getComputedStyle(e)}function bt(e){return re(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function ge(e){if(ze(e)==="html")return e;const t=e.assignedSlot||e.parentNode||qt(e)&&e.host||de(e);return qt(t)?t.host:t}function io(e){const t=ge(e);return We(t)?e.ownerDocument?e.ownerDocument.body:e.body:fe(t)&&st(t)?t:io(t)}function nt(e,t,n){var o;t===void 0&&(t=[]),n===void 0&&(n=!0);const r=io(e),l=r===((o=e.ownerDocument)==null?void 0:o.body),s=U(r);if(l){const i=Lt(s);return t.concat(s,s.visualViewport||[],st(r)?r:[],i&&n?nt(i):[])}return t.concat(r,nt(r,[],n))}function Lt(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function co(e){const t=le(e);let n=parseFloat(t.width)||0,o=parseFloat(t.height)||0;const r=fe(e),l=r?e.offsetWidth:n,s=r?e.offsetHeight:o,i=ht(n)!==l||ht(o)!==s;return i&&(n=l,o=s),{width:n,height:o,$:i}}function Bt(e){return re(e)?e:e.contextElement}function Ie(e){const t=Bt(e);if(!fe(t))return ue(1);const n=t.getBoundingClientRect(),{width:o,height:r,$:l}=co(t);let s=(l?ht(n.width):n.width)/o,i=(l?ht(n.height):n.height)/r;return(!s||!Number.isFinite(s))&&(s=1),(!i||!Number.isFinite(i))&&(i=1),{x:s,y:i}}const jo=ue(0);function ao(e){const t=U(e);return!$t()||!t.visualViewport?jo:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function Po(e,t,n){return t===void 0&&(t=!1),!n||t&&n!==U(e)?!1:t}function Oe(e,t,n,o){t===void 0&&(t=!1),n===void 0&&(n=!1);const r=e.getBoundingClientRect(),l=Bt(e);let s=ue(1);t&&(o?re(o)&&(s=Ie(o)):s=Ie(e));const i=Po(l,n,o)?ao(l):ue(0);let c=(r.left+i.x)/s.x,a=(r.top+i.y)/s.y,m=r.width/s.x,d=r.height/s.y;if(l){const g=U(l),f=o&&re(o)?U(o):o;let h=g,w=Lt(h);for(;w&&o&&f!==h;){const E=Ie(w),b=w.getBoundingClientRect(),S=le(w),_=b.left+(w.clientLeft+parseFloat(S.paddingLeft))*E.x,C=b.top+(w.clientTop+parseFloat(S.paddingTop))*E.y;c*=E.x,a*=E.y,m*=E.x,d*=E.y,c+=_,a+=C,h=U(w),w=Lt(h)}}return yt({width:m,height:d,x:c,y:a})}function It(e,t){const n=bt(e).scrollLeft;return t?t.left+n:Oe(de(e)).left+n}function uo(e,t,n){n===void 0&&(n=!1);const o=e.getBoundingClientRect(),r=o.left+t.scrollLeft-(n?0:It(e,o)),l=o.top+t.scrollTop;return{x:r,y:l}}function $o(e){let{elements:t,rect:n,offsetParent:o,strategy:r}=e;const l=r==="fixed",s=de(o),i=t?wt(t.floating):!1;if(o===s||i&&l)return n;let c={scrollLeft:0,scrollTop:0},a=ue(1);const m=ue(0),d=fe(o);if((d||!d&&!l)&&((ze(o)!=="body"||st(s))&&(c=bt(o)),fe(o))){const f=Oe(o);a=Ie(o),m.x=f.x+o.clientLeft,m.y=f.y+o.clientTop}const g=s&&!d&&!l?uo(s,c,!0):ue(0);return{width:n.width*a.x,height:n.height*a.y,x:n.x*a.x-c.scrollLeft*a.x+m.x+g.x,y:n.y*a.y-c.scrollTop*a.y+m.y+g.y}}function Bo(e){return Array.from(e.getClientRects())}function Io(e){const t=de(e),n=bt(e),o=e.ownerDocument.body,r=Se(t.scrollWidth,t.clientWidth,o.scrollWidth,o.clientWidth),l=Se(t.scrollHeight,t.clientHeight,o.scrollHeight,o.clientHeight);let s=-n.scrollLeft+It(e);const i=-n.scrollTop;return le(o).direction==="rtl"&&(s+=Se(t.clientWidth,o.clientWidth)-r),{width:r,height:l,x:s,y:i}}function Mo(e,t){const n=U(e),o=de(e),r=n.visualViewport;let l=o.clientWidth,s=o.clientHeight,i=0,c=0;if(r){l=r.width,s=r.height;const a=$t();(!a||a&&t==="fixed")&&(i=r.offsetLeft,c=r.offsetTop)}return{width:l,height:s,x:i,y:c}}function Fo(e,t){const n=Oe(e,!0,t==="fixed"),o=n.top+e.clientTop,r=n.left+e.clientLeft,l=fe(e)?Ie(e):ue(1),s=e.clientWidth*l.x,i=e.clientHeight*l.y,c=r*l.x,a=o*l.y;return{width:s,height:i,x:c,y:a}}function Ht(e,t,n){let o;if(t==="viewport")o=Mo(e,n);else if(t==="document")o=Io(de(e));else if(re(t))o=Fo(t,n);else{const r=ao(e);o={x:t.x-r.x,y:t.y-r.y,width:t.width,height:t.height}}return yt(o)}function fo(e,t){const n=ge(e);return n===t||!re(n)||We(n)?!1:le(n).position==="fixed"||fo(n,t)}function Wo(e,t){const n=t.get(e);if(n)return n;let o=nt(e,[],!1).filter(i=>re(i)&&ze(i)!=="body"),r=null;const l=le(e).position==="fixed";let s=l?ge(e):e;for(;re(s)&&!We(s);){const i=le(s),c=Pt(s);!c&&i.position==="fixed"&&(r=null),(l?!c&&!r:!c&&i.position==="static"&&!!r&&["absolute","fixed"].includes(r.position)||st(s)&&!c&&fo(e,s))?o=o.filter(m=>m!==s):r=i,s=ge(s)}return t.set(e,o),o}function zo(e){let{element:t,boundary:n,rootBoundary:o,strategy:r}=e;const s=[...n==="clippingAncestors"?wt(t)?[]:Wo(t,this._c):[].concat(n),o],i=s[0],c=s.reduce((a,m)=>{const d=Ht(t,m,r);return a.top=Se(d.top,a.top),a.right=Me(d.right,a.right),a.bottom=Me(d.bottom,a.bottom),a.left=Se(d.left,a.left),a},Ht(t,i,r));return{width:c.right-c.left,height:c.bottom-c.top,x:c.left,y:c.top}}function Vo(e){const{width:t,height:n}=co(e);return{width:t,height:n}}function qo(e,t,n){const o=fe(t),r=de(t),l=n==="fixed",s=Oe(e,!0,l,t);let i={scrollLeft:0,scrollTop:0};const c=ue(0);if(o||!o&&!l)if((ze(t)!=="body"||st(r))&&(i=bt(t)),o){const g=Oe(t,!0,l,t);c.x=g.x+t.clientLeft,c.y=g.y+t.clientTop}else r&&(c.x=It(r));const a=r&&!o&&!l?uo(r,i):ue(0),m=s.left+i.scrollLeft-c.x-a.x,d=s.top+i.scrollTop-c.y-a.y;return{x:m,y:d,width:s.width,height:s.height}}function At(e){return le(e).position==="static"}function Kt(e,t){if(!fe(e)||le(e).position==="fixed")return null;if(t)return t(e);let n=e.offsetParent;return de(e)===n&&(n=n.ownerDocument.body),n}function po(e,t){const n=U(e);if(wt(e))return n;if(!fe(e)){let r=ge(e);for(;r&&!We(r);){if(re(r)&&!At(r))return r;r=ge(r)}return n}let o=Kt(e,t);for(;o&&No(o)&&At(o);)o=Kt(o,t);return o&&We(o)&&At(o)&&!Pt(o)?n:o||Do(e)||n}const Ho=async function(e){const t=this.getOffsetParent||po,n=this.getDimensions,o=await n(e.floating);return{reference:qo(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:o.width,height:o.height}}};function Ko(e){return le(e).direction==="rtl"}const Xo={convertOffsetParentRelativeRectToViewportRelativeRect:$o,getDocumentElement:de,getClippingRect:zo,getOffsetParent:po,getElementRects:Ho,getClientRects:Bo,getDimensions:Vo,getScale:Ie,isElement:re,isRTL:Ko};function mo(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function Yo(e,t){let n=null,o;const r=de(e);function l(){var i;clearTimeout(o),(i=n)==null||i.disconnect(),n=null}function s(i,c){i===void 0&&(i=!1),c===void 0&&(c=1),l();const a=e.getBoundingClientRect(),{left:m,top:d,width:g,height:f}=a;if(i||t(),!g||!f)return;const h=mt(d),w=mt(r.clientWidth-(m+g)),E=mt(r.clientHeight-(d+f)),b=mt(m),_={rootMargin:-h+"px "+-w+"px "+-E+"px "+-b+"px",threshold:Se(0,Me(1,c))||1};let C=!0;function O(q){const L=q[0].intersectionRatio;if(L!==c){if(!C)return s();L?s(!1,L):o=setTimeout(()=>{s(!1,1e-7)},1e3)}L===1&&!mo(a,e.getBoundingClientRect())&&s(),C=!1}try{n=new IntersectionObserver(O,{..._,root:r.ownerDocument})}catch{n=new IntersectionObserver(O,_)}n.observe(e)}return s(!0),l}function Uo(e,t,n,o){o===void 0&&(o={});const{ancestorScroll:r=!0,ancestorResize:l=!0,elementResize:s=typeof ResizeObserver=="function",layoutShift:i=typeof IntersectionObserver=="function",animationFrame:c=!1}=o,a=Bt(e),m=r||l?[...a?nt(a):[],...nt(t)]:[];m.forEach(b=>{r&&b.addEventListener("scroll",n,{passive:!0}),l&&b.addEventListener("resize",n)});const d=a&&i?Yo(a,n):null;let g=-1,f=null;s&&(f=new ResizeObserver(b=>{let[S]=b;S&&S.target===a&&f&&(f.unobserve(t),cancelAnimationFrame(g),g=requestAnimationFrame(()=>{var _;(_=f)==null||_.observe(t)})),n()}),a&&!c&&f.observe(a),f.observe(t));let h,w=c?Oe(e):null;c&&E();function E(){const b=Oe(e);w&&!mo(w,b)&&n(),w=b,h=requestAnimationFrame(E)}return n(),()=>{var b;m.forEach(S=>{r&&S.removeEventListener("scroll",n),l&&S.removeEventListener("resize",n)}),d?.(),(b=f)==null||b.disconnect(),f=null,c&&cancelAnimationFrame(h)}}const Jo=Lo,Qo=ko,Zo=Co,Go=Ro,Xt=(e,t,n)=>{const o=new Map,r={platform:Xo,...n},l={...r.platform,_c:o};return Oo(e,t,{...r,platform:l})};var Ot={exports:{}};/*!
	Copyright (c) 2018 Jed Watson.
	Licensed under the MIT License (MIT), see
	http://jedwatson.github.io/classnames
*/var Yt;function en(){return Yt||(Yt=1,function(e){(function(){var t={}.hasOwnProperty;function n(){for(var o=[],r=0;r<arguments.length;r++){var l=arguments[r];if(l){var s=typeof l;if(s==="string"||s==="number")o.push(l);else if(Array.isArray(l)){if(l.length){var i=n.apply(null,l);i&&o.push(i)}}else if(s==="object"){if(l.toString!==Object.prototype.toString&&!l.toString.toString().includes("[native code]")){o.push(l.toString());continue}for(var c in l)t.call(l,c)&&l[c]&&o.push(c)}}}return o.join(" ")}e.exports?(n.default=n,e.exports=n):window.classNames=n})()}(Ot)),Ot.exports}var tn=en();const kt=go(tn);var Ut={};const on="react-tooltip-core-styles",nn="react-tooltip-base-styles",Jt={core:!1,base:!1};function Qt({css:e,id:t=nn,type:n="base",ref:o}){var r,l;if(!e||typeof document>"u"||Jt[n]||n==="core"&&typeof process<"u"&&(!((r=process==null?void 0:Ut)===null||r===void 0)&&r.REACT_TOOLTIP_DISABLE_CORE_STYLES)||n!=="base"&&typeof process<"u"&&(!((l=process==null?void 0:Ut)===null||l===void 0)&&l.REACT_TOOLTIP_DISABLE_BASE_STYLES))return;n==="core"&&(t=on),o||(o={});const{insertAt:s}=o;if(document.getElementById(t))return;const i=document.head||document.getElementsByTagName("head")[0],c=document.createElement("style");c.id=t,c.type="text/css",s==="top"&&i.firstChild?i.insertBefore(c,i.firstChild):i.appendChild(c),c.styleSheet?c.styleSheet.cssText=e:c.appendChild(document.createTextNode(e)),Jt[n]=!0}const Zt=async({elementReference:e=null,tooltipReference:t=null,tooltipArrowReference:n=null,place:o="top",offset:r=10,strategy:l="absolute",middlewares:s=[Jo(Number(r)),Zo({fallbackAxisSideDirection:"start"}),Qo({padding:5})],border:i})=>{if(!e)return{tooltipStyles:{},tooltipArrowStyles:{},place:o};if(t===null)return{tooltipStyles:{},tooltipArrowStyles:{},place:o};const c=s;return n?(c.push(Go({element:n,padding:5})),Xt(e,t,{placement:o,strategy:l,middleware:c}).then(({x:a,y:m,placement:d,middlewareData:g})=>{var f,h;const w={left:`${a}px`,top:`${m}px`,border:i},{x:E,y:b}=(f=g.arrow)!==null&&f!==void 0?f:{x:0,y:0},S=(h={top:"bottom",right:"left",bottom:"top",left:"right"}[d.split("-")[0]])!==null&&h!==void 0?h:"bottom",_=i&&{borderBottom:i,borderRight:i};let C=0;if(i){const O=`${i}`.match(/(\d+)px/);C=O?.[1]?Number(O[1]):1}return{tooltipStyles:w,tooltipArrowStyles:{left:E!=null?`${E}px`:"",top:b!=null?`${b}px`:"",right:"",bottom:"",..._,[S]:`-${4+C}px`},place:d}})):Xt(e,t,{placement:"bottom",strategy:l,middleware:c}).then(({x:a,y:m,placement:d})=>({tooltipStyles:{left:`${a}px`,top:`${m}px`},tooltipArrowStyles:{},place:d}))},Gt=(e,t)=>!("CSS"in window&&"supports"in window.CSS)||window.CSS.supports(e,t),eo=(e,t,n)=>{let o=null;const r=function(...l){const s=()=>{o=null};!o&&(e.apply(this,l),o=setTimeout(s,t))};return r.cancel=()=>{o&&(clearTimeout(o),o=null)},r},to=e=>e!==null&&!Array.isArray(e)&&typeof e=="object",Nt=(e,t)=>{if(e===t)return!0;if(Array.isArray(e)&&Array.isArray(t))return e.length===t.length&&e.every((r,l)=>Nt(r,t[l]));if(Array.isArray(e)!==Array.isArray(t))return!1;if(!to(e)||!to(t))return e===t;const n=Object.keys(e),o=Object.keys(t);return n.length===o.length&&n.every(r=>Nt(e[r],t[r]))},rn=e=>{if(!(e instanceof HTMLElement||e instanceof SVGElement))return!1;const t=getComputedStyle(e);return["overflow","overflow-x","overflow-y"].some(n=>{const o=t.getPropertyValue(n);return o==="auto"||o==="scroll"})},oo=e=>{if(!e)return null;let t=e.parentElement;for(;t;){if(rn(t))return t;t=t.parentElement}return document.scrollingElement||document.documentElement},ln=typeof window<"u"?p.useLayoutEffect:p.useEffect,te=e=>{e.current&&(clearTimeout(e.current),e.current=null)},sn="DEFAULT_TOOLTIP_ID",cn={anchorRefs:new Set,activeAnchor:{current:null},attach:()=>{},detach:()=>{},setActiveAnchor:()=>{}},an=p.createContext({getTooltipData:()=>cn});function ho(e=sn){return p.useContext(an).getTooltipData(e)}var Be={tooltip:"core-styles-module_tooltip__3vRRp",fixed:"core-styles-module_fixed__pcSol",arrow:"core-styles-module_arrow__cvMwQ",noArrow:"core-styles-module_noArrow__xock6",clickable:"core-styles-module_clickable__ZuTTB",show:"core-styles-module_show__Nt9eE",closing:"core-styles-module_closing__sGnxF"},Rt={tooltip:"styles-module_tooltip__mnnfp",arrow:"styles-module_arrow__K0L3T",dark:"styles-module_dark__xNqje",light:"styles-module_light__Z6W-X",success:"styles-module_success__A2AKt",warning:"styles-module_warning__SCK0X",error:"styles-module_error__JvumD",info:"styles-module_info__BWdHW"};const un=({forwardRef:e,id:t,className:n,classNameArrow:o,variant:r="dark",anchorId:l,anchorSelect:s,place:i="top",offset:c=10,events:a=["hover"],openOnClick:m=!1,positionStrategy:d="absolute",middlewares:g,wrapper:f,delayShow:h=0,delayHide:w=0,float:E=!1,hidden:b=!1,noArrow:S=!1,clickable:_=!1,closeOnEsc:C=!1,closeOnScroll:O=!1,closeOnResize:q=!1,openEvents:L,closeEvents:J,globalCloseEvents:H,imperativeModeOnly:Q,style:se,position:Z,afterShow:ie,afterHide:T,disableTooltip:K,content:F,contentWrapperRef:B,isOpen:D,defaultIsOpen:W=!1,setIsOpen:pe,activeAnchor:P,setActiveAnchor:Re,border:it,opacity:ct,arrowColor:at,role:xt="tooltip"})=>{var Ve;const z=p.useRef(null),Ce=p.useRef(null),oe=p.useRef(null),he=p.useRef(null),qe=p.useRef(null),[ve,Et]=p.useState({tooltipStyles:{},tooltipArrowStyles:{},place:i}),[X,ut]=p.useState(!1),[we,be]=p.useState(!1),[N,He]=p.useState(null),Ke=p.useRef(!1),Xe=p.useRef(null),{anchorRefs:Ye,setActiveAnchor:ft}=ho(t),Te=p.useRef(!1),[ye,Ue]=p.useState([]),xe=p.useRef(!1),Le=m||a.includes("click"),Je=Le||L?.click||L?.dblclick||L?.mousedown,ke=L?{...L}:{mouseover:!0,focus:!0,mouseenter:!1,click:!1,dblclick:!1,mousedown:!1};!L&&Le&&Object.assign(ke,{mouseenter:!1,focus:!1,mouseover:!1,click:!0});const Qe=J?{...J}:{mouseout:!0,blur:!0,mouseleave:!1,click:!1,dblclick:!1,mouseup:!1};!J&&Le&&Object.assign(Qe,{mouseleave:!1,blur:!1,mouseout:!1});const ne=H?{...H}:{escape:C||!1,scroll:O||!1,resize:q||!1,clickOutsideAnchor:Je||!1};Q&&(Object.assign(ke,{mouseenter:!1,focus:!1,click:!1,dblclick:!1,mousedown:!1}),Object.assign(Qe,{mouseleave:!1,blur:!1,click:!1,dblclick:!1,mouseup:!1}),Object.assign(ne,{escape:!1,scroll:!1,resize:!1,clickOutsideAnchor:!1})),ln(()=>(xe.current=!0,()=>{xe.current=!1}),[]);const j=u=>{xe.current&&(u&&be(!0),setTimeout(()=>{xe.current&&(pe?.(u),D===void 0&&ut(u))},10))};p.useEffect(()=>{if(D===void 0)return()=>null;D&&be(!0);const u=setTimeout(()=>{ut(D)},10);return()=>{clearTimeout(u)}},[D]),p.useEffect(()=>{if(X!==Ke.current)if(te(qe),Ke.current=X,X)ie?.();else{const u=(y=>{const x=y.match(/^([\d.]+)(ms|s)$/);if(!x)return 0;const[,$,M]=x;return Number($)*(M==="ms"?1:1e3)})(getComputedStyle(document.body).getPropertyValue("--rt-transition-show-delay"));qe.current=setTimeout(()=>{be(!1),He(null),T?.()},u+25)}},[X]);const dt=u=>{Et(y=>Nt(y,u)?y:u)},Ze=(u=h)=>{te(oe),we?j(!0):oe.current=setTimeout(()=>{j(!0)},u)},Ne=(u=w)=>{te(he),he.current=setTimeout(()=>{Te.current||j(!1)},u)},Ge=u=>{var y;if(!u)return;const x=(y=u.currentTarget)!==null&&y!==void 0?y:u.target;if(!x?.isConnected)return Re(null),void ft({current:null});h?Ze():j(!0),Re(x),ft({current:x}),te(he)},De=()=>{_?Ne(w||100):w?Ne():j(!1),te(oe)},je=({x:u,y})=>{var x;const $={getBoundingClientRect:()=>({x:u,y,width:0,height:0,top:y,left:u,right:u,bottom:y})};Zt({place:(x=N?.place)!==null&&x!==void 0?x:i,offset:c,elementReference:$,tooltipReference:z.current,tooltipArrowReference:Ce.current,strategy:d,middlewares:g,border:it}).then(M=>{dt(M)})},Pe=u=>{if(!u)return;const y=u,x={x:y.clientX,y:y.clientY};je(x),Xe.current=x},et=u=>{var y;if(!X)return;const x=u.target;x.isConnected&&(!((y=z.current)===null||y===void 0)&&y.contains(x)||[document.querySelector(`[id='${l}']`),...ye].some($=>$?.contains(x))||(j(!1),te(oe)))},pt=eo(Ge,50),I=eo(De,50),G=u=>{I.cancel(),pt(u)},v=()=>{pt.cancel(),I()},A=p.useCallback(()=>{var u,y;const x=(u=N?.position)!==null&&u!==void 0?u:Z;x?je(x):E?Xe.current&&je(Xe.current):P?.isConnected&&Zt({place:(y=N?.place)!==null&&y!==void 0?y:i,offset:c,elementReference:P,tooltipReference:z.current,tooltipArrowReference:Ce.current,strategy:d,middlewares:g,border:it}).then($=>{xe.current&&dt($)})},[X,P,F,se,i,N?.place,c,d,Z,N?.position,E]);p.useEffect(()=>{var u,y;const x=new Set(Ye);ye.forEach(R=>{K?.(R)||x.add({current:R})});const $=document.querySelector(`[id='${l}']`);$&&!K?.($)&&x.add({current:$});const M=()=>{j(!1)},ce=oo(P),ae=oo(z.current);ne.scroll&&(window.addEventListener("scroll",M),ce?.addEventListener("scroll",M),ae?.addEventListener("scroll",M));let V=null;ne.resize?window.addEventListener("resize",M):P&&z.current&&(V=Uo(P,z.current,A,{ancestorResize:!0,elementResize:!0,layoutShift:!0}));const ee=R=>{R.key==="Escape"&&j(!1)};ne.escape&&window.addEventListener("keydown",ee),ne.clickOutsideAnchor&&window.addEventListener("click",et);const k=[],tt=R=>{X&&R?.target===P||Ge(R)},vo=R=>{X&&R?.target===P&&De()},Mt=["mouseover","mouseout","mouseenter","mouseleave","focus","blur"],Ft=["click","dblclick","mousedown","mouseup"];Object.entries(ke).forEach(([R,me])=>{me&&(Mt.includes(R)?k.push({event:R,listener:G}):Ft.includes(R)&&k.push({event:R,listener:tt}))}),Object.entries(Qe).forEach(([R,me])=>{me&&(Mt.includes(R)?k.push({event:R,listener:v}):Ft.includes(R)&&k.push({event:R,listener:vo}))}),E&&k.push({event:"pointermove",listener:Pe});const Wt=()=>{Te.current=!0},zt=()=>{Te.current=!1,De()};return _&&!Je&&((u=z.current)===null||u===void 0||u.addEventListener("mouseenter",Wt),(y=z.current)===null||y===void 0||y.addEventListener("mouseleave",zt)),k.forEach(({event:R,listener:me})=>{x.forEach(_t=>{var ot;(ot=_t.current)===null||ot===void 0||ot.addEventListener(R,me)})}),()=>{var R,me;ne.scroll&&(window.removeEventListener("scroll",M),ce?.removeEventListener("scroll",M),ae?.removeEventListener("scroll",M)),ne.resize?window.removeEventListener("resize",M):V?.(),ne.clickOutsideAnchor&&window.removeEventListener("click",et),ne.escape&&window.removeEventListener("keydown",ee),_&&!Je&&((R=z.current)===null||R===void 0||R.removeEventListener("mouseenter",Wt),(me=z.current)===null||me===void 0||me.removeEventListener("mouseleave",zt)),k.forEach(({event:_t,listener:ot})=>{x.forEach(yo=>{var St;(St=yo.current)===null||St===void 0||St.removeEventListener(_t,ot)})})}},[P,A,we,Ye,ye,L,J,H,Le,h,w]),p.useEffect(()=>{var u,y;let x=(y=(u=N?.anchorSelect)!==null&&u!==void 0?u:s)!==null&&y!==void 0?y:"";!x&&t&&(x=`[data-tooltip-id='${t.replace(/'/g,"\\'")}']`);const $=new MutationObserver(M=>{const ce=[],ae=[];M.forEach(V=>{if(V.type==="attributes"&&V.attributeName==="data-tooltip-id"&&(V.target.getAttribute("data-tooltip-id")===t?ce.push(V.target):V.oldValue===t&&ae.push(V.target)),V.type==="childList"){if(P){const ee=[...V.removedNodes].filter(k=>k.nodeType===1);if(x)try{ae.push(...ee.filter(k=>k.matches(x))),ae.push(...ee.flatMap(k=>[...k.querySelectorAll(x)]))}catch{}ee.some(k=>{var tt;return!!(!((tt=k?.contains)===null||tt===void 0)&&tt.call(k,P))&&(be(!1),j(!1),Re(null),te(oe),te(he),!0)})}if(x)try{const ee=[...V.addedNodes].filter(k=>k.nodeType===1);ce.push(...ee.filter(k=>k.matches(x))),ce.push(...ee.flatMap(k=>[...k.querySelectorAll(x)]))}catch{}}}),(ce.length||ae.length)&&Ue(V=>[...V.filter(ee=>!ae.includes(ee)),...ce])});return $.observe(document.body,{childList:!0,subtree:!0,attributes:!0,attributeFilter:["data-tooltip-id"],attributeOldValue:!0}),()=>{$.disconnect()}},[t,s,N?.anchorSelect,P]),p.useEffect(()=>{A()},[A]),p.useEffect(()=>{if(!B?.current)return()=>null;const u=new ResizeObserver(()=>{setTimeout(()=>A())});return u.observe(B.current),()=>{u.disconnect()}},[F,B?.current]),p.useEffect(()=>{var u;const y=document.querySelector(`[id='${l}']`),x=[...ye,y];P&&x.includes(P)||Re((u=ye[0])!==null&&u!==void 0?u:y)},[l,ye,P]),p.useEffect(()=>(W&&j(!0),()=>{te(oe),te(he)}),[]),p.useEffect(()=>{var u;let y=(u=N?.anchorSelect)!==null&&u!==void 0?u:s;if(!y&&t&&(y=`[data-tooltip-id='${t.replace(/'/g,"\\'")}']`),y)try{const x=Array.from(document.querySelectorAll(y));Ue(x)}catch{Ue([])}},[t,s,N?.anchorSelect]),p.useEffect(()=>{oe.current&&(te(oe),Ze(h))},[h]);const Y=(Ve=N?.content)!==null&&Ve!==void 0?Ve:F,Ee=X&&Object.keys(ve.tooltipStyles).length>0;return p.useImperativeHandle(e,()=>({open:u=>{if(u?.anchorSelect)try{document.querySelector(u.anchorSelect)}catch{return void console.warn(`[react-tooltip] "${u.anchorSelect}" is not a valid CSS selector`)}He(u??null),u?.delay?Ze(u.delay):j(!0)},close:u=>{u?.delay?Ne(u.delay):j(!1)},activeAnchor:P,place:ve.place,isOpen:!!(we&&!b&&Y&&Ee)})),we&&!b&&Y?_e.createElement(f,{id:t,role:xt,className:kt("react-tooltip",Be.tooltip,Rt.tooltip,Rt[r],n,`react-tooltip__place-${ve.place}`,Be[Ee?"show":"closing"],Ee?"react-tooltip__show":"react-tooltip__closing",d==="fixed"&&Be.fixed,_&&Be.clickable),onTransitionEnd:u=>{te(qe),X||u.propertyName!=="opacity"||(be(!1),He(null),T?.())},style:{...se,...ve.tooltipStyles,opacity:ct!==void 0&&Ee?ct:void 0},ref:z},Y,_e.createElement(f,{className:kt("react-tooltip-arrow",Be.arrow,Rt.arrow,o,S&&Be.noArrow),style:{...ve.tooltipArrowStyles,background:at?`linear-gradient(to right bottom, transparent 50%, ${at} 50%)`:void 0},ref:Ce})):null},fn=({content:e})=>_e.createElement("span",{dangerouslySetInnerHTML:{__html:e}}),dn=_e.forwardRef(({id:e,anchorId:t,anchorSelect:n,content:o,html:r,render:l,className:s,classNameArrow:i,variant:c="dark",place:a="top",offset:m=10,wrapper:d="div",children:g=null,events:f=["hover"],openOnClick:h=!1,positionStrategy:w="absolute",middlewares:E,delayShow:b=0,delayHide:S=0,float:_=!1,hidden:C=!1,noArrow:O=!1,clickable:q=!1,closeOnEsc:L=!1,closeOnScroll:J=!1,closeOnResize:H=!1,openEvents:Q,closeEvents:se,globalCloseEvents:Z,imperativeModeOnly:ie=!1,style:T,position:K,isOpen:F,defaultIsOpen:B=!1,disableStyleInjection:D=!1,border:W,opacity:pe,arrowColor:P,setIsOpen:Re,afterShow:it,afterHide:ct,disableTooltip:at,role:xt="tooltip"},Ve)=>{const[z,Ce]=p.useState(o),[oe,he]=p.useState(r),[qe,ve]=p.useState(a),[Et,X]=p.useState(c),[ut,we]=p.useState(m),[be,N]=p.useState(b),[He,Ke]=p.useState(S),[Xe,Ye]=p.useState(_),[ft,Te]=p.useState(C),[ye,Ue]=p.useState(d),[xe,Le]=p.useState(f),[Je,ke]=p.useState(w),[Qe,ne]=p.useState(null),[j,dt]=p.useState(null),Ze=p.useRef(D),{anchorRefs:Ne,activeAnchor:Ge}=ho(e),De=I=>I?.getAttributeNames().reduce((G,v)=>{var A;return v.startsWith("data-tooltip-")&&(G[v.replace(/^data-tooltip-/,"")]=(A=I?.getAttribute(v))!==null&&A!==void 0?A:null),G},{}),je=I=>{const G={place:v=>{var A;ve((A=v)!==null&&A!==void 0?A:a)},content:v=>{Ce(v??o)},html:v=>{he(v??r)},variant:v=>{var A;X((A=v)!==null&&A!==void 0?A:c)},offset:v=>{we(v===null?m:Number(v))},wrapper:v=>{var A;Ue((A=v)!==null&&A!==void 0?A:d)},events:v=>{const A=v?.split(" ");Le(A??f)},"position-strategy":v=>{var A;ke((A=v)!==null&&A!==void 0?A:w)},"delay-show":v=>{N(v===null?b:Number(v))},"delay-hide":v=>{Ke(v===null?S:Number(v))},float:v=>{Ye(v===null?_:v==="true")},hidden:v=>{Te(v===null?C:v==="true")},"class-name":v=>{ne(v)}};Object.values(G).forEach(v=>v(null)),Object.entries(I).forEach(([v,A])=>{var Y;(Y=G[v])===null||Y===void 0||Y.call(G,A)})};p.useEffect(()=>{Ce(o)},[o]),p.useEffect(()=>{he(r)},[r]),p.useEffect(()=>{ve(a)},[a]),p.useEffect(()=>{X(c)},[c]),p.useEffect(()=>{we(m)},[m]),p.useEffect(()=>{N(b)},[b]),p.useEffect(()=>{Ke(S)},[S]),p.useEffect(()=>{Ye(_)},[_]),p.useEffect(()=>{Te(C)},[C]),p.useEffect(()=>{ke(w)},[w]),p.useEffect(()=>{Ze.current!==D&&console.warn("[react-tooltip] Do not change `disableStyleInjection` dynamically.")},[D]),p.useEffect(()=>{typeof window<"u"&&window.dispatchEvent(new CustomEvent("react-tooltip-inject-styles",{detail:{disableCore:D==="core",disableBase:D}}))},[]),p.useEffect(()=>{var I;const G=new Set(Ne);let v=n;if(!v&&e&&(v=`[data-tooltip-id='${e.replace(/'/g,"\\'")}']`),v)try{document.querySelectorAll(v).forEach(y=>{G.add({current:y})})}catch{console.warn(`[react-tooltip] "${v}" is not a valid CSS selector`)}const A=document.querySelector(`[id='${t}']`);if(A&&G.add({current:A}),!G.size)return()=>null;const Y=(I=j??A)!==null&&I!==void 0?I:Ge.current,Ee=new MutationObserver(y=>{y.forEach(x=>{var $;if(!Y||x.type!=="attributes"||!(!(($=x.attributeName)===null||$===void 0)&&$.startsWith("data-tooltip-")))return;const M=De(Y);je(M)})}),u={attributes:!0,childList:!1,subtree:!1};if(Y){const y=De(Y);je(y),Ee.observe(Y,u)}return()=>{Ee.disconnect()}},[Ne,Ge,j,t,n]),p.useEffect(()=>{T?.border&&console.warn("[react-tooltip] Do not set `style.border`. Use `border` prop instead."),W&&!Gt("border",`${W}`)&&console.warn(`[react-tooltip] "${W}" is not a valid \`border\`.`),T?.opacity&&console.warn("[react-tooltip] Do not set `style.opacity`. Use `opacity` prop instead."),pe&&!Gt("opacity",`${pe}`)&&console.warn(`[react-tooltip] "${pe}" is not a valid \`opacity\`.`)},[]);let Pe=g;const et=p.useRef(null);if(l){const I=l({content:j?.getAttribute("data-tooltip-content")||z||null,activeAnchor:j});Pe=I?_e.createElement("div",{ref:et,className:"react-tooltip-content-wrapper"},I):null}else z&&(Pe=z);oe&&(Pe=_e.createElement(fn,{content:oe}));const pt={forwardRef:Ve,id:e,anchorId:t,anchorSelect:n,className:kt(s,Qe),classNameArrow:i,content:Pe,contentWrapperRef:et,place:qe,variant:Et,offset:ut,wrapper:ye,events:xe,openOnClick:h,positionStrategy:Je,middlewares:E,delayShow:be,delayHide:He,float:Xe,hidden:ft,noArrow:O,clickable:q,closeOnEsc:L,closeOnScroll:J,closeOnResize:H,openEvents:Q,closeEvents:se,globalCloseEvents:Z,imperativeModeOnly:ie,style:T,position:K,isOpen:F,defaultIsOpen:B,border:W,opacity:pe,arrowColor:P,setIsOpen:Re,afterShow:it,afterHide:ct,disableTooltip:at,activeAnchor:j,setActiveAnchor:I=>dt(I),role:xt};return _e.createElement(un,{...pt})});typeof window<"u"&&window.addEventListener("react-tooltip-inject-styles",e=>{e.detail.disableCore||Qt({css:":root{--rt-color-white:#fff;--rt-color-dark:#222;--rt-color-success:#8dc572;--rt-color-error:#be6464;--rt-color-warning:#f0ad4e;--rt-color-info:#337ab7;--rt-opacity:0.9;--rt-transition-show-delay:0.15s;--rt-transition-closing-delay:0.15s}.core-styles-module_tooltip__3vRRp{position:absolute;top:0;left:0;pointer-events:none;opacity:0;will-change:opacity}.core-styles-module_fixed__pcSol{position:fixed}.core-styles-module_arrow__cvMwQ{position:absolute;background:inherit}.core-styles-module_noArrow__xock6{display:none}.core-styles-module_clickable__ZuTTB{pointer-events:auto}.core-styles-module_show__Nt9eE{opacity:var(--rt-opacity);transition:opacity var(--rt-transition-show-delay)ease-out}.core-styles-module_closing__sGnxF{opacity:0;transition:opacity var(--rt-transition-closing-delay)ease-in}",type:"core"}),e.detail.disableBase||Qt({css:`
.styles-module_tooltip__mnnfp{padding:8px 16px;border-radius:3px;font-size:90%;width:max-content}.styles-module_arrow__K0L3T{width:8px;height:8px}[class*='react-tooltip__place-top']>.styles-module_arrow__K0L3T{transform:rotate(45deg)}[class*='react-tooltip__place-right']>.styles-module_arrow__K0L3T{transform:rotate(135deg)}[class*='react-tooltip__place-bottom']>.styles-module_arrow__K0L3T{transform:rotate(225deg)}[class*='react-tooltip__place-left']>.styles-module_arrow__K0L3T{transform:rotate(315deg)}.styles-module_dark__xNqje{background:var(--rt-color-dark);color:var(--rt-color-white)}.styles-module_light__Z6W-X{background-color:var(--rt-color-white);color:var(--rt-color-dark)}.styles-module_success__A2AKt{background-color:var(--rt-color-success);color:var(--rt-color-white)}.styles-module_warning__SCK0X{background-color:var(--rt-color-warning);color:var(--rt-color-white)}.styles-module_error__JvumD{background-color:var(--rt-color-error);color:var(--rt-color-white)}.styles-module_info__BWdHW{background-color:var(--rt-color-info);color:var(--rt-color-white)}`,type:"base"})});const mn=({id:e,Icon:t,title:n,bgColor:o})=>$e.jsxs($e.Fragment,{children:[$e.jsx("p",{"data-tooltip-id":e,className:"text-xl",children:$e.jsx(t,{})}),$e.jsx(dn,{id:e,style:{backgroundColor:o},children:$e.jsx("span",{className:"text-sm font-medium",children:n})})]});export{mn as T};
