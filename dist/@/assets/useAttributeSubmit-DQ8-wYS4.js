import{u as R,r as u,S as U}from"./index-BnbL29JP.js";import{u as _}from"./index.esm-ClJnGQn6.js";import{A as m}from"./ProductServices-CXwJ-2YB.js";import{a as p,n as S}from"./toast-DZMsp61l.js";import{u as $}from"./DrawerButton-BQHT-xfW.js";import{u as z}from"./useTranslationValue-DCEiB8J2.js";const X=r=>{const i=R(),{isDrawerOpen:T,closeDrawer:l,setIsUpdate:g,lang:x}=u.useContext(U),[o,d]=u.useState([]),[e,C]=u.useState("en"),[c,I]=u.useState({}),[y,V]=u.useState(!1),[O,n]=u.useState(!1),{setServiceId:h}=$(),{handlerTextTranslateHandler:w}=z();let v=[];(async()=>{for(let t=0;t<o.length;t++){const a=await w(o[t],e);v=[...v,{name:{[e]:o[t],...a}}]}})();const{handleSubmit:L,register:j,setValue:s,clearErrors:b,formState:{errors:B}}=_(),k=async({title:t,name:a,option:A})=>{try{if(n(!0),!r&&o.length===0){p("Minimum one value is required for add attribute!");return}const f=await w(t,e,c?.title),P=await w(a,e,c?.name),E={title:{...f,[e]:t},name:{...P,[e]:a},variants:v,option:A,type:"attribute",lang:e};if(r){const D=await m.updateAttributes(r,E);g(!0),n(!1),S(D.message),l(),h()}else{const D=await m.addAttribute(E);g(!0),n(!1),S(D.message),l(),h()}}catch(f){p(f?f.response.data.message:f.message),l(),n(!1),h()}},q=async({name:t})=>{try{if(n(!0),r){const a=await m.updateChildAttributes({ids:i.pathname.split("/")[2],id:r},{name:{[e]:t},status:y?"show":"hide"});g(!0),n(!1),S(a.message),l()}else{const a=await m.addChildAttribute(i.pathname.split("/")[2],{name:{[e]:t},status:y?"show":"hide"});g(!0),n(!1),S(a.message),l()}}catch(a){p(a?a.response.data.message:a.message),l(),n(!1),h()}},F=t=>{C(t),Object.keys(c).length>0&&(s("title",c.title[t||"en"]),s("name",c.name[t||"en"]))},H=t=>{d([...o.filter((a,A)=>A!==t)])},M=t=>{t.preventDefault(),t.target.value!==""&&(d([...o,t.target.value]),t.target.value="")};return u.useEffect(()=>{if(!T){I({}),s("title"),s("name"),s("option"),b("title"),b("name"),b("option"),d([]),C(x),s("language",e);return}i.pathname==="/attributes"&&r?(async()=>{try{const t=await m.getAttributeById(r);t&&(I(t),s("title",t.title[e||"en"]),s("name",t.name[e||"en"]),s("option",t.option))}catch(t){p(t?.response?.data?.message||t?.message)}})():i.pathname===`/attributes/${i.pathname.split("/")[2]}`&&(async()=>{try{const t=await m.getChildAttributeById({id:i.pathname.split("/")[2],ids:r});t&&(s("name",t.name[e||"en"]),V(t.status==="show"))}catch(t){p(t?.response?.data?.message||t?.message)}})()},[b,r,T,s,i,e,x]),{handleSubmit:L,onSubmits:q,onSubmit:k,register:j,errors:B,variants:o,setVariants:d,addVariant:M,removeVariant:H,published:y,setPublished:V,isSubmitting:O,handleSelectLanguage:F}};export{X as u};
