import{j as e,h as l}from"./index-BnbL29JP.js";const u=({setRole:i,register:n,name:r,label:o})=>e.jsx(e.Fragment,{children:e.jsxs(l.Select,{onChange:t=>i(t.target.value),name:r,...n(`${r}`,{required:`${o} is required!`}),children:[e.jsx("option",{value:"",defaultValue:!0,hidden:!0,children:"Staff role"}),e.jsx("option",{value:"Super Admin",children:"Super Admin"}),e.jsx("option",{value:"Admin",children:"Admin"}),e.jsx("option",{value:"Cashier",children:"Cashier"}),e.jsx("option",{value:"CEO",children:"CEO"}),e.jsx("option",{value:"Manager",children:"Manager"}),e.jsx("option",{value:"Accountant",children:"Accountant"}),e.jsx("option",{value:"Driver",children:" Driver "}),e.jsx("option",{value:"Security Guard",children:"Security Guard"}),e.jsx("option",{value:"Deliver Person",children:"Delivery Person"})]})});export{u as S};
