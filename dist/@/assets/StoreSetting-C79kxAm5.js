import{j as e,r,S as V,e as O,k as B}from"./index-BnbL29JP.js";import{u as H,E as d}from"./index.esm-ClJnGQn6.js";import{P as J}from"./PageTitle-FuOKSvYQ.js";import{I as c}from"./InputAreaTwo-0b6oYMpv.js";import{S as _}from"./SwitchToggle-CGiqq8S_.js";import{u as Q}from"./useDisableForDemo-Bu4HEiKz.js";import{a as R,n as A}from"./toast-DZMsp61l.js";import{A as X}from"./AnimatedContent-0V4vlNfe.js";import{S as Y}from"./SettingContainer-CMYqe4I4.js";import"./index.prod-BbOPZ2BB.js";import"./Layout-pFzaaQDc.js";import"./iconBase-CKOh_aia.js";import"./spinner-CkndCogW.js";const t=({label:a})=>e.jsx("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:a}),Z=a=>{const{setIsUpdate:f}=r.useContext(V),[l,i]=r.useState(!0),[C,F]=r.useState(""),[L,S]=r.useState(""),[y,g]=r.useState(!0),[u,n]=r.useState(!0),[w,m]=r.useState(!0),[E,p]=r.useState(!0),[G,b]=r.useState(!1),[D,x]=r.useState(!0),[z,h]=r.useState(!1),[P,k]=r.useState(!1),[j,T]=r.useState(!1),[v,I]=r.useState(!1),{handleDisableForDemo:$}=Q(),{register:K,handleSubmit:W,setValue:o,formState:{errors:M}}=H(),U=async s=>{if(!$())try{I(!0);const N={name:"storeSetting",setting:{cod_status:y,stripe_status:u,razorpay_status:w,stripe_key:s.stripe_key,stripe_secret:s.stripe_secret,razorpay_id:s.razorpay_id,razorpay_secret:s.razorpay_secret,google_login_status:D,github_login_status:z,facebook_login_status:P,google_id:s.google_id,google_secret:s.google_secret,github_id:s.github_id,github_secret:s.github_secret,facebook_id:s.facebook_id,facebook_secret:s.facebook_secret,google_analytic_status:j,google_analytic_key:s.google_analytic_key,fb_pixel_status:E,fb_pixel_key:s.fb_pixel_key,tawk_chat_status:G,tawk_chat_property_id:s.tawk_chat_property_id,tawk_chat_widget_id:s.tawk_chat_widget_id}};if(l){const q=await O.addStoreSetting(N);f(!0),I(!1),window.location.reload(),A(q.message)}else{const q=await O.updateStoreSetting(N);f(!0),I(!1),window.location.reload(),A(q.message)}}catch(N){R(N?.response?.data?.message||N?.message),I(!1)}};return r.useEffect(()=>{(async()=>{try{const s=await O.getStoreSetting();s&&(i(!1),g(s.cod_status),n(s.stripe_status),m(s.razorpay_status),p(s.fb_pixel_status),b(s.tawk_chat_status),x(s.google_login_status),h(s.github_login_status),k(s.facebook_login_status),T(s.google_analytic_status),o("stripe_key",s.stripe_key),o("stripe_secret",s.stripe_secret),o("razorpay_id",s.razorpay_id),o("razorpay_secret",s.razorpay_secret),o("google_id",s.google_id),o("google_secret",s.google_secret),o("github_id",s.github_id),o("github_secret",s.github_secret),o("facebook_id",s.facebook_id),o("facebook_secret",s.facebook_secret),o("google_analytic_key",s.google_analytic_key),o("fb_pixel_key",s.fb_pixel_key),o("tawk_chat_property_id",s.tawk_chat_property_id),o("tawk_chat_widget_id",s.tawk_chat_widget_id))}catch(s){R(s?.response?.data?.message||s.message)}})()},[]),{errors:M,register:K,isSave:l,favicon:L,setFavicon:S,metaImg:C,setMetaImg:F,isSubmitting:v,onSubmit:U,handleSubmit:W,enabledCOD:y,setEnabledCOD:g,enabledStripe:u,setEnabledStripe:n,enabledRazorPay:w,setEnabledRazorPay:m,enabledFbPixel:E,setEnableFbPixel:p,enabledTawkChat:G,setEnabledTawkChat:b,enabledGoogleLogin:D,setEnabledGoogleLogin:x,enabledGithubLogin:z,setEnabledGithubLogin:h,enabledFacebookLogin:P,setEnabledFacebookLogin:k,enabledGoogleAnalytics:j,setEnabledGoogleAnalytics:T}},pe=()=>{const{t:a}=B(),{isSave:f,errors:l,register:i,onSubmit:C,handleSubmit:F,isSubmitting:L,enabledCOD:S,setEnabledCOD:y,enabledStripe:g,setEnabledStripe:u,enabledRazorPay:n,setEnabledRazorPay:w,enabledTawkChat:m,setEnabledTawkChat:E,enabledGoogleLogin:p,setEnabledGoogleLogin:G,enabledGithubLogin:b,setEnabledGithubLogin:D,enabledFacebookLogin:x,setEnabledFacebookLogin:z,enabledGoogleAnalytics:h,setEnabledGoogleAnalytics:P}=Z(),k=(j,T,v)=>{v==="stripe"&&!j?(u(!g),y(!0)):v==="stripe"&&j?u(!g):v==="cod"&&!j?(y(!S),u(!0)):y(!S)};return e.jsxs(e.Fragment,{children:[e.jsx(J,{children:a("StoreSetting")}),e.jsx(X,{children:e.jsx("div",{className:"sm:container w-full md:p-6 p-4 mx-auto bg-white dark:bg-gray-800 dark:text-gray-200 rounded-lg",children:e.jsx("form",{onSubmit:F(C),children:e.jsx(Y,{isSave:f,title:a("StoreDetails"),isSubmitting:L,children:e.jsxs("div",{className:"flex-grow scrollbar-hide w-full max-h-full",children:[e.jsxs("div",{className:"grid md:grid-cols-5 items-center sm:grid-cols-12 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsxs("label",{className:"block md:text-sm md:col-span-1 sm:col-span-2 text-xs font-semibold text-gray-600 dark:text-gray-400 mb-1",children:[a("EnableCOD")," ",e.jsx("br",{}),e.jsx("span",{className:"text-xs font-normal text-gray-600 dark:text-gray-400",children:"(This is enabled by default)"})]}),e.jsx("div",{className:"sm:col-span-4",children:e.jsx(_,{id:"cod",processOption:S,handleProcess:k})})]}),e.jsxs("div",{className:"grid md:grid-cols-5 items-center sm:grid-cols-12 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(t,{label:a("EnableStripe")}),e.jsx("div",{className:"sm:col-span-4",children:e.jsx(_,{id:"stripe",processOption:g,handleProcess:k})})]}),e.jsxs("div",{style:{height:g?"auto":0,transition:"all .6s",visibility:g?"visible":"hidden",opacity:g?"1":"0"},className:`${g?"mb-8":"mb-2"}`,children:[e.jsxs("div",{className:"grid md:grid-cols-5 items-center sm:grid-cols-12 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(t,{label:a("StripeKey")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(c,{required:g,register:i,label:a("StripeKey"),name:"stripe_key",type:"password",placeholder:a("StripeKey")}),e.jsx(d,{errorName:l.stripe_key})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 items-center sm:grid-cols-12 gap-3 md:gap-5 xl:gap-6 lg:gap-6",children:[e.jsx(t,{label:a("StripeSecret")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(c,{required:g,register:i,label:a("StripeSecret"),name:"stripe_secret",type:"password",placeholder:a("StripeSecret")}),e.jsx(d,{errorName:l.stripe_secret})]})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 items-center sm:grid-cols-12 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(t,{label:"Enable RazorPay"}),e.jsx("div",{className:"sm:col-span-4",children:e.jsx(_,{id:"razorpay",processOption:n,handleProcess:w})})]}),e.jsxs("div",{style:{height:n?"auto":0,transition:"all .6s",visibility:n?"visible":"hidden",opacity:n?"1":"0"},className:`${n?"mb-8":"mb-2"}`,children:[e.jsxs("div",{className:"grid md:grid-cols-5 items-center sm:grid-cols-12 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(t,{label:"RazorPay ID"}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(c,{required:n,register:i,label:"RazorPay ID",name:"razorpay_id",type:"password",placeholder:"RazorPay ID"}),e.jsx(d,{errorName:l.razorpay_id})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 items-center sm:grid-cols-12 gap-3 md:gap-5 xl:gap-6 lg:gap-6",children:[e.jsx(t,{label:"RazorPay Secret"}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(c,{required:n,register:i,label:"RazorPay Secret",name:"razorpay_secret",type:"password",placeholder:"RazorPay Secret"}),e.jsx(d,{errorName:l.razorpay_secret})]})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 items-center sm:grid-cols-12 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(t,{label:a("EnableGoogleLogin")}),e.jsx("div",{className:"sm:col-span-4",children:e.jsx(_,{id:"google_login",processOption:p,handleProcess:G})})]}),e.jsxs("div",{style:{height:p?"auto":0,transition:"all .6s",visibility:p?"visible":"hidden",opacity:p?"1":"0"},className:`${p?"mb-8":"mb-2"}`,children:[e.jsxs("div",{className:"grid md:grid-cols-5 items-center sm:grid-cols-12 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(t,{label:a("GoogleClientId")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(c,{required:p,register:i,label:a("GoogleClientId"),name:"google_id",type:"password",placeholder:a("GoogleClientId")}),e.jsx(d,{errorName:l.google_id})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 items-center sm:grid-cols-12 gap-3 md:gap-5 xl:gap-6 lg:gap-6",children:[e.jsx(t,{label:a("GoogleSecret")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(c,{required:p,register:i,label:a("GoogleSecret"),name:"google_secret",type:"password",placeholder:a("GoogleSecret")}),e.jsx(d,{errorName:l.google_secret})]})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 items-center sm:grid-cols-12 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(t,{label:"Enable Github Login"}),e.jsx("div",{className:"sm:col-span-4",children:e.jsx(_,{id:"github_login",processOption:b,handleProcess:D})})]}),e.jsxs("div",{style:{height:b?"auto":0,transition:"all .6s",visibility:b?"visible":"hidden",opacity:b?"1":"0"},className:`${b?"mb-8":"mb-2"}`,children:[e.jsxs("div",{className:"grid md:grid-cols-5 items-center sm:grid-cols-12 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(t,{label:"Github ID"}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(c,{required:b,register:i,label:"Github ID",name:"github_id",type:"password",placeholder:"Github ID"}),e.jsx(d,{errorName:l.github_id})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 items-center sm:grid-cols-12 gap-3 md:gap-5 xl:gap-6 lg:gap-6",children:[e.jsx(t,{label:"Github Secret"}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(c,{required:b,register:i,label:"Github Secret",name:"github_secret",type:"password",placeholder:"Github Secret"}),e.jsx(d,{errorName:l.github_secret})]})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 items-center sm:grid-cols-12 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(t,{label:"Enable Facebook Login"}),e.jsx("div",{className:"sm:col-span-4",children:e.jsx(_,{id:"facebook_login",processOption:x,handleProcess:z})})]}),e.jsxs("div",{style:{height:x?"auto":0,transition:"all .6s",visibility:x?"visible":"hidden",opacity:x?"1":"0"},className:`${x?"mb-8":"mb-2"}`,children:[e.jsxs("div",{className:"grid md:grid-cols-5 items-center sm:grid-cols-12 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(t,{label:"Facebook ID"}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(c,{required:x,register:i,label:"Facebook ID",name:"facebook_id",type:"password",placeholder:"Facebook ID"}),e.jsx(d,{errorName:l.facebook_id})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 items-center sm:grid-cols-12 gap-3 md:gap-5 xl:gap-6 lg:gap-6",children:[e.jsx(t,{label:"Facebook Secret"}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(c,{required:x,register:i,label:"Facebook Secret",name:"facebook_secret",type:"password",placeholder:"Facebook Secret"}),e.jsx(d,{errorName:l.facebook_secret})]})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 items-center sm:grid-cols-12 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(t,{label:a("EnableGoggleAnalytics")}),e.jsx("div",{className:"sm:col-span-4",children:e.jsx(_,{id:"google_analytics",processOption:h,handleProcess:P})})]}),e.jsxs("div",{style:{height:h?"auto":0,transition:"all .6s",visibility:h?"visible":"hidden",opacity:h?"1":"0"},className:`${h?"grid md:grid-cols-5 items-center sm:grid-cols-12 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6":"mb-2"}`,children:[e.jsx(t,{label:a("GoogleAnalyticKey")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(c,{required:h,register:i,label:a("GoogleAnalyticKey"),name:"google_analytic_key",type:"password",placeholder:a("GoogleAnalyticKey")}),e.jsx(d,{errorName:l.google_analytic_key})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 items-center sm:grid-cols-12 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(t,{label:a("EnableTawkChat")}),e.jsx("div",{className:"sm:col-span-4",children:e.jsx(_,{id:"tawk_chat",processOption:m,handleProcess:E})})]}),e.jsxs("div",{style:{height:m?"auto":0,transition:"all .6s",visibility:m?"visible":"hidden",opacity:m?"1":"0"},className:`${m?"mb-8":"mb-2"}`,children:[e.jsxs("div",{className:"grid md:grid-cols-5 items-center sm:grid-cols-12 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(t,{label:a("TawkChatPropertyID")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(c,{required:m,register:i,label:a("TawkChatPropertyID"),name:"tawk_chat_property_id",type:"password",placeholder:a("TawkChatPropertyID")}),e.jsx(d,{errorName:l.tawk_chat_property_id})]})]}),e.jsxs("div",{className:"grid md:grid-cols-5 items-center sm:grid-cols-12 gap-3 md:gap-5 xl:gap-6 lg:gap-6",children:[e.jsx(t,{label:a("TawkChatWidgetID")}),e.jsxs("div",{className:"sm:col-span-4",children:[e.jsx(c,{required:m,register:i,label:a("TawkChatWidgetID"),name:"tawk_chat_widget_id",type:"password",placeholder:a("TawkChatWidgetID")}),e.jsx(d,{errorName:l.tawk_chat_widget_id})]})]})]})]})})})})})]})};export{pe as default};
