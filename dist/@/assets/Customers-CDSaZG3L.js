import{r as p,S as D,j as e,h as s,l as k,t as A,k as E}from"./index-BnbL29JP.js";import{U as P}from"./UploadMany-DQiD3frs.js";import{S as F,d as I,i as U}from"./Layout-pFzaaQDc.js";import{T as w,D as M,u as B,M as R}from"./DrawerButton-BQHT-xfW.js";import{D as L,E as Y}from"./EditDeleteButton-BfkOlssy.js";import{T as _}from"./Tooltip-CCK2Gwc2.js";import{u as q,E as h}from"./index.esm-ClJnGQn6.js";import{I as g}from"./InputArea-B_vEs1uv.js";import{L as u}from"./LabelArea-DQFDcuEN.js";import{b as N}from"./ProductServices-CXwJ-2YB.js";import{a as T,n as O}from"./toast-DZMsp61l.js";import{T as V}from"./TableLoading-BGBA3G2p.js";import{N as H}from"./NotFound-H45Wi1lW.js";import{P as J}from"./PageTitle-FuOKSvYQ.js";import{u as Z}from"./useAsync-DWXVKl2F.js";import{u as $}from"./useFilter-7smJgyBE.js";import{A as z}from"./AnimatedContent-0V4vlNfe.js";import"./exportFromJSON-fDIoOtpr.js";import"./iconBase-CKOh_aia.js";import"./spinner-CkndCogW.js";import"./SelectLanguageTwo-KDdaD-gj.js";import"./AdminServices-DjQuFfvs.js";import"./CouponServices-BN-cEYCp.js";import"./CurrencyServices-3wuDp8cZ.js";import"./useDisableForDemo-Bu4HEiKz.js";import"./index-C148XJoK.js";const G=l=>{const[i,o]=p.useState(""),[m,t]=p.useState(!1),{closeDrawer:a,setIsUpdate:j}=p.useContext(D),{register:b,handleSubmit:f,setValue:d,formState:{errors:C}}=q(),x=async r=>{try{t(!0);const c={name:r.name,email:r.email,phone:r.phone,address:r.address};if(l){const y=await N.updateCustomer(l,c);j(!0),O(y.message),a()}t(!1)}catch(c){T(c?.response?.data?.message||c?.message),a()}};return p.useEffect(()=>{l&&(async()=>{try{const r=await N.getCustomerById(l);r&&(d("name",r.name),d("phone",r.phone),d("email",r.email),d("address",r.address))}catch(r){T(r?.response?.data?.message||r?.message)}})()},[l,d]),{register:b,handleSubmit:f,onSubmit:x,errors:C,setImageUrl:o,imageUrl:i,isSubmitting:m}},K=({id:l})=>{const{register:i,handleSubmit:o,onSubmit:m,errors:t,isSubmitting:a}=G(l);return e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"w-full relative p-6 border-b border-gray-100 bg-gray-50 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-300",children:l?e.jsx(w,{title:"Update Customer",description:"Update your Customer necessary information from here"}):e.jsx(w,{title:"Add Customer",description:"Add your Customer necessary information from here"})}),e.jsx(F,{className:"w-full md:w-7/12 lg:w-8/12 xl:w-8/12 relative dark:bg-gray-700 dark:text-gray-200",children:e.jsxs("form",{onSubmit:o(m),children:[e.jsxs("div",{className:"px-6 pt-8 flex-grow scrollbar-hide w-full max-h-full pb-40",children:[e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(u,{label:"Name"}),e.jsxs("div",{className:"col-span-8 sm:col-span-4",children:[e.jsx(g,{required:!0,register:i,label:"Name",name:"name",type:"text",placeholder:"Name"}),e.jsx(h,{errorName:t.name})]})]}),e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(u,{label:"Email"}),e.jsxs("div",{className:"col-span-8 sm:col-span-4",children:[e.jsx(g,{required:!0,register:i,label:"Email",name:"email",type:"email",placeholder:"Email"}),e.jsx(h,{errorName:t.email})]})]}),e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(u,{label:"Phone"}),e.jsxs("div",{className:"col-span-8 sm:col-span-4",children:[e.jsx(g,{register:i,label:"Phone",name:"phone",type:"text",placeholder:"Phone"}),e.jsx(h,{errorName:t.phone})]})]}),e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(u,{label:"Address"}),e.jsxs("div",{className:"col-span-8 sm:col-span-4",children:[e.jsx(g,{register:i,label:"Address",name:"address",type:"text",placeholder:"Address"}),e.jsx(h,{errorName:t.address})]})]})]}),e.jsx(M,{id:l,title:"Customer",isSubmitting:a})]})})]})},Q=({customers:l})=>{const{title:i,serviceId:o,handleModalOpen:m,handleUpdate:t}=B();return e.jsxs(e.Fragment,{children:[e.jsx(L,{id:o,title:i}),e.jsx(R,{children:e.jsx(K,{id:o})}),e.jsx(s.TableBody,{children:l?.map(a=>e.jsxs(s.TableRow,{children:[e.jsx(s.TableCell,{children:e.jsxs("span",{className:"font-semibold uppercase text-xs",children:[" ",a?._id?.substring(20,24)]})}),e.jsx(s.TableCell,{children:e.jsx("span",{className:"text-sm",children:I(a.createdAt).format("MMM D, YYYY")})}),e.jsx(s.TableCell,{children:e.jsx("span",{className:"text-sm",children:a.name})}),e.jsxs(s.TableCell,{children:[e.jsx("span",{className:"text-sm",children:a.email})," "]}),e.jsx(s.TableCell,{children:e.jsx("span",{className:"text-sm font-medium",children:a.phone})}),e.jsx(s.TableCell,{children:e.jsxs("div",{className:"flex justify-end text-right",children:[e.jsxs("div",{className:"p-2 cursor-pointer text-gray-400 hover:text-emerald-600",children:[" ",e.jsx(k,{to:`/customer-order/${a._id}`,children:e.jsx(_,{id:"view",Icon:U,title:A("ViewOrder"),bgColor:"#34D399"})})]}),e.jsx(Y,{title:a.name,id:a._id,handleUpdate:t,handleModalOpen:m})]})})]},a._id))})]})},Te=()=>{const{data:l,loading:i,error:o}=Z(N.getAllCustomers),{userRef:m,dataTable:t,serviceData:a,filename:j,isDisabled:b,setSearchUser:f,totalResults:d,resultsPerPage:C,handleSubmitUser:x,handleSelectFile:r,handleChangePage:c,handleUploadMultiple:y,handleRemoveSelectFile:v}=$(l),{t:n}=E(),S=()=>{f(""),m.current.value=""};return e.jsxs(e.Fragment,{children:[e.jsx(J,{children:n("CustomersPage")}),e.jsxs(z,{children:[e.jsx(s.Card,{className:"min-w-0 shadow-xs overflow-hidden bg-white dark:bg-gray-800 mb-5",children:e.jsx(s.CardBody,{children:e.jsx("form",{onSubmit:x,className:"py-3 grid gap-4 lg:gap-6 xl:gap-6 md:flex xl:flex",children:e.jsx("div",{className:"items-center",children:e.jsx(P,{title:"Customers",exportData:l,filename:j,isDisabled:b,handleSelectFile:r,handleUploadMultiple:y,handleRemoveSelectFile:v})})})})}),e.jsx(s.Card,{className:"min-w-0 shadow-xs overflow-hidden bg-white dark:bg-gray-800 mb-5",children:e.jsx(s.CardBody,{children:e.jsxs("form",{onSubmit:x,className:"py-3 grid gap-4 lg:gap-6 xl:gap-6 md:flex xl:flex",children:[e.jsxs("div",{className:"flex-grow-0 md:flex-grow lg:flex-grow xl:flex-grow",children:[e.jsx(s.Input,{ref:m,type:"search",name:"search",placeholder:n("CustomersPageSearchPlaceholder")}),e.jsx("button",{type:"submit",className:"absolute right-0 top-0 mt-5 mr-1"})]}),e.jsxs("div",{className:"flex items-center gap-2 flex-grow-0 md:flex-grow lg:flex-grow xl:flex-grow",children:[e.jsx("div",{className:"w-full mx-1",children:e.jsx(s.Button,{type:"submit",className:"h-12 w-full bg-emerald-700",children:"Filter"})}),e.jsx("div",{className:"w-full mx-1",children:e.jsx(s.Button,{layout:"outline",onClick:S,type:"reset",className:"px-4 md:py-1 py-2 h-12 text-sm dark:bg-gray-700",children:e.jsx("span",{className:"text-black dark:text-gray-200",children:"Reset"})})})]})]})})})]}),i?e.jsx(V,{row:12,col:6,width:190,height:20}):o?e.jsx("span",{className:"text-center mx-auto text-red-500",children:o}):a?.length!==0?e.jsxs(s.TableContainer,{className:"mb-8",children:[e.jsxs(s.Table,{children:[e.jsx(s.TableHeader,{children:e.jsxs("tr",{children:[e.jsx(s.TableCell,{children:n("CustomersId")}),e.jsx(s.TableCell,{children:n("CustomersJoiningDate")}),e.jsx(s.TableCell,{children:n("CustomersName")}),e.jsx(s.TableCell,{children:n("CustomersEmail")}),e.jsx(s.TableCell,{children:n("CustomersPhone")}),e.jsx(s.TableCell,{className:"text-right",children:n("CustomersActions")})]})}),e.jsx(Q,{customers:t})]}),e.jsx(s.TableFooter,{children:e.jsx(s.Pagination,{totalResults:d,resultsPerPage:C,onChange:c,label:"Table navigation"})})]}):e.jsx(H,{title:"Sorry, There are no customers right now."})]})};export{Te as default};
