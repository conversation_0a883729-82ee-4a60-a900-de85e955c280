import{r as N,S as B,k as _,j as e,h as t}from"./index-BnbL29JP.js";import{u as V,o as H}from"./Layout-pFzaaQDc.js";import{e as w}from"./exportFromJSON-fDIoOtpr.js";import{a as J}from"./toast-DZMsp61l.js";import{u as U}from"./useAsync-DWXVKl2F.js";import{u as $}from"./useFilter-7smJgyBE.js";import{O}from"./OrderServices-CpvSXYO1.js";import{N as q}from"./NotFound-H45Wi1lW.js";import{P as z}from"./PageTitle-FuOKSvYQ.js";import{O as G}from"./OrderTable-CpEV7rsM.js";import{T as K}from"./TableLoading-BGBA3G2p.js";import{s as Q}from"./spinner-CkndCogW.js";import{A as W}from"./AnimatedContent-0V4vlNfe.js";import"./iconBase-CKOh_aia.js";import"./ProductServices-CXwJ-2YB.js";import"./index-C148XJoK.js";import"./useDisableForDemo-Bu4HEiKz.js";import"./CouponServices-BN-cEYCp.js";import"./CurrencyServices-3wuDp8cZ.js";import"./Status-B_9gO_Q4.js";import"./Tooltip-CCK2Gwc2.js";import"./index-BfvxtTdM.js";import"./SelectStatus-DmARa0Zb.js";const fe=()=>{const{time:c,setTime:m,status:h,endDate:x,setStatus:u,setEndDate:g,startDate:p,currentPage:D,searchText:j,searchRef:b,method:v,setMethod:f,setStartDate:y,setSearchText:S,handleChangePage:P,handleSubmitForAll:A,resultsPerPage:C}=N.useContext(B),{t:s}=_(),[n,d]=N.useState(!1),{data:l,loading:F,error:T}=U(()=>O.getAllOrders({day:c,method:v,status:h,page:D,endDate:x,startDate:p,limit:C,customerName:j})),{currency:k,getNumber:E,getNumberTwo:i}=V(),{dataTable:L,serviceData:I}=$(l?.orders),M=async()=>{try{d(!0);const o=(await O.getAllOrders({page:1,day:c,method:v,status:h,endDate:x,download:!0,startDate:p,limit:l?.totalDoc,customerName:j}))?.orders?.map(r=>({_id:r._id,invoice:r.invoice,subTotal:i(r.subTotal),shippingCost:i(r.shippingCost),discount:i(r?.discount),total:i(r.total),paymentMethod:r.paymentMethod,status:r.status,user_info:r?.user_info?.name,createdAt:r.createdAt,updatedAt:r.updatedAt}));w({data:o,fileName:"orders",exportType:w.types.csv}),d(!1)}catch(a){d(!1),J(a?.response?.data?.message||a?.message)}},R=()=>{m(""),f(""),u(""),g(""),y(""),S(""),b.current.value=""};return e.jsxs(e.Fragment,{children:[e.jsx(z,{children:s("Orders")}),e.jsx(W,{children:e.jsx(t.Card,{className:"min-w-0 shadow-xs overflow-hidden bg-white dark:bg-gray-800 mb-5",children:e.jsx(t.CardBody,{children:e.jsxs("form",{onSubmit:A,children:[e.jsxs("div",{className:"grid gap-4 lg:gap-4 xl:gap-6 md:gap-2 md:grid-cols-5 py-2",children:[e.jsx("div",{children:e.jsx(t.Input,{ref:b,type:"search",name:"search",placeholder:"Search by Customer Name"})}),e.jsx("div",{children:e.jsxs(t.Select,{onChange:a=>u(a.target.value),children:[e.jsx("option",{value:"Status",defaultValue:!0,hidden:!0,children:s("Status")}),e.jsx("option",{value:"Delivered",children:s("PageOrderDelivered")}),e.jsx("option",{value:"Pending",children:s("PageOrderPending")}),e.jsx("option",{value:"Processing",children:s("PageOrderProcessing")}),e.jsx("option",{value:"Cancel",children:s("OrderCancel")})]})}),e.jsx("div",{children:e.jsxs(t.Select,{onChange:a=>m(a.target.value),children:[e.jsx("option",{value:"Order limits",defaultValue:!0,hidden:!0,children:s("Orderlimits")}),e.jsx("option",{value:"5",children:s("DaysOrders5")}),e.jsx("option",{value:"7",children:s("DaysOrders7")}),e.jsx("option",{value:"15",children:s("DaysOrders15")}),e.jsx("option",{value:"30",children:s("DaysOrders30")})]})}),e.jsx("div",{children:e.jsxs(t.Select,{onChange:a=>f(a.target.value),children:[e.jsx("option",{value:"Method",defaultValue:!0,hidden:!0,children:s("Method")}),e.jsx("option",{value:"Cash",children:s("Cash")}),e.jsx("option",{value:"Card",children:s("Card")}),e.jsx("option",{value:"Credit",children:s("Credit")})]})}),e.jsx("div",{children:n?e.jsxs(t.Button,{disabled:!0,type:"button",className:"h-12 w-full",children:[e.jsx("img",{src:Q,alt:"Loading",width:20,height:10})," ",e.jsx("span",{className:"font-serif ml-2 font-light",children:"Processing"})]}):e.jsxs("button",{onClick:M,disabled:l?.orders?.length<=0||n,type:"button",className:`${(l?.orders?.length<=0||n)&&"opacity-50 cursor-not-allowed bg-emerald-600"} flex items-center justify-center text-sm leading-5 h-12 w-full text-center transition-colors duration-150 font-medium px-6 py-2 rounded-md text-white bg-emerald-500 border border-transparent active:bg-emerald-600 hover:bg-emerald-600 `,children:["Download All Orders",e.jsx("span",{className:"ml-2 text-base",children:e.jsx(H,{})})]})})]}),e.jsxs("div",{className:"grid gap-4 lg:gap-6 xl:gap-6 lg:grid-cols-3 xl:grid-cols-3 md:grid-cols-3 sm:grid-cols-1 py-2",children:[e.jsxs("div",{children:[e.jsx(t.Label,{children:"Start Date"}),e.jsx(t.Input,{type:"date",name:"startDate",onChange:a=>y(a.target.value)})]}),e.jsxs("div",{children:[e.jsx(t.Label,{children:"End Date"}),e.jsx(t.Input,{type:"date",name:"startDate",onChange:a=>g(a.target.value)})]}),e.jsxs("div",{className:"mt-2 md:mt-0 flex items-center xl:gap-x-4 gap-x-1 flex-grow-0 md:flex-grow lg:flex-grow xl:flex-grow",children:[e.jsxs("div",{className:"w-full mx-1",children:[e.jsx(t.Label,{style:{visibility:"hidden"},children:"Filter"}),e.jsx(t.Button,{type:"submit",className:"h-12 w-full bg-emerald-700",children:"Filter"})]}),e.jsxs("div",{className:"w-full",children:[e.jsx(t.Label,{style:{visibility:"hidden"},children:"Reset"}),e.jsx(t.Button,{layout:"outline",onClick:R,type:"reset",className:"px-4 md:py-1 py-3 text-sm dark:bg-gray-700",children:e.jsx("span",{className:"text-black dark:text-gray-200",children:"Reset"})})]})]})]})]})})})}),l?.methodTotals?.length>0&&e.jsx(t.Card,{className:"min-w-0 shadow-xs overflow-hidden bg-white dark:bg-gray-800 rounded-t-lg rounded-0 mb-4",children:e.jsx(t.CardBody,{children:e.jsx("div",{className:"flex gap-1",children:l?.methodTotals?.map((a,o)=>e.jsx("div",{className:"dark:text-gray-300",children:a?.method&&e.jsxs(e.Fragment,{children:[e.jsxs("span",{className:"font-medium",children:[" ",a.method]})," :"," ",e.jsxs("span",{className:"font-semibold mr-2",children:[k,E(a.total)]})]})},o+1))})})}),F?e.jsx(K,{row:12,col:7,width:160,height:20}):T?e.jsx("span",{className:"text-center mx-auto text-red-500",children:T}):I?.length!==0?e.jsxs(t.TableContainer,{className:"mb-8 dark:bg-gray-900",children:[e.jsxs(t.Table,{children:[e.jsx(t.TableHeader,{children:e.jsxs("tr",{children:[e.jsx(t.TableCell,{children:s("InvoiceNo")}),e.jsx(t.TableCell,{children:s("TimeTbl")}),e.jsx(t.TableCell,{children:s("CustomerName")}),e.jsx(t.TableCell,{children:s("MethodTbl")}),e.jsx(t.TableCell,{children:s("AmountTbl")}),e.jsx(t.TableCell,{children:s("OderStatusTbl")}),e.jsx(t.TableCell,{children:s("ActionTbl")}),e.jsx(t.TableCell,{className:"text-right",children:s("InvoiceTbl")})]})}),e.jsx(G,{orders:L})]}),e.jsx(t.TableFooter,{children:e.jsx(t.Pagination,{totalResults:l?.totalDoc,resultsPerPage:C,onChange:P,label:"Table navigation"})})]}):e.jsx(q,{title:"Sorry, There are no orders right now."})]})};export{fe as default};
