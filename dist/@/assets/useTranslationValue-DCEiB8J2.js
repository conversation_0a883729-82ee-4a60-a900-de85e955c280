import{u as h}from"./Layout-pFzaaQDc.js";import{G as m}from"./index-BnbL29JP.js";const w={translateText:async(o,l,c)=>(await m.get(`https://api.mymemory.translated.net/get?q=${o}&langpair=${l}|${c}`)).data},$=()=>{const{globalSetting:o,languages:l}=h(),c=t=>{if(!t)return t;const e={...t};return Object.keys(e).forEach(a=>{const r=e[a];(r?.toLowerCase().includes("authentication failure")||r?.toLowerCase().includes("error")||!r)&&(console.log(`Removing invalid translation for language: ${a}`),delete e[a])}),e},u=(t,e)=>{const a=l?.map(r=>r?.iso_code);return t?!e||!a?.length?!1:a?.some(r=>t[r]?e[r]?t[r]!==e[r]:!1:!0):!0},y=async(t,e,a)=>{const r=o?.translation_key||"a91efbf362a6e399453d";try{const n=(await w.translateText(t,e,a,r))?.responseData?.translatedText;return n?.toLowerCase().includes("authentication failure")||n?.toLowerCase().includes("error")||!n?(console.error(`Translation API failed for ${e} to ${a}:`,n),null):n}catch(d){return console.error("error on translation",d),null}};return{hasKeyChanged:u,handleRemoveEmptyKey:t=>{for(const e in t)t[e].trim()===""&&delete t[e];return t},handlerTextTranslateHandler:async(t,e,a)=>{const r=c(a);if(!o?.allow_auto_trans||!(u(r,{[e]:t})||!1))return!1;const n=l?.filter(s=>s?.iso_code!==e),p=n.map(s=>t?y(t?.toLowerCase(),e,s?.iso_code):""),T=await Promise.all(p);let f=n.map((s,i)=>{const g=T[i];return g?{lang:s?.iso_code,text:g}:null}).filter(Boolean).reduce((s,i)=>Object.assign(s,{[i.lang]:i.text}),{});return r&&r[e]&&(f[e]=r[e]),f}}};export{$ as u};
