import{a as B,g as I,j as w}from"./index-BnbL29JP.js";var o={},d={},x;function N(){if(x)return d;x=1,Object.defineProperty(d,"__esModule",{value:!0}),d.cssValue=d.parseLengthAndUnit=void 0;var l={cm:!0,mm:!0,in:!0,px:!0,pt:!0,pc:!0,em:!0,ex:!0,ch:!0,rem:!0,vw:!0,vh:!0,vmin:!0,vmax:!0,"%":!0};function f(c){if(typeof c=="number")return{value:c,unit:"px"};var i,u=(c.match(/^[0-9.]*/)||"").toString();u.includes(".")?i=parseFloat(u):i=parseInt(u,10);var s=(c.match(/[^0-9]*$/)||"").toString();return l[s]?{value:i,unit:s}:(console.warn("React Spinners: ".concat(c," is not a valid css value. Defaulting to ").concat(i,"px.")),{value:i,unit:"px"})}d.parseLengthAndUnit=f;function v(c){var i=f(c);return"".concat(i.value).concat(i.unit)}return d.cssValue=v,d}var m={},S;function $(){if(S)return m;S=1,Object.defineProperty(m,"__esModule",{value:!0}),m.createAnimation=void 0;var l=function(f,v,c){var i="react-spinners-".concat(f,"-").concat(c);if(typeof window>"u"||!window.document)return i;var u=document.createElement("style");document.head.appendChild(u);var s=u.sheet,g=`
    @keyframes `.concat(i,` {
      `).concat(v,`
    }
  `);return s&&s.insertRule(g,0),i};return m.createAnimation=l,m}var E;function W(){if(E)return o;E=1;var l=o&&o.__assign||function(){return l=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++){t=arguments[n];for(var a in t)Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a])}return e},l.apply(this,arguments)},f=o&&o.__createBinding||(Object.create?function(e,t,n,r){r===void 0&&(r=n);var a=Object.getOwnPropertyDescriptor(t,n);(!a||("get"in a?!t.__esModule:a.writable||a.configurable))&&(a={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,a)}:function(e,t,n,r){r===void 0&&(r=n),e[r]=t[n]}),v=o&&o.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),c=o&&o.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(e!=null)for(var n in e)n!=="default"&&Object.prototype.hasOwnProperty.call(e,n)&&f(t,e,n);return v(t,e),t},i=o&&o.__rest||function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)t.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(n[r[a]]=e[r[a]]);return n};Object.defineProperty(o,"__esModule",{value:!0});var u=c(B()),s=N(),g=$(),L=(0,g.createAnimation)("ScaleLoader","0% {transform: scaley(1.0)} 50% {transform: scaley(0.4)} 100% {transform: scaley(1.0)}","scale");function M(e){var t=e.loading,n=t===void 0?!0:t,r=e.color,a=r===void 0?"#000000":r,h=e.speedMultiplier,P=h===void 0?1:h,y=e.cssOverride,A=y===void 0?{}:y,_=e.height,R=_===void 0?35:_,b=e.width,q=b===void 0?4:b,O=e.radius,C=O===void 0?2:O,j=e.margin,V=j===void 0?2:j,D=i(e,["loading","color","speedMultiplier","cssOverride","height","width","radius","margin"]),U=l({display:"inherit"},A),p=function(F){return{backgroundColor:a,width:(0,s.cssValue)(q),height:(0,s.cssValue)(R),margin:(0,s.cssValue)(V),borderRadius:(0,s.cssValue)(C),display:"inline-block",animation:"".concat(L," ").concat(1/P,"s ").concat(F*.1,"s infinite cubic-bezier(0.2, 0.68, 0.18, 1.08)"),animationFillMode:"both"}};return n?u.createElement("span",l({style:U},D),u.createElement("span",{style:p(1)}),u.createElement("span",{style:p(2)}),u.createElement("span",{style:p(3)}),u.createElement("span",{style:p(4)}),u.createElement("span",{style:p(5)})):null}return o.default=M,o}var G=W();const H=I(G),K=({loading:l})=>w.jsx("div",{className:"text-lg text-center py-6",children:w.jsx(H,{color:"#34D399",loading:l,height:25,width:3,radius:3,margin:4})});export{K as L};
