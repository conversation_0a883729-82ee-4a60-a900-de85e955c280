import{j as e,u as Q,r as h,S as J,L as Z,h as k}from"./index-BnbL29JP.js";import{a as oe}from"./index.prod-BbOPZ2BB.js";import{C as ee,P as se,A as H}from"./ProductServices-CXwJ-2YB.js";import{C as G}from"./CouponServices-BN-cEYCp.js";import{C as W}from"./CurrencyServices-3wuDp8cZ.js";import{n as p,a as O}from"./toast-DZMsp61l.js";import{P as ne,M as ie,R as ce,T as X}from"./ParentCategory-BlfDuDd5.js";import{a as de,T as pe}from"./DrawerButton-BQHT-xfW.js";import{u as ge,j as me,l as ue}from"./Layout-pFzaaQDc.js";import{u as he,E as D}from"./index.esm-ClJnGQn6.js";import{L as c}from"./LabelArea-DQFDcuEN.js";import{S as P}from"./SwitchToggle-CGiqq8S_.js";import{u as xe}from"./useDisableForDemo-Bu4HEiKz.js";const Ae=({id:g,name:l,type:f,handleClick:x,isChecked:i})=>e.jsx(e.Fragment,{children:e.jsx("input",{id:g,name:l,type:f,onChange:x,checked:i})}),Fe=({id:g,status:l,category:f,currencyStatusName:x})=>{const i=Q(),{setIsUpdate:d}=h.useContext(J),S=async a=>{try{let o;if(l==="show"?o="hide":o="show",i.pathname==="/categories"||f){const t=await ee.updateStatus(a,{status:o});d(!0),p(t.message)}if(i.pathname==="/products"){const t=await se.updateStatus(a,{status:o});d(!0),p(t.message)}if(i.pathname==="/languages"){const t=await Z.updateStatus(a,{status:o});d(!0),p(t.message)}if(i.pathname==="/currencies")if(x==="status"){const t=await W.updateEnabledStatus(a,{status:o});d(!0),p(t.message)}else{const t=await W.updateLiveExchangeRateStatus(a,{live_exchange_rates:o});d(!0),p(t.message)}if(i.pathname==="/attributes"){const t=await H.updateStatus(a,{status:o});d(!0),p(t.message)}if(i.pathname===`/attributes/${i.pathname.split("/")[2]}`){const t=await H.updateChildStatus(a,{status:o});d(!0),p(t.message)}if(i.pathname==="/coupons"){const t=await G.updateStatus(a,{status:o});d(!0),p(t.message)}if(i.pathname==="/our-staff"){const t=await G.updateStaffStatus(a,{status:o});d(!0),p(t.message)}}catch(o){O(o?o?.response?.data?.message:o?.message)}};return e.jsx(oe,{onChange:()=>S(g),checked:l==="show",className:"react-switch md:ml-0",uncheckedIcon:e.jsx("div",{style:{display:"flex",alignItems:"center",height:"100%",width:120,fontSize:14,color:"white",paddingRight:22,paddingTop:1}}),width:30,height:15,handleDiameter:13,offColor:"#E53E3E",onColor:"#2F855A",checkedIcon:e.jsx("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",width:73,height:"100%",fontSize:14,color:"white",paddingLeft:20,paddingTop:1}})})},Y=({register:g,name:l,label:f,placeholder:x,required:i,type:d,value:S})=>e.jsx(e.Fragment,{children:e.jsx(k.Textarea,{className:"border text-sm border-gray-200 focus:border-gray-300 block w-full bg-gray-100",...g(`${l}`,{required:i?`${f} is required!`:!1}),type:d,placeholder:x,name:l,value:S,rows:"4",spellCheck:"false"})}),je=(g,l="en",f)=>{const[x,i]=h.useState(""),[d,S]=h.useState([]),a=Q(),[o,t]=h.useState(""),[j,$]=h.useState(!0),[L,w]=h.useState(!0),[y,B]=h.useState([]),[v,F]=h.useState([]),[R,U]=h.useState(!1),[M,N]=h.useState(""),{isBulkDrawerOpen:_,closeBulkDrawer:C,setIsUpdate:T}=h.useContext(J),{handleDisableForDemo:V}=xe(),{register:E,handleSubmit:K,watch:A,setValue:n,clearErrors:r,formState:{errors:q}}=he(),z=async s=>{if(!V())try{const u={ids:g,categories:y?.map(m=>m._id),category:v[0]?._id,productType:[R?"food":"others"],show:s.show,status:j?"show":"hide",tag:JSON.stringify(d)},b={ids:g,status:j?"show":"hide"},I={ids:g,enabled:j?"show":"hide",live_exchange_rates:L?"show":"hide"},ae={ids:g,parentId:o,description:s.description,parentName:M,status:j?"show":"hide"},te={ids:g,option:s.option,status:j?"show":"hide"},re={ids:g,currentId:f,changeId:s.groupName,status:j?"show":"hide"},le={ids:g,startTime:s.startTime,endTime:s.endTime,status:j?"show":"hide"};if(a.pathname==="/products"){const m=await se.updateManyProducts(u);T(!0),p(m.message),C()}if(a.pathname==="/coupons"){const m=await G.updateManyCoupons(le);T(!0),p(m.message),C()}if(a.pathname==="/languages"){const m=await Z.updateManyLanguage(b);T(!0),p(m.message),C()}if(a.pathname==="/currencies"){const m=await W.updateManyCurrencies(I);T(!0),p(m.message),C()}if(a.pathname==="/categories"||a.pathname===`/categories/${f}`){const m=await ee.updateManyCategory(ae);T(!0),p(m.message),C()}if(a.pathname==="/attributes"){const m=await H.updateManyAttribute(te);T(!0),p(m.message),C()}if(a.pathname===`/attributes/${a.pathname.split("/")[2]}`){const m=await H.updateManyChildAttribute(re);T(!0),p(m.message),C()}}catch(u){O(u?.response?.data?.message||u?.message)}};return h.useEffect(()=>{if(!_){n("parent"),n("children"),n("type"),n("show"),n("name"),i(""),S([]),r("parent"),r("children"),r("type"),r("name"),n("name"),n("iso_code"),n("call_prefix"),n("currency"),n("zone"),r("name"),r("iso_code"),r("call_prefix"),r("currency"),r("zone"),r("status"),n("name"),n("iso_code"),n("country"),n("zone"),n("description"),r("name"),r("iso_code"),r("country"),r("zone"),r("status"),r("show"),r("description"),F([]),B([]);return}},[n,_,r]),h.useEffect(()=>{i(A("children"))},[A,x]),{register:E,watch:A,handleSubmit:K,onSubmit:z,errors:q,tag:d,setTag:S,published:j,setPublished:$,published2:L,setPublished2:w,checked:o,setChecked:t,selectedCategory:y,setSelectedCategory:B,defaultCategory:v,setDefaultCategory:F,selectCategoryName:M,setSelectCategoryName:N,isFoodItem:R,setIsFoodItem:U}},_e=({ids:g,title:l,lang:f,data:x,childId:i,attributes:d,isCheck:S})=>{const{toggleBulkDrawer:a,isBulkDrawerOpen:o,closeBulkDrawer:t}=h.useContext(J),{showingTranslateValue:j}=ge(),{tag:$,setTag:L,published:w,register:y,onSubmit:B,errors:v,checked:F,setChecked:R,resetRefTwo:U,handleSubmit:M,setPublished:N,selectedCategory:_,setSelectedCategory:C,defaultCategory:T,setDefaultCategory:V,selectCategoryName:E,setSelectCategoryName:K}=je(g,f,i),A={motionName:"node-motion",motionAppear:!1,onAppearStart:s=>({height:0}),onAppearActive:s=>({height:s.scrollHeight}),onLeaveStart:s=>({height:s.offsetHeight}),onLeaveActive:()=>({height:0})},n=s=>{let u=[];for(let b of s)u.push({title:j(b?.name),key:b._id,children:b.children.length>0&&n(b.children)});return u},r=(s,u)=>s._id===u?s:s?.children?.reduce((b,I)=>b??r(I,u),void 0),q=s=>{const u=S?.find(b=>b===s);if(S?.length===x[0]?.children?.length)return O("This can't be select as a parent category!");if(u!==void 0)return O("This can't be select as a parent category!");if(s===i)return O("This can't be select as a parent category!");{if(s===void 0)return;R(s);const b=x[0],I=r(b,s);K(j(I?.name))}},z=`
  .rc-tree-child-tree {
    display: hidden;
  }
  .node-motion {
    transition: all .3s;
    overflow-y: hidden;
  }
`;return e.jsx(e.Fragment,{children:e.jsxs(de,{open:o,onClose:t,parent:null,level:null,placement:"right",children:[e.jsx("button",{onClick:a,className:"absolute z-50 text-red-500 hover:bg-red-100 hover:text-gray-700 transition-colors duration-150 bg-white shadow-md mr-6 mt-6 right-0 left-auto w-10 h-10 rounded-full block text-center",children:e.jsx(me,{className:"mx-auto"})}),e.jsxs("div",{className:"flex flex-col w-full h-full justify-between",children:[e.jsx("div",{className:"w-full relative p-6 border-b border-gray-100 bg-gray-50 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-300",children:e.jsx(pe,{title:`Update Selected ${l}`,description:`Apply changes to the selected ${l} from the list`})}),e.jsx(ue.Scrollbars,{className:"w-full md:w-7/12 lg:w-8/12 xl:w-8/12 relative dark:bg-gray-700 dark:text-gray-200",children:e.jsxs("form",{onSubmit:M(B),className:"block",children:[e.jsxs("div",{className:"px-6 pt-8 flex-grow w-full h-full max-h-full pb-40 md:pb-32 lg:pb-32 xl:pb-32",children:[l==="Products"&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(c,{label:"Categorys"}),e.jsx("div",{className:"col-span-8 sm:col-span-4",children:e.jsx(ne,{lang:f,selectedCategory:_,setSelectedCategory:C,setDefaultCategory:V})})]}),e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(c,{label:"Default Category"}),e.jsx("div",{className:"col-span-8 sm:col-span-4",children:e.jsx(ie,{displayValue:"name",isObject:!0,singleSelect:!0,ref:U,hidePlaceholder:!0,onKeyPressFn:function(){},onRemove:function(){},onSearch:function(){},onSelect:s=>V(s),selectedValues:T,options:_,placeholder:"Default Category"})})]}),e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(c,{label:"Published"}),e.jsxs("div",{className:"col-span-8 sm:col-span-4",children:[e.jsx(P,{handleProcess:N,processOption:w}),e.jsx(D,{errorName:v.status})]})]}),e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(c,{label:"Product Tags"}),e.jsx("div",{className:"col-span-8 sm:col-span-4",children:e.jsx(ce,{placeholder:"Product Tag (Write then press enter to add new tag )",tags:$,onChange:s=>L(s)})})]})]}),l==="Coupons"&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(c,{label:"Start Time"}),e.jsxs("div",{className:"col-span-8 sm:col-span-4",children:[e.jsx(k.Input,{...y("startTime",{required:"Coupon Validation Start Time"}),label:"Coupon Validation Start Time",name:"startTime",type:"datetime-local",placeholder:"Start Time"}),e.jsx(D,{errorName:v.startTime})]})]}),e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(c,{label:"End Time"}),e.jsxs("div",{className:"col-span-8 sm:col-span-4",children:[e.jsx(k.Input,{...y("endTime",{required:"Coupon Validation End Time"}),label:"Coupon Validation End Time",name:"endTime",type:"datetime-local",placeholder:"End Time"}),e.jsx(D,{errorName:v.endTime})]})]}),e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(c,{label:"Published"}),e.jsxs("div",{className:"col-span-8 sm:col-span-4",children:[e.jsx(P,{handleProcess:N,processOption:w}),e.jsx(D,{errorName:v.published})]})]})]}),l==="Languages"&&e.jsx(e.Fragment,{children:e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(c,{label:"Published"}),e.jsx("div",{className:"col-span-8 sm:col-span-4",children:e.jsx(P,{title:"",processOption:w,handleProcess:N})})]})}),l==="Currencies"&&e.jsx(e.Fragment,{children:e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(c,{label:"Enabled"}),e.jsx("div",{className:"col-span-8 sm:col-span-4",children:e.jsx(P,{title:"",processOption:w,handleProcess:N})})]})}),l==="Categories"&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(c,{label:"Description"}),e.jsxs("div",{className:"col-span-8 sm:col-span-4",children:[e.jsx(Y,{register:y,label:"Description",name:"description",type:"text",placeholder:"Category Description"}),e.jsx(D,{errorName:v.description})]})]}),e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(c,{label:"Parent Category"}),e.jsxs("div",{className:"col-span-8 sm:col-span-4",children:[e.jsx(k.Input,{readOnly:!0,...y("parent",{required:!1}),name:"parent",value:E||"Home",placeholder:"parent category",type:"text"}),e.jsxs("div",{className:"draggable-demo capitalize",children:[e.jsx("style",{dangerouslySetInnerHTML:{__html:z}}),e.jsx(X,{treeData:n(x),selectedKeys:[F],onSelect:s=>q(s[0]),motion:A,animation:"slide-up"})]})]})]}),e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(c,{label:"Published"}),e.jsx("div",{className:"col-span-8 sm:col-span-4",children:e.jsx(P,{title:"",processOption:w,handleProcess:N})})]})]}),l==="Child Categories"&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(c,{label:"Description"}),e.jsxs("div",{className:"col-span-8 sm:col-span-4",children:[e.jsx(Y,{register:y,label:"Description",name:"description",type:"text",placeholder:"Category Description"}),e.jsx(D,{errorName:v.description})]})]}),e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(c,{label:"Parent Category"}),e.jsxs("div",{className:"col-span-8 sm:col-span-4",children:[e.jsx(k.Input,{readOnly:!0,...y("parent",{required:!1}),name:"parent",value:E||"Home",placeholder:"parent category",type:"text"}),e.jsxs("div",{className:"draggable-demo capitalize",children:[e.jsx("style",{dangerouslySetInnerHTML:{__html:z}}),e.jsx(X,{treeData:n(x),selectedKeys:[F],onSelect:s=>q(s[0]),motion:A,animation:"slide-up"})]})]})]}),e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(c,{label:"Published"}),e.jsx("div",{className:"col-span-8 sm:col-span-4",children:e.jsx(P,{title:"",processOption:w,handleProcess:N})})]})]}),l==="Attributes"&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(c,{label:"Options"}),e.jsxs("div",{className:"col-span-8 sm:col-span-4",children:[e.jsxs(k.Select,{name:"option",...y("option",{required:"Option is required!"}),children:[e.jsx("option",{value:"",defaultValue:!0,hidden:!0,children:"Select type"}),e.jsx("option",{value:"Dropdown",children:"Dropdown"}),e.jsx("option",{value:"Radio",children:"Radio"})]}),e.jsx(D,{errorName:v.option})]})]}),e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(c,{label:"Published"}),e.jsx("div",{className:"col-span-8 sm:col-span-4",children:e.jsx(P,{title:"",processOption:w,handleProcess:N})})]})]}),l==="Attribute Value(s)"&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(c,{label:"Change Attribute Group"}),e.jsxs("div",{className:"col-span-8 sm:col-span-4",children:[e.jsxs(k.Select,{name:"groupName",...y("groupName",{required:!1}),children:[e.jsx("option",{value:"",defaultValue:!0,hidden:!0,children:"Select Attribute Group"}),d?.map((s,u)=>e.jsx("option",{value:s._id,children:j(s?.name)},u+1))]}),e.jsx(D,{errorName:v.groupName})]})]}),e.jsxs("div",{className:"grid grid-cols-6 gap-3 md:gap-5 xl:gap-6 lg:gap-6 mb-6",children:[e.jsx(c,{label:"Published"}),e.jsx("div",{className:"col-span-8 sm:col-span-4",children:e.jsx(P,{title:"",processOption:w,handleProcess:N})})]})]})]}),e.jsxs("div",{className:"fixed bottom-0 w-full right-0 py-4 lg:py-8 px-6 grid gap-4 lg:gap-6 xl:gap-6 md:flex xl:flex bg-gray-50 border-t border-gray-100 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-300",children:[e.jsx("div",{className:"flex-grow-0 md:flex-grow lg:flex-grow xl:flex-grow",children:e.jsx(k.Button,{onClick:a,className:" text-red-500 hover:bg-red-50 hover:border-red-100 hover:text-red-600 dark:bg-gray-700 dark:border-gray-700 dark:text-gray-500 dark:hover:bg-gray-800 dark:hover:text-red-700",layout:"outline",children:"Cancel"})}),e.jsx("div",{className:"flex-grow-0 md:flex-grow lg:flex-grow xl:flex-grow",children:e.jsxs(k.Button,{type:"submit",className:"h-12 w-full",children:[" ","Bulk Update ",l]})})]})]})})]})]})})};export{_e as B,Ae as C,Fe as S,Y as T};
