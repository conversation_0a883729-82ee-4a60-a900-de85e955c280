const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["@/assets/Dashboard-bmOwL-ir.js","@/assets/index-BnbL29JP.js","@/assets/useFilter-7smJgyBE.js","@/assets/ProductServices-CXwJ-2YB.js","@/assets/index-C148XJoK.js","@/assets/useDisableForDemo-Bu4HEiKz.js","@/assets/toast-DZMsp61l.js","@/assets/CouponServices-BN-cEYCp.js","@/assets/CurrencyServices-3wuDp8cZ.js","@/assets/index.esm-Bq5jp4f7.js","@/assets/iconBase-CKOh_aia.js","@/assets/useAsync-DWXVKl2F.js","@/assets/TableLoading-BGBA3G2p.js","@/assets/OrderTable-CpEV7rsM.js","@/assets/Status-B_9gO_Q4.js","@/assets/Tooltip-CCK2Gwc2.js","@/assets/index-BfvxtTdM.js","@/assets/OrderServices-CpvSXYO1.js","@/assets/SelectStatus-DmARa0Zb.js","@/assets/NotFound-H45Wi1lW.js","@/assets/PageTitle-FuOKSvYQ.js","@/assets/AnimatedContent-0V4vlNfe.js","@/assets/Attributes-fusadkhi.js","@/assets/DrawerButton-BQHT-xfW.js","@/assets/SelectLanguageTwo-KDdaD-gj.js","@/assets/spinner-CkndCogW.js","@/assets/EditDeleteButton-BfkOlssy.js","@/assets/AdminServices-DjQuFfvs.js","@/assets/BulkActionDrawer-0F-ByaaS.js","@/assets/index.prod-BbOPZ2BB.js","@/assets/ParentCategory-BlfDuDd5.js","@/assets/index.esm-ClJnGQn6.js","@/assets/LabelArea-DQFDcuEN.js","@/assets/SwitchToggle-CGiqq8S_.js","@/assets/InputArea-B_vEs1uv.js","@/assets/useAttributeSubmit-DQ8-wYS4.js","@/assets/useTranslationValue-DCEiB8J2.js","@/assets/UploadMany-DQiD3frs.js","@/assets/exportFromJSON-fDIoOtpr.js","@/assets/ChildAttributes-DnqoCjdK.js","@/assets/Loading-_yh85t44.js","@/assets/Products-KGwJMDVZ.js","@/assets/ProductDrawer-rLFmsmir.js","@/assets/index-CJFAfqOd.js","@/assets/InputValue-ohzJqb3r.js","@/assets/Uploader-COz_-nhd.js","@/assets/_commonjs-dynamic-modules-BJDvAndU.js","@/assets/ProductDetails-DaFthmiY.js","@/assets/Category-CV1YsYME.js","@/assets/CategoryTable-Bnf_Hwk3.js","@/assets/ChildCategory-X9NjJqvR.js","@/assets/Staff-EU7FJ_LS.js","@/assets/useStaffSubmit-D-tFnEKy.js","@/assets/SelectRole-DrH9K_iP.js","@/assets/Customers-CDSaZG3L.js","@/assets/CustomerOrder-DTywdyNw.js","@/assets/Orders-C4vZ_Guq.js","@/assets/OrderInvoice-DE8Ez2u_.js","@/assets/index-OcClbx-5.js","@/assets/Coupons-UHvQwB1A.js","@/assets/404-CE5DCzqU.js","@/assets/ComingSoon-BWDZjz5t.js","@/assets/EditProfile-VgWWB-Nm.js","@/assets/Languages-DayBgvtb.js","@/assets/Currencies-BZXFJAfc.js","@/assets/Setting-DEaW96v2.js","@/assets/InputAreaTwo-0b6oYMpv.js","@/assets/SettingContainer-CMYqe4I4.js","@/assets/StoreHome-Bib7SISz.js","@/assets/StoreSetting-C79kxAm5.js","@/assets/Notifications-DD3SXWP6.js"])))=>i.map(i=>d[i]);
import{R as ue,r as v,A as De,u as Ve,j as a,c as pt,a as rt,b as vt,g as nt,S as ge,d as Oe,e as yt,L as xt,f as ne,h as K,i as je,k as Ne,l as he,m as de,N as at,_ as V,n as kt,o as bt,T as wt}from"./index-BnbL29JP.js";import{G as T}from"./iconBase-CKOh_aia.js";function it(t=ue){return function(){return v.useContext(t)}}const St=it();function ot(t=ue){const l=t===ue?St:it(t);return function(){const{store:m}=l();return m}}const Ht=ot();function Lt(t=ue){const l=t===ue?Ht:ot(t);return function(){return l().dispatch}}const Tt=Lt(),st=()=>{const{state:t}=v.useContext(De),{adminInfo:l}=t,m=Ve()?.pathname?.split("?")[0].split("/")[1],[c,b]=v.useState(),[u,x]=v.useState([]),k=async(h,g)=>{const L=await crypto.subtle.digest("SHA-256",new TextEncoder().encode("856305f1a5b7ba87b8448e69b3bb7a4631c23f0afa2ca5331fa1373f7e372345")),w=new Uint8Array(h.match(/.{1,2}/g).map(z=>parseInt(z,16))),E=new Uint8Array(g.match(/.{1,2}/g).map(z=>parseInt(z,16)));try{const z=await crypto.subtle.decrypt({name:"AES-CBC",iv:E},await crypto.subtle.importKey("raw",L,{name:"AES-CBC"},!1,["decrypt"]),w);return new TextDecoder().decode(z)}catch(z){return console.error("Decryption failed:",z),null}};return v.useEffect(()=>{(async()=>{if(l?.data&&l?.iv)try{const g=await k(l.data,l.iv),p=JSON.parse(g),L=p.pop();b(L),x(p)}catch(g){console.error("Failed to decrypt and parse data:",g)}})()},[l]),{role:c,path:m,accessList:u}},_t="/@/assets/404-DUm08_0T.svg",Ct=()=>{const l=Ve()?.pathname;return v.useEffect(()=>{l==="/"&&window.location.replace("/dashboard")},[l]),a.jsx(a.Fragment,{children:a.jsx("div",{className:"px-6 py-16 lg:py-20 h-screen flex flex-wrap content-center",children:a.jsxs("div",{className:"block justify-items-stretch mx-auto items-center text-center",children:[a.jsx("img",{width:650,height:450,src:_t,alt:"404"}),a.jsx("h2",{className:"font-bold font-serif dark:text-gray-200 font-2xl lg:text-4xl leading-7 mb-4",children:"Page is not found!"}),a.jsx("p",{className:"text-red-400 text-lg",children:"Sorry you don't have access to this page!"})]})})})},Mt=({children:t})=>{const{path:l,accessList:d}=st();return d?.includes(l)?a.jsx("main",{className:"h-full overflow-y-auto",children:a.jsx("div",{className:"sm:container grid lg:px-6 sm:px-4 px-2 mx-auto",children:t})}):a.jsx(Ct,{})};var ve={},ye={},se={exports:{}},ae={exports:{}},jt=ae.exports,Ee;function Dt(){return Ee||(Ee=1,(function(){var t,l,d,m,c,b;typeof performance<"u"&&performance!==null&&performance.now?ae.exports=function(){return performance.now()}:typeof process<"u"&&process!==null&&process.hrtime?(ae.exports=function(){return(t()-c)/1e6},l=process.hrtime,t=function(){var u;return u=l(),u[0]*1e9+u[1]},m=t(),b=process.uptime()*1e9,c=m-b):Date.now?(ae.exports=function(){return Date.now()-d},d=Date.now()):(ae.exports=function(){return new Date().getTime()-d},d=new Date().getTime())}).call(jt)),ae.exports}var $e;function Vt(){if($e)return se.exports;$e=1;for(var t=Dt(),l=typeof window>"u"?pt:window,d=["moz","webkit"],m="AnimationFrame",c=l["request"+m],b=l["cancel"+m]||l["cancelRequest"+m],u=0;!c&&u<d.length;u++)c=l[d[u]+"Request"+m],b=l[d[u]+"Cancel"+m]||l[d[u]+"CancelRequest"+m];if(!c||!b){var x=0,k=0,h=[],g=1e3/60;c=function(p){if(h.length===0){var L=t(),w=Math.max(0,g-(L-x));x=w+L,setTimeout(function(){var E=h.slice(0);h.length=0;for(var z=0;z<E.length;z++)if(!E[z].cancelled)try{E[z].callback(x)}catch(_){setTimeout(function(){throw _},0)}},Math.round(w))}return h.push({handle:++k,callback:p,cancelled:!1}),k},b=function(p){for(var L=0;L<h.length;L++)h[L].handle===p&&(h[L].cancelled=!0)}}return se.exports=function(p){return c.call(l,p)},se.exports.cancel=function(){b.apply(l,arguments)},se.exports.polyfill=function(p){p||(p=l),p.requestAnimationFrame=c,p.cancelAnimationFrame=b},se.exports}var le={exports:{}},xe,We;function Nt(){if(We)return xe;We=1;var t=null,l=["Webkit","Moz","O","ms"];return xe=function(m){t||(t=document.createElement("div"));var c=t.style;if(m in c)return m;for(var b=m.charAt(0).toUpperCase()+m.slice(1),u=l.length;u>=0;u--){var x=l[u]+b;if(x in c)return x}return!1},xe}var ke,Re;function zt(){if(Re)return ke;Re=1,ke=m;var t=/\s/,l=/(_|-|\.|:)/,d=/([a-z][A-Z]|[A-Z][a-z])/;function m(k){return t.test(k)?k.toLowerCase():l.test(k)?(b(k)||k).toLowerCase():d.test(k)?x(k).toLowerCase():k.toLowerCase()}var c=/[\W_]+(.|$)/g;function b(k){return k.replace(c,function(h,g){return g?" "+g:""})}var u=/(.)([A-Z]+)/g;function x(k){return k.replace(u,function(h,g,p){return g+" "+p.toLowerCase().split("").join(" ")})}return ke}var be,Fe;function Ot(){if(Fe)return be;Fe=1;var t=zt();be=l;function l(d){return t(d).replace(/[\W_]+(.|$)/g,function(m,c){return c?" "+c:""}).trim()}return be}var we,Ae;function Et(){if(Ae)return we;Ae=1;var t=Ot();we=l;function l(d){return t(d).replace(/\s(\w)/g,function(m,c){return c.toUpperCase()})}return we}var Se,Pe;function $t(){if(Pe)return Se;Pe=1;var t={animationIterationCount:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridRow:!0,gridColumn:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,stopOpacity:!0,strokeDashoffset:!0,strokeOpacity:!0,strokeWidth:!0};return Se=function(l,d){return typeof d=="number"&&!t[l]?d+"px":d},Se}var Ie;function lt(){if(Ie)return le.exports;Ie=1;var t=Nt(),l=Et(),d={float:"cssFloat"},m=$t();function c(k,h,g){var p=d[h];if(typeof p>"u"&&(p=u(h)),p){if(g===void 0)return k.style[p];k.style[p]=m(p,g)}}function b(k,h){for(var g in h)h.hasOwnProperty(g)&&c(k,g,h[g])}function u(k){var h=l(k),g=t(h);return d[h]=d[k]=d[g]=g,g}function x(){arguments.length===2?typeof arguments[1]=="string"?arguments[0].style.cssText=arguments[1]:b(arguments[0],arguments[1]):c(arguments[0],arguments[1],arguments[2])}return le.exports=x,le.exports.set=x,le.exports.get=function(k,h){return Array.isArray(h)?h.reduce(function(g,p){return g[p]=c(k,p||""),g},{}):c(k,h||"")},le.exports}var He={},Be;function Wt(){return Be||(Be=1,function(t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=l;function l(d){return typeof d=="string"}}(He)),He}var Le={},qe;function Rt(){return qe||(qe=1,function(t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=b;var l=lt(),d=m(l);function m(u){return u&&u.__esModule?u:{default:u}}var c=!1;function b(){var u=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0;if(u&&c!==!1)return c;if(typeof document<"u"){var x=document.createElement("div");(0,d.default)(x,{width:100,height:100,position:"absolute",top:-9999,overflow:"scroll",MsOverflowStyle:"scrollbar"}),document.body.appendChild(x),c=x.offsetWidth-x.clientWidth,document.body.removeChild(x)}else c=0;return c||0}}(Le)),Le}var Te={},Ze;function Ft(){return Ze||(Ze=1,function(t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=l;function l(){return!1}}(Te)),Te}var _e={},Ue;function At(){return Ue||(Ue=1,function(t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=l;function l(d){var m=d.clientWidth,c=getComputedStyle(d),b=c.paddingLeft,u=c.paddingRight;return m-parseFloat(b)-parseFloat(u)}}(_e)),_e}var Ce={},Ye;function Pt(){return Ye||(Ye=1,function(t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=l;function l(d){var m=d.clientHeight,c=getComputedStyle(d),b=c.paddingTop,u=c.paddingBottom;return m-parseFloat(b)-parseFloat(u)}}(Ce)),Ce}var q={},Ge;function It(){return Ge||(Ge=1,Object.defineProperty(q,"__esModule",{value:!0}),q.containerStyleDefault={position:"relative",overflow:"hidden",width:"100%",height:"100%"},q.containerStyleAutoHeight={height:"auto"},q.viewStyleDefault={position:"absolute",top:0,left:0,right:0,bottom:0,overflow:"scroll",WebkitOverflowScrolling:"touch"},q.viewStyleAutoHeight={position:"relative",top:void 0,left:void 0,right:void 0,bottom:void 0},q.viewStyleUniversalInitial={overflow:"hidden",marginRight:0,marginBottom:0},q.trackHorizontalStyleDefault={position:"absolute",height:6},q.trackVerticalStyleDefault={position:"absolute",width:6},q.thumbHorizontalStyleDefault={position:"relative",display:"block",height:"100%"},q.thumbVerticalStyleDefault={position:"relative",display:"block",width:"100%"},q.disableSelectStyle={userSelect:"none"},q.disableSelectStyleReset={userSelect:""}),q}var re={},Xe;function Bt(){if(Xe)return re;Xe=1,Object.defineProperty(re,"__esModule",{value:!0});var t=Object.assign||function(g){for(var p=1;p<arguments.length;p++){var L=arguments[p];for(var w in L)Object.prototype.hasOwnProperty.call(L,w)&&(g[w]=L[w])}return g};re.renderViewDefault=b,re.renderTrackHorizontalDefault=u,re.renderTrackVerticalDefault=x,re.renderThumbHorizontalDefault=k,re.renderThumbVerticalDefault=h;var l=rt(),d=m(l);function m(g){return g&&g.__esModule?g:{default:g}}function c(g,p){var L={};for(var w in g)p.indexOf(w)>=0||Object.prototype.hasOwnProperty.call(g,w)&&(L[w]=g[w]);return L}function b(g){return d.default.createElement("div",g)}function u(g){var p=g.style,L=c(g,["style"]),w=t({},p,{right:2,bottom:2,left:2,borderRadius:3});return d.default.createElement("div",t({style:w},L))}function x(g){var p=g.style,L=c(g,["style"]),w=t({},p,{right:2,bottom:2,top:2,borderRadius:3});return d.default.createElement("div",t({style:w},L))}function k(g){var p=g.style,L=c(g,["style"]),w=t({},p,{cursor:"pointer",borderRadius:"inherit",backgroundColor:"rgba(0,0,0,.2)"});return d.default.createElement("div",t({style:w},L))}function h(g){var p=g.style,L=c(g,["style"]),w=t({},p,{cursor:"pointer",borderRadius:"inherit",backgroundColor:"rgba(0,0,0,.2)"});return d.default.createElement("div",t({style:w},L))}return re}var Ke;function qt(){return Ke||(Ke=1,function(t){Object.defineProperty(t,"__esModule",{value:!0});var l=Object.assign||function(M){for(var S=1;S<arguments.length;S++){var o=arguments[S];for(var e in o)Object.prototype.hasOwnProperty.call(o,e)&&(M[e]=o[e])}return M},d=function(){function M(S,o){for(var e=0;e<o.length;e++){var r=o[e];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(S,r.key,r)}}return function(S,o,e){return o&&M(S.prototype,o),e&&M(S,e),S}}(),m=Vt(),c=$(m),b=lt(),u=$(b),x=rt(),k=vt(),h=$(k),g=Wt(),p=$(g),L=Rt(),w=$(L),E=Ft(),z=$(E),_=At(),U=$(_),ie=Pt(),J=$(ie),P=It(),I=Bt();function $(M){return M&&M.__esModule?M:{default:M}}function ee(M,S){var o={};for(var e in M)S.indexOf(e)>=0||Object.prototype.hasOwnProperty.call(M,e)&&(o[e]=M[e]);return o}function te(M,S){if(!(M instanceof S))throw new TypeError("Cannot call a class as a function")}function Y(M,S){if(!M)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return S&&(typeof S=="object"||typeof S=="function")?S:M}function N(M,S){if(typeof S!="function"&&S!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof S);M.prototype=Object.create(S&&S.prototype,{constructor:{value:M,enumerable:!1,writable:!0,configurable:!0}}),S&&(Object.setPrototypeOf?Object.setPrototypeOf(M,S):M.__proto__=S)}var C=function(M){N(S,M);function S(o){var e;te(this,S);for(var r=arguments.length,i=Array(r>1?r-1:0),s=1;s<r;s++)i[s-1]=arguments[s];var n=Y(this,(e=S.__proto__||Object.getPrototypeOf(S)).call.apply(e,[this,o].concat(i)));return n.getScrollLeft=n.getScrollLeft.bind(n),n.getScrollTop=n.getScrollTop.bind(n),n.getScrollWidth=n.getScrollWidth.bind(n),n.getScrollHeight=n.getScrollHeight.bind(n),n.getClientWidth=n.getClientWidth.bind(n),n.getClientHeight=n.getClientHeight.bind(n),n.getValues=n.getValues.bind(n),n.getThumbHorizontalWidth=n.getThumbHorizontalWidth.bind(n),n.getThumbVerticalHeight=n.getThumbVerticalHeight.bind(n),n.getScrollLeftForOffset=n.getScrollLeftForOffset.bind(n),n.getScrollTopForOffset=n.getScrollTopForOffset.bind(n),n.scrollLeft=n.scrollLeft.bind(n),n.scrollTop=n.scrollTop.bind(n),n.scrollToLeft=n.scrollToLeft.bind(n),n.scrollToTop=n.scrollToTop.bind(n),n.scrollToRight=n.scrollToRight.bind(n),n.scrollToBottom=n.scrollToBottom.bind(n),n.handleTrackMouseEnter=n.handleTrackMouseEnter.bind(n),n.handleTrackMouseLeave=n.handleTrackMouseLeave.bind(n),n.handleHorizontalTrackMouseDown=n.handleHorizontalTrackMouseDown.bind(n),n.handleVerticalTrackMouseDown=n.handleVerticalTrackMouseDown.bind(n),n.handleHorizontalThumbMouseDown=n.handleHorizontalThumbMouseDown.bind(n),n.handleVerticalThumbMouseDown=n.handleVerticalThumbMouseDown.bind(n),n.handleWindowResize=n.handleWindowResize.bind(n),n.handleScroll=n.handleScroll.bind(n),n.handleDrag=n.handleDrag.bind(n),n.handleDragEnd=n.handleDragEnd.bind(n),n.state={didMountUniversal:!1},n}return d(S,[{key:"componentDidMount",value:function(){this.addListeners(),this.update(),this.componentDidMountUniversal()}},{key:"componentDidMountUniversal",value:function(){var e=this.props.universal;e&&this.setState({didMountUniversal:!0})}},{key:"componentDidUpdate",value:function(){this.update()}},{key:"componentWillUnmount",value:function(){this.removeListeners(),(0,m.cancel)(this.requestFrame),clearTimeout(this.hideTracksTimeout),clearInterval(this.detectScrollingInterval)}},{key:"getScrollLeft",value:function(){return this.view?this.view.scrollLeft:0}},{key:"getScrollTop",value:function(){return this.view?this.view.scrollTop:0}},{key:"getScrollWidth",value:function(){return this.view?this.view.scrollWidth:0}},{key:"getScrollHeight",value:function(){return this.view?this.view.scrollHeight:0}},{key:"getClientWidth",value:function(){return this.view?this.view.clientWidth:0}},{key:"getClientHeight",value:function(){return this.view?this.view.clientHeight:0}},{key:"getValues",value:function(){var e=this.view||{},r=e.scrollLeft,i=r===void 0?0:r,s=e.scrollTop,n=s===void 0?0:s,f=e.scrollWidth,y=f===void 0?0:f,H=e.scrollHeight,j=H===void 0?0:H,D=e.clientWidth,O=D===void 0?0:D,W=e.clientHeight,R=W===void 0?0:W;return{left:i/(y-O)||0,top:n/(j-R)||0,scrollLeft:i,scrollTop:n,scrollWidth:y,scrollHeight:j,clientWidth:O,clientHeight:R}}},{key:"getThumbHorizontalWidth",value:function(){var e=this.props,r=e.thumbSize,i=e.thumbMinSize,s=this.view,n=s.scrollWidth,f=s.clientWidth,y=(0,U.default)(this.trackHorizontal),H=Math.ceil(f/n*y);return y<=H?0:r||Math.max(H,i)}},{key:"getThumbVerticalHeight",value:function(){var e=this.props,r=e.thumbSize,i=e.thumbMinSize,s=this.view,n=s.scrollHeight,f=s.clientHeight,y=(0,J.default)(this.trackVertical),H=Math.ceil(f/n*y);return y<=H?0:r||Math.max(H,i)}},{key:"getScrollLeftForOffset",value:function(e){var r=this.view,i=r.scrollWidth,s=r.clientWidth,n=(0,U.default)(this.trackHorizontal),f=this.getThumbHorizontalWidth();return e/(n-f)*(i-s)}},{key:"getScrollTopForOffset",value:function(e){var r=this.view,i=r.scrollHeight,s=r.clientHeight,n=(0,J.default)(this.trackVertical),f=this.getThumbVerticalHeight();return e/(n-f)*(i-s)}},{key:"scrollLeft",value:function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0;this.view&&(this.view.scrollLeft=e)}},{key:"scrollTop",value:function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0;this.view&&(this.view.scrollTop=e)}},{key:"scrollToLeft",value:function(){this.view&&(this.view.scrollLeft=0)}},{key:"scrollToTop",value:function(){this.view&&(this.view.scrollTop=0)}},{key:"scrollToRight",value:function(){this.view&&(this.view.scrollLeft=this.view.scrollWidth)}},{key:"scrollToBottom",value:function(){this.view&&(this.view.scrollTop=this.view.scrollHeight)}},{key:"addListeners",value:function(){if(!(typeof document>"u"||!this.view)){var e=this.view,r=this.trackHorizontal,i=this.trackVertical,s=this.thumbHorizontal,n=this.thumbVertical;e.addEventListener("scroll",this.handleScroll),(0,w.default)()&&(r.addEventListener("mouseenter",this.handleTrackMouseEnter),r.addEventListener("mouseleave",this.handleTrackMouseLeave),r.addEventListener("mousedown",this.handleHorizontalTrackMouseDown),i.addEventListener("mouseenter",this.handleTrackMouseEnter),i.addEventListener("mouseleave",this.handleTrackMouseLeave),i.addEventListener("mousedown",this.handleVerticalTrackMouseDown),s.addEventListener("mousedown",this.handleHorizontalThumbMouseDown),n.addEventListener("mousedown",this.handleVerticalThumbMouseDown),window.addEventListener("resize",this.handleWindowResize))}}},{key:"removeListeners",value:function(){if(!(typeof document>"u"||!this.view)){var e=this.view,r=this.trackHorizontal,i=this.trackVertical,s=this.thumbHorizontal,n=this.thumbVertical;e.removeEventListener("scroll",this.handleScroll),(0,w.default)()&&(r.removeEventListener("mouseenter",this.handleTrackMouseEnter),r.removeEventListener("mouseleave",this.handleTrackMouseLeave),r.removeEventListener("mousedown",this.handleHorizontalTrackMouseDown),i.removeEventListener("mouseenter",this.handleTrackMouseEnter),i.removeEventListener("mouseleave",this.handleTrackMouseLeave),i.removeEventListener("mousedown",this.handleVerticalTrackMouseDown),s.removeEventListener("mousedown",this.handleHorizontalThumbMouseDown),n.removeEventListener("mousedown",this.handleVerticalThumbMouseDown),window.removeEventListener("resize",this.handleWindowResize),this.teardownDragging())}}},{key:"handleScroll",value:function(e){var r=this,i=this.props,s=i.onScroll,n=i.onScrollFrame;s&&s(e),this.update(function(f){var y=f.scrollLeft,H=f.scrollTop;r.viewScrollLeft=y,r.viewScrollTop=H,n&&n(f)}),this.detectScrolling()}},{key:"handleScrollStart",value:function(){var e=this.props.onScrollStart;e&&e(),this.handleScrollStartAutoHide()}},{key:"handleScrollStartAutoHide",value:function(){var e=this.props.autoHide;e&&this.showTracks()}},{key:"handleScrollStop",value:function(){var e=this.props.onScrollStop;e&&e(),this.handleScrollStopAutoHide()}},{key:"handleScrollStopAutoHide",value:function(){var e=this.props.autoHide;e&&this.hideTracks()}},{key:"handleWindowResize",value:function(){(0,w.default)(!1),this.forceUpdate()}},{key:"handleHorizontalTrackMouseDown",value:function(e){e.preventDefault();var r=e.target,i=e.clientX,s=r.getBoundingClientRect(),n=s.left,f=this.getThumbHorizontalWidth(),y=Math.abs(n-i)-f/2;this.view.scrollLeft=this.getScrollLeftForOffset(y)}},{key:"handleVerticalTrackMouseDown",value:function(e){e.preventDefault();var r=e.target,i=e.clientY,s=r.getBoundingClientRect(),n=s.top,f=this.getThumbVerticalHeight(),y=Math.abs(n-i)-f/2;this.view.scrollTop=this.getScrollTopForOffset(y)}},{key:"handleHorizontalThumbMouseDown",value:function(e){e.preventDefault(),this.handleDragStart(e);var r=e.target,i=e.clientX,s=r.offsetWidth,n=r.getBoundingClientRect(),f=n.left;this.prevPageX=s-(i-f)}},{key:"handleVerticalThumbMouseDown",value:function(e){e.preventDefault(),this.handleDragStart(e);var r=e.target,i=e.clientY,s=r.offsetHeight,n=r.getBoundingClientRect(),f=n.top;this.prevPageY=s-(i-f)}},{key:"setupDragging",value:function(){(0,u.default)(document.body,P.disableSelectStyle),document.addEventListener("mousemove",this.handleDrag),document.addEventListener("mouseup",this.handleDragEnd),document.onselectstart=z.default}},{key:"teardownDragging",value:function(){(0,u.default)(document.body,P.disableSelectStyleReset),document.removeEventListener("mousemove",this.handleDrag),document.removeEventListener("mouseup",this.handleDragEnd),document.onselectstart=void 0}},{key:"handleDragStart",value:function(e){this.dragging=!0,e.stopImmediatePropagation(),this.setupDragging()}},{key:"handleDrag",value:function(e){if(this.prevPageX){var r=e.clientX,i=this.trackHorizontal.getBoundingClientRect(),s=i.left,n=this.getThumbHorizontalWidth(),f=n-this.prevPageX,y=-s+r-f;this.view.scrollLeft=this.getScrollLeftForOffset(y)}if(this.prevPageY){var H=e.clientY,j=this.trackVertical.getBoundingClientRect(),D=j.top,O=this.getThumbVerticalHeight(),W=O-this.prevPageY,R=-D+H-W;this.view.scrollTop=this.getScrollTopForOffset(R)}return!1}},{key:"handleDragEnd",value:function(){this.dragging=!1,this.prevPageX=this.prevPageY=0,this.teardownDragging(),this.handleDragEndAutoHide()}},{key:"handleDragEndAutoHide",value:function(){var e=this.props.autoHide;e&&this.hideTracks()}},{key:"handleTrackMouseEnter",value:function(){this.trackMouseOver=!0,this.handleTrackMouseEnterAutoHide()}},{key:"handleTrackMouseEnterAutoHide",value:function(){var e=this.props.autoHide;e&&this.showTracks()}},{key:"handleTrackMouseLeave",value:function(){this.trackMouseOver=!1,this.handleTrackMouseLeaveAutoHide()}},{key:"handleTrackMouseLeaveAutoHide",value:function(){var e=this.props.autoHide;e&&this.hideTracks()}},{key:"showTracks",value:function(){clearTimeout(this.hideTracksTimeout),(0,u.default)(this.trackHorizontal,{opacity:1}),(0,u.default)(this.trackVertical,{opacity:1})}},{key:"hideTracks",value:function(){var e=this;if(!this.dragging&&!this.scrolling&&!this.trackMouseOver){var r=this.props.autoHideTimeout;clearTimeout(this.hideTracksTimeout),this.hideTracksTimeout=setTimeout(function(){(0,u.default)(e.trackHorizontal,{opacity:0}),(0,u.default)(e.trackVertical,{opacity:0})},r)}}},{key:"detectScrolling",value:function(){var e=this;this.scrolling||(this.scrolling=!0,this.handleScrollStart(),this.detectScrollingInterval=setInterval(function(){e.lastViewScrollLeft===e.viewScrollLeft&&e.lastViewScrollTop===e.viewScrollTop&&(clearInterval(e.detectScrollingInterval),e.scrolling=!1,e.handleScrollStop()),e.lastViewScrollLeft=e.viewScrollLeft,e.lastViewScrollTop=e.viewScrollTop},100))}},{key:"raf",value:function(e){var r=this;this.requestFrame&&c.default.cancel(this.requestFrame),this.requestFrame=(0,c.default)(function(){r.requestFrame=void 0,e()})}},{key:"update",value:function(e){var r=this;this.raf(function(){return r._update(e)})}},{key:"_update",value:function(e){var r=this.props,i=r.onUpdate,s=r.hideTracksWhenNotNeeded,n=this.getValues();if((0,w.default)()){var f=n.scrollLeft,y=n.clientWidth,H=n.scrollWidth,j=(0,U.default)(this.trackHorizontal),D=this.getThumbHorizontalWidth(),O=f/(H-y)*(j-D),W={width:D,transform:"translateX("+O+"px)"},R=n.scrollTop,B=n.clientHeight,A=n.scrollHeight,F=(0,J.default)(this.trackVertical),Z=this.getThumbVerticalHeight(),Q=R/(A-B)*(F-Z),G={height:Z,transform:"translateY("+Q+"px)"};if(s){var me={visibility:H>y?"visible":"hidden"},pe={visibility:A>B?"visible":"hidden"};(0,u.default)(this.trackHorizontal,me),(0,u.default)(this.trackVertical,pe)}(0,u.default)(this.thumbHorizontal,W),(0,u.default)(this.thumbVertical,G)}i&&i(n),typeof e=="function"&&e(n)}},{key:"render",value:function(){var e=this,r=(0,w.default)(),i=this.props;i.onScroll,i.onScrollFrame,i.onScrollStart,i.onScrollStop,i.onUpdate;var s=i.renderView,n=i.renderTrackHorizontal,f=i.renderTrackVertical,y=i.renderThumbHorizontal,H=i.renderThumbVertical,j=i.tagName;i.hideTracksWhenNotNeeded;var D=i.autoHide;i.autoHideTimeout;var O=i.autoHideDuration;i.thumbSize,i.thumbMinSize;var W=i.universal,R=i.autoHeight,B=i.autoHeightMin,A=i.autoHeightMax,F=i.style,Z=i.children,Q=ee(i,["onScroll","onScrollFrame","onScrollStart","onScrollStop","onUpdate","renderView","renderTrackHorizontal","renderTrackVertical","renderThumbHorizontal","renderThumbVertical","tagName","hideTracksWhenNotNeeded","autoHide","autoHideTimeout","autoHideDuration","thumbSize","thumbMinSize","universal","autoHeight","autoHeightMin","autoHeightMax","style","children"]),G=this.state.didMountUniversal,me=l({},P.containerStyleDefault,R&&l({},P.containerStyleAutoHeight,{minHeight:B,maxHeight:A}),F),pe=l({},P.viewStyleDefault,{marginRight:r?-r:0,marginBottom:r?-r:0},R&&l({},P.viewStyleAutoHeight,{minHeight:(0,p.default)(B)?"calc("+B+" + "+r+"px)":B+r,maxHeight:(0,p.default)(A)?"calc("+A+" + "+r+"px)":A+r}),R&&W&&!G&&{minHeight:B,maxHeight:A},W&&!G&&P.viewStyleUniversalInitial),ze={transition:"opacity "+O+"ms",opacity:0},gt=l({},P.trackHorizontalStyleDefault,D&&ze,(!r||W&&!G)&&{display:"none"}),mt=l({},P.trackVerticalStyleDefault,D&&ze,(!r||W&&!G)&&{display:"none"});return(0,x.createElement)(j,l({},Q,{style:me,ref:function(X){e.container=X}}),[(0,x.cloneElement)(s({style:pe}),{key:"view",ref:function(X){e.view=X}},Z),(0,x.cloneElement)(n({style:gt}),{key:"trackHorizontal",ref:function(X){e.trackHorizontal=X}},(0,x.cloneElement)(y({style:P.thumbHorizontalStyleDefault}),{ref:function(X){e.thumbHorizontal=X}})),(0,x.cloneElement)(f({style:mt}),{key:"trackVertical",ref:function(X){e.trackVertical=X}},(0,x.cloneElement)(H({style:P.thumbVerticalStyleDefault}),{ref:function(X){e.thumbVertical=X}}))])}}]),S}(x.Component);t.default=C,C.propTypes={onScroll:h.default.func,onScrollFrame:h.default.func,onScrollStart:h.default.func,onScrollStop:h.default.func,onUpdate:h.default.func,renderView:h.default.func,renderTrackHorizontal:h.default.func,renderTrackVertical:h.default.func,renderThumbHorizontal:h.default.func,renderThumbVertical:h.default.func,tagName:h.default.string,thumbSize:h.default.number,thumbMinSize:h.default.number,hideTracksWhenNotNeeded:h.default.bool,autoHide:h.default.bool,autoHideTimeout:h.default.number,autoHideDuration:h.default.number,autoHeight:h.default.bool,autoHeightMin:h.default.oneOfType([h.default.number,h.default.string]),autoHeightMax:h.default.oneOfType([h.default.number,h.default.string]),universal:h.default.bool,style:h.default.object,children:h.default.node},C.defaultProps={renderView:I.renderViewDefault,renderTrackHorizontal:I.renderTrackHorizontalDefault,renderTrackVertical:I.renderTrackVerticalDefault,renderThumbHorizontal:I.renderThumbHorizontalDefault,renderThumbVertical:I.renderThumbVerticalDefault,tagName:"div",thumbMinSize:30,hideTracksWhenNotNeeded:!1,autoHide:!1,autoHideTimeout:1e3,autoHideDuration:200,autoHeight:!1,autoHeightMin:0,autoHeightMax:200,universal:!1}}(ye)),ye}var Je;function Zt(){return Je||(Je=1,function(t){Object.defineProperty(t,"__esModule",{value:!0}),t.Scrollbars=void 0;var l=qt(),d=m(l);function m(c){return c&&c.__esModule?c:{default:c}}t.default=d.default,t.Scrollbars=d.default}(ve)),ve}var ct=Zt();const Gr=nt(ct);function Ut(t){return T({attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"}},{tag:"path",attr:{d:"M13.73 21a2 2 0 0 1-3.46 0"}}]})(t)}function Xr(t){return T({attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"polyline",attr:{points:"20 6 9 17 4 12"}}]})(t)}function Kr(t){return T({attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"polyline",attr:{points:"9 18 15 12 9 6"}}]})(t)}function Yt(t){return T({attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"circle",attr:{cx:"12",cy:"12",r:"10"}},{tag:"polygon",attr:{points:"16.24 7.76 14.12 14.12 7.76 16.24 9.88 9.88 16.24 7.76"}}]})(t)}function Jr(t){return T({attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"}},{tag:"polyline",attr:{points:"7 10 12 15 17 10"}},{tag:"line",attr:{x1:"12",y1:"15",x2:"12",y2:"3"}}]})(t)}function Qr(t){return T({attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"}},{tag:"path",attr:{d:"M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"}}]})(t)}function Gt(t){return T({attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"circle",attr:{cx:"12",cy:"12",r:"10"}},{tag:"line",attr:{x1:"2",y1:"12",x2:"22",y2:"12"}},{tag:"path",attr:{d:"M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"}}]})(t)}function ut(t){return T({attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"rect",attr:{x:"3",y:"3",width:"7",height:"7"}},{tag:"rect",attr:{x:"14",y:"3",width:"7",height:"7"}},{tag:"rect",attr:{x:"14",y:"14",width:"7",height:"7"}},{tag:"rect",attr:{x:"3",y:"14",width:"7",height:"7"}}]})(t)}function Xt(t){return T({attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"}},{tag:"polyline",attr:{points:"16 17 21 12 16 7"}},{tag:"line",attr:{x1:"21",y1:"12",x2:"9",y2:"12"}}]})(t)}function en(t){return T({attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"}},{tag:"polyline",attr:{points:"22,6 12,13 2,6"}}]})(t)}function Kt(t){return T({attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"line",attr:{x1:"3",y1:"12",x2:"21",y2:"12"}},{tag:"line",attr:{x1:"3",y1:"6",x2:"21",y2:"6"}},{tag:"line",attr:{x1:"3",y1:"18",x2:"21",y2:"18"}}]})(t)}function Jt(t){return T({attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"}}]})(t)}function tn(t){return T({attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"line",attr:{x1:"12",y1:"5",x2:"12",y2:"19"}},{tag:"line",attr:{x1:"5",y1:"12",x2:"19",y2:"12"}}]})(t)}function rn(t){return T({attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"polyline",attr:{points:"6 9 6 2 18 2 18 9"}},{tag:"path",attr:{d:"M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2"}},{tag:"rect",attr:{x:"6",y:"14",width:"12",height:"8"}}]})(t)}function nn(t){return T({attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"polyline",attr:{points:"23 4 23 10 17 10"}},{tag:"polyline",attr:{points:"1 20 1 14 7 14"}},{tag:"path",attr:{d:"M3.51 9a9 9 0 0 1 14.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0 0 20.49 15"}}]})(t)}function dt(t){return T({attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"circle",attr:{cx:"12",cy:"12",r:"3"}},{tag:"path",attr:{d:"M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"}}]})(t)}function an(t){return T({attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"circle",attr:{cx:"9",cy:"21",r:"1"}},{tag:"circle",attr:{cx:"20",cy:"21",r:"1"}},{tag:"path",attr:{d:"M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"}}]})(t)}function Qe(t){return T({attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M14.5 10c-.83 0-1.5-.67-1.5-1.5v-5c0-.83.67-1.5 1.5-1.5s1.5.67 1.5 1.5v5c0 .83-.67 1.5-1.5 1.5z"}},{tag:"path",attr:{d:"M20.5 10H19V8.5c0-.83.67-1.5 1.5-1.5s1.5.67 1.5 1.5-.67 1.5-1.5 1.5z"}},{tag:"path",attr:{d:"M9.5 14c.83 0 1.5.67 1.5 1.5v5c0 .83-.67 1.5-1.5 1.5S8 21.33 8 20.5v-5c0-.83.67-1.5 1.5-1.5z"}},{tag:"path",attr:{d:"M3.5 14H5v1.5c0 .83-.67 1.5-1.5 1.5S2 16.33 2 15.5 2.67 14 3.5 14z"}},{tag:"path",attr:{d:"M14 14.5c0-.83.67-1.5 1.5-1.5h5c.83 0 1.5.67 1.5 1.5s-.67 1.5-1.5 1.5h-5c-.83 0-1.5-.67-1.5-1.5z"}},{tag:"path",attr:{d:"M15.5 19H14v1.5c0 .83.67 1.5 1.5 1.5s1.5-.67 1.5-1.5-.67-1.5-1.5-1.5z"}},{tag:"path",attr:{d:"M10 9.5C10 8.67 9.33 8 8.5 8h-5C2.67 8 2 8.67 2 9.5S2.67 11 3.5 11h5c.83 0 1.5-.67 1.5-1.5z"}},{tag:"path",attr:{d:"M8.5 5H10V3.5C10 2.67 9.33 2 8.5 2S7 2.67 7 3.5 7.67 5 8.5 5z"}}]})(t)}function Qt(t){return T({attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"circle",attr:{cx:"12",cy:"12",r:"5"}},{tag:"line",attr:{x1:"12",y1:"1",x2:"12",y2:"3"}},{tag:"line",attr:{x1:"12",y1:"21",x2:"12",y2:"23"}},{tag:"line",attr:{x1:"4.22",y1:"4.22",x2:"5.64",y2:"5.64"}},{tag:"line",attr:{x1:"18.36",y1:"18.36",x2:"19.78",y2:"19.78"}},{tag:"line",attr:{x1:"1",y1:"12",x2:"3",y2:"12"}},{tag:"line",attr:{x1:"21",y1:"12",x2:"23",y2:"12"}},{tag:"line",attr:{x1:"4.22",y1:"19.78",x2:"5.64",y2:"18.36"}},{tag:"line",attr:{x1:"18.36",y1:"5.64",x2:"19.78",y2:"4.22"}}]})(t)}function er(t){return T({attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"circle",attr:{cx:"12",cy:"12",r:"10"}},{tag:"circle",attr:{cx:"12",cy:"12",r:"6"}},{tag:"circle",attr:{cx:"12",cy:"12",r:"2"}}]})(t)}function tr(t){return T({attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"polyline",attr:{points:"3 6 5 6 21 6"}},{tag:"path",attr:{d:"M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"}},{tag:"line",attr:{x1:"10",y1:"11",x2:"10",y2:"17"}},{tag:"line",attr:{x1:"14",y1:"11",x2:"14",y2:"17"}}]})(t)}function on(t){return T({attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"rect",attr:{x:"1",y:"3",width:"15",height:"13"}},{tag:"polygon",attr:{points:"16 8 20 8 23 11 23 16 16 16 16 8"}},{tag:"circle",attr:{cx:"5.5",cy:"18.5",r:"2.5"}},{tag:"circle",attr:{cx:"18.5",cy:"18.5",r:"2.5"}}]})(t)}function sn(t){return T({attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"polyline",attr:{points:"16 16 12 12 8 16"}},{tag:"line",attr:{x1:"12",y1:"12",x2:"12",y2:"21"}},{tag:"path",attr:{d:"M20.39 18.39A5 5 0 0 0 18 9h-1.26A8 8 0 1 0 3 16.3"}},{tag:"polyline",attr:{points:"16 16 12 12 8 16"}}]})(t)}function ln(t){return T({attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"}},{tag:"polyline",attr:{points:"17 8 12 3 7 8"}},{tag:"line",attr:{x1:"12",y1:"3",x2:"12",y2:"15"}}]})(t)}function rr(t){return T({attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"}},{tag:"circle",attr:{cx:"12",cy:"7",r:"4"}}]})(t)}function nr(t){return T({attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"}},{tag:"circle",attr:{cx:"9",cy:"7",r:"4"}},{tag:"path",attr:{d:"M23 21v-2a4 4 0 0 0-3-3.87"}},{tag:"path",attr:{d:"M16 3.13a4 4 0 0 1 0 7.75"}}]})(t)}function cn(t){return T({attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"circle",attr:{cx:"12",cy:"12",r:"10"}},{tag:"line",attr:{x1:"15",y1:"9",x2:"9",y2:"15"}},{tag:"line",attr:{x1:"9",y1:"9",x2:"15",y2:"15"}}]})(t)}function un(t){return T({attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"line",attr:{x1:"18",y1:"6",x2:"6",y2:"18"}},{tag:"line",attr:{x1:"6",y1:"6",x2:"18",y2:"18"}}]})(t)}function dn(t){return T({attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"circle",attr:{cx:"11",cy:"11",r:"8"}},{tag:"line",attr:{x1:"21",y1:"21",x2:"16.65",y2:"16.65"}},{tag:"line",attr:{x1:"11",y1:"8",x2:"11",y2:"14"}},{tag:"line",attr:{x1:"8",y1:"11",x2:"14",y2:"11"}}]})(t)}const ar="data:image/svg+xml,%3csvg%20viewBox='0%200%20512%20512'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M256%20464c-114.69%200-208-93.31-208-208S141.31%2048%20256%2048s208%2093.31%20208%20208-93.31%20208-208%20208Z'%20fill='%23148f45'%20class='fill-000000'%3e%3c/path%3e%3c/svg%3e",ir=()=>{Tt();const[t,l]=v.useState(null),[d,m]=v.useState(!1);return{socket:t,updated:d,setUpdated:m}};var fe={exports:{}},or=fe.exports,et;function sr(){return et||(et=1,function(t,l){(function(d,m){t.exports=m()})(or,function(){var d=1e3,m=6e4,c=36e5,b="millisecond",u="second",x="minute",k="hour",h="day",g="week",p="month",L="quarter",w="year",E="date",z="Invalid Date",_=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,U=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,ie={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(o){var e=["th","st","nd","rd"],r=o%100;return"["+o+(e[(r-20)%10]||e[r]||e[0])+"]"}},J=function(o,e,r){var i=String(o);return!i||i.length>=e?o:""+Array(e+1-i.length).join(r)+o},P={s:J,z:function(o){var e=-o.utcOffset(),r=Math.abs(e),i=Math.floor(r/60),s=r%60;return(e<=0?"+":"-")+J(i,2,"0")+":"+J(s,2,"0")},m:function o(e,r){if(e.date()<r.date())return-o(r,e);var i=12*(r.year()-e.year())+(r.month()-e.month()),s=e.clone().add(i,p),n=r-s<0,f=e.clone().add(i+(n?-1:1),p);return+(-(i+(r-s)/(n?s-f:f-s))||0)},a:function(o){return o<0?Math.ceil(o)||0:Math.floor(o)},p:function(o){return{M:p,y:w,w:g,d:h,D:E,h:k,m:x,s:u,ms:b,Q:L}[o]||String(o||"").toLowerCase().replace(/s$/,"")},u:function(o){return o===void 0}},I="en",$={};$[I]=ie;var ee="$isDayjsObject",te=function(o){return o instanceof M||!(!o||!o[ee])},Y=function o(e,r,i){var s;if(!e)return I;if(typeof e=="string"){var n=e.toLowerCase();$[n]&&(s=n),r&&($[n]=r,s=n);var f=e.split("-");if(!s&&f.length>1)return o(f[0])}else{var y=e.name;$[y]=e,s=y}return!i&&s&&(I=s),s||!i&&I},N=function(o,e){if(te(o))return o.clone();var r=typeof e=="object"?e:{};return r.date=o,r.args=arguments,new M(r)},C=P;C.l=Y,C.i=te,C.w=function(o,e){return N(o,{locale:e.$L,utc:e.$u,x:e.$x,$offset:e.$offset})};var M=function(){function o(r){this.$L=Y(r.locale,null,!0),this.parse(r),this.$x=this.$x||r.x||{},this[ee]=!0}var e=o.prototype;return e.parse=function(r){this.$d=function(i){var s=i.date,n=i.utc;if(s===null)return new Date(NaN);if(C.u(s))return new Date;if(s instanceof Date)return new Date(s);if(typeof s=="string"&&!/Z$/i.test(s)){var f=s.match(_);if(f){var y=f[2]-1||0,H=(f[7]||"0").substring(0,3);return n?new Date(Date.UTC(f[1],y,f[3]||1,f[4]||0,f[5]||0,f[6]||0,H)):new Date(f[1],y,f[3]||1,f[4]||0,f[5]||0,f[6]||0,H)}}return new Date(s)}(r),this.init()},e.init=function(){var r=this.$d;this.$y=r.getFullYear(),this.$M=r.getMonth(),this.$D=r.getDate(),this.$W=r.getDay(),this.$H=r.getHours(),this.$m=r.getMinutes(),this.$s=r.getSeconds(),this.$ms=r.getMilliseconds()},e.$utils=function(){return C},e.isValid=function(){return this.$d.toString()!==z},e.isSame=function(r,i){var s=N(r);return this.startOf(i)<=s&&s<=this.endOf(i)},e.isAfter=function(r,i){return N(r)<this.startOf(i)},e.isBefore=function(r,i){return this.endOf(i)<N(r)},e.$g=function(r,i,s){return C.u(r)?this[i]:this.set(s,r)},e.unix=function(){return Math.floor(this.valueOf()/1e3)},e.valueOf=function(){return this.$d.getTime()},e.startOf=function(r,i){var s=this,n=!!C.u(i)||i,f=C.p(r),y=function(A,F){var Z=C.w(s.$u?Date.UTC(s.$y,F,A):new Date(s.$y,F,A),s);return n?Z:Z.endOf(h)},H=function(A,F){return C.w(s.toDate()[A].apply(s.toDate("s"),(n?[0,0,0,0]:[23,59,59,999]).slice(F)),s)},j=this.$W,D=this.$M,O=this.$D,W="set"+(this.$u?"UTC":"");switch(f){case w:return n?y(1,0):y(31,11);case p:return n?y(1,D):y(0,D+1);case g:var R=this.$locale().weekStart||0,B=(j<R?j+7:j)-R;return y(n?O-B:O+(6-B),D);case h:case E:return H(W+"Hours",0);case k:return H(W+"Minutes",1);case x:return H(W+"Seconds",2);case u:return H(W+"Milliseconds",3);default:return this.clone()}},e.endOf=function(r){return this.startOf(r,!1)},e.$set=function(r,i){var s,n=C.p(r),f="set"+(this.$u?"UTC":""),y=(s={},s[h]=f+"Date",s[E]=f+"Date",s[p]=f+"Month",s[w]=f+"FullYear",s[k]=f+"Hours",s[x]=f+"Minutes",s[u]=f+"Seconds",s[b]=f+"Milliseconds",s)[n],H=n===h?this.$D+(i-this.$W):i;if(n===p||n===w){var j=this.clone().set(E,1);j.$d[y](H),j.init(),this.$d=j.set(E,Math.min(this.$D,j.daysInMonth())).$d}else y&&this.$d[y](H);return this.init(),this},e.set=function(r,i){return this.clone().$set(r,i)},e.get=function(r){return this[C.p(r)]()},e.add=function(r,i){var s,n=this;r=Number(r);var f=C.p(i),y=function(D){var O=N(n);return C.w(O.date(O.date()+Math.round(D*r)),n)};if(f===p)return this.set(p,this.$M+r);if(f===w)return this.set(w,this.$y+r);if(f===h)return y(1);if(f===g)return y(7);var H=(s={},s[x]=m,s[k]=c,s[u]=d,s)[f]||1,j=this.$d.getTime()+r*H;return C.w(j,this)},e.subtract=function(r,i){return this.add(-1*r,i)},e.format=function(r){var i=this,s=this.$locale();if(!this.isValid())return s.invalidDate||z;var n=r||"YYYY-MM-DDTHH:mm:ssZ",f=C.z(this),y=this.$H,H=this.$m,j=this.$M,D=s.weekdays,O=s.months,W=s.meridiem,R=function(F,Z,Q,G){return F&&(F[Z]||F(i,n))||Q[Z].slice(0,G)},B=function(F){return C.s(y%12||12,F,"0")},A=W||function(F,Z,Q){var G=F<12?"AM":"PM";return Q?G.toLowerCase():G};return n.replace(U,function(F,Z){return Z||function(Q){switch(Q){case"YY":return String(i.$y).slice(-2);case"YYYY":return C.s(i.$y,4,"0");case"M":return j+1;case"MM":return C.s(j+1,2,"0");case"MMM":return R(s.monthsShort,j,O,3);case"MMMM":return R(O,j);case"D":return i.$D;case"DD":return C.s(i.$D,2,"0");case"d":return String(i.$W);case"dd":return R(s.weekdaysMin,i.$W,D,2);case"ddd":return R(s.weekdaysShort,i.$W,D,3);case"dddd":return D[i.$W];case"H":return String(y);case"HH":return C.s(y,2,"0");case"h":return B(1);case"hh":return B(2);case"a":return A(y,H,!0);case"A":return A(y,H,!1);case"m":return String(H);case"mm":return C.s(H,2,"0");case"s":return String(i.$s);case"ss":return C.s(i.$s,2,"0");case"SSS":return C.s(i.$ms,3,"0");case"Z":return f}return null}(F)||f.replace(":","")})},e.utcOffset=function(){return 15*-Math.round(this.$d.getTimezoneOffset()/15)},e.diff=function(r,i,s){var n,f=this,y=C.p(i),H=N(r),j=(H.utcOffset()-this.utcOffset())*m,D=this-H,O=function(){return C.m(f,H)};switch(y){case w:n=O()/12;break;case p:n=O();break;case L:n=O()/3;break;case g:n=(D-j)/6048e5;break;case h:n=(D-j)/864e5;break;case k:n=D/c;break;case x:n=D/m;break;case u:n=D/d;break;default:n=D}return s?n:C.a(n)},e.daysInMonth=function(){return this.endOf(p).$D},e.$locale=function(){return $[this.$L]},e.locale=function(r,i){if(!r)return this.$L;var s=this.clone(),n=Y(r,i,!0);return n&&(s.$L=n),s},e.clone=function(){return C.w(this.$d,this)},e.toDate=function(){return new Date(this.valueOf())},e.toJSON=function(){return this.isValid()?this.toISOString():null},e.toISOString=function(){return this.$d.toISOString()},e.toString=function(){return this.$d.toUTCString()},o}(),S=M.prototype;return N.prototype=S,[["$ms",b],["$s",u],["$m",x],["$H",k],["$W",h],["$M",p],["$y",w],["$D",E]].forEach(function(o){S[o[1]]=function(e){return this.$g(e,o[0],o[1])}}),N.extend=function(o,e){return o.$i||(o(e,M,N),o.$i=!0),N},N.locale=Y,N.isDayjs=te,N.unix=function(o){return N(1e3*o)},N.en=$[I],N.Ls=$,N.p={},N})}(fe)),fe.exports}var lr=sr();const Me=nt(lr),ht=()=>{const{lang:t}=v.useContext(ge),{error:l,isLoading:d,data:m}=Oe({queryKey:["globalSetting"],queryFn:async()=>await yt.getGlobalSetting(),staleTime:1200*1e3,gcTime:1500*1e3}),{data:c,error:b,isLoading:u}=Oe({queryKey:["languages"],queryFn:async()=>await xt.getShowingLanguage(),staleTime:1200*1e3,gcTime:1500*1e3}),x=(_,U)=>Me(_).format(U),k=_=>Me(_).format(m?.default_date_format),h=_=>Me(_).format(`${m?.default_date_format}  h:mm A`),g=(_=0)=>Number(parseFloat(_||0).toFixed(2)),p=(_=0)=>parseFloat(_||0).toFixed(m?.floating_number||2),L=v.useMemo(()=>_=>!_||typeof _!="object"?"":_[t]??_[m?.default_language]??_.en??"",[t,m?.default_language]),w=_=>_!==void 0&&_,E=_=>_!==void 0?_:"!#",z=m?.default_currency||"$";return{error:l,loading:d,currency:z,getNumber:g,langError:b,langLoading:u,getNumberTwo:p,showTimeFormat:x,showDateFormat:k,showingImage:w,showingUrl:E,languages:c,globalSetting:m,showDateTimeFormat:h,showingTranslateValue:L}},cr=({title:t})=>a.jsx("div",{className:"flex justify-center item h-full items-center",children:a.jsxs("h2",{className:"text-lg md:text-xl text-center mt-2 font-medium font-serif text-gray-600",children:[t,a.jsx("span",{role:"img","aria-labelledby":"img",children:"😞"})]})}),ce={addNotification:async t=>ne.post("/notification/add",t),getAllNotification:async t=>ne.get(`/notification?page=${t}`),updateStatusNotification:async(t,l)=>ne.put(`/notification/${t}`,l),updateManyStatusNotification:async t=>ne.patch("/notification/update/many",t),deleteNotification:async t=>ne.delete(`/notification/${t}`),deleteNotificationByProductId:async t=>ne.delete(`/notification/product-id/${t}`),deleteManyNotification:async t=>ne.patch("/notification/delete/many",t)},ur=({handleLanguageChange:t})=>{const{languages:l,langError:d,langLoading:m}=ht();return a.jsx("ul",{className:"dropdown-content w-full",children:!d&&!m&&l?.map(c=>a.jsxs("li",{className:"cursor-pointer flex items-center space-x-2 p-2 hover:bg-gray-100 rounded-md",onClick:()=>t(c),children:[a.jsx("div",{className:"flag bg-start",style:{backgroundImage:`url(https://flagcdn.com/w20/${c.flag.toLowerCase()}.png)`}}),a.jsx("span",{className:"text-gray-900 dark:text-gray-600 pr-8 text-right",children:c?.name})]},c._id))})},dr=()=>{const{toggleSidebar:t,handleLanguageChange:l,setNavBar:d,navBar:m,currLang:c}=v.useContext(ge),{state:b,dispatch:u}=v.useContext(De),{adminInfo:x}=b,{mode:k,toggleMode:h}=v.useContext(K.WindmillContext),g=v.useRef(),p=v.useRef();je.get("i18next");const{t:L}=Ne(),{updated:w,setUpdated:E}=ir(),{showDateTimeFormat:z}=ht(),[_,U]=v.useState([]),[ie,J]=v.useState(0),[P,I]=v.useState(0),[$,ee]=v.useState(!1),[te,Y]=v.useState(!1),N=()=>{u({type:"USER_LOGOUT"}),je.remove("adminInfo"),window.location.replace("https://dashtar-admin.netlify.app/login/login")},C=async()=>{Y(!te),ee(!1),await e()},M=()=>{ee(!$),Y(!1)},S=async r=>{try{await ce.updateStatusNotification(r,{status:"read"});const i=await ce.getAllNotification();U(i?.notifications),I(i?.totalUnreadDoc),window.location.reload(!1)}catch(i){notifyError(i?.response?.data?.message||i?.message)}},o=async r=>{try{await ce.deleteNotification(r);const i=await ce.getAllNotification();U(i?.notifications),I(i?.totalUnreadDoc),J(i?.totalDoc)}catch(i){notifyError(i?.response?.data?.message||i?.message)}},e=async()=>{try{const r=await ce.getAllNotification();U(r?.notifications),I(r?.totalUnreadDoc),J(r?.totalDoc),E(!1)}catch(r){E(!1),notifyError(r?.response?.data?.message||r?.message)}};return v.useEffect(()=>{const r=i=>{g?.current?.contains(i.target)||ee(!1),p?.current?.contains(i.target)||Y(!1)};document.addEventListener("mousedown",r)},[g,p]),v.useEffect(()=>{e()},[w]),a.jsx(a.Fragment,{children:a.jsx("header",{className:"z-30 py-4 bg-white shadow-sm dark:bg-gray-800",children:a.jsxs("div",{className:"container flex items-center justify-between h-full px-6 mx-auto text-emerald-500 dark:text-emerald-500",children:[a.jsx("button",{type:"button",onClick:()=>d(!m),className:"hidden lg:block outline-0 focus:outline-none",children:a.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 18 18",xmlns:"http://www.w3.org/2000/svg",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M4 6h16M4 12h16M4 18h16"})})}),a.jsx("button",{className:"p-1 mr-5 -ml-1 rounded-md lg:hidden focus:outline-none",onClick:t,"aria-label":"Menu",children:a.jsx(Kt,{className:"w-6 h-6","aria-hidden":"true"})}),a.jsx("span",{}),a.jsxs("ul",{className:"flex justify-end items-center flex-shrink-0 space-x-6",children:[a.jsx("li",{className:"changeLanguage",children:a.jsxs("div",{className:"dropdown",children:[a.jsxs("button",{className:"dropbtn focus:outline-none flex",children:[a.jsx("div",{className:`text-sm flag ${c?.flag?.toLowerCase()}`})," ",a.jsx("span",{className:"md:inline-block hidden text-gray-900 dark:text-gray-300",children:c?.name}),a.jsx("span",{className:"md:hidden uppercase",children:c?.iso_code})]}),a.jsx(ur,{handleLanguageChange:l})]})}),a.jsx("li",{className:"flex",children:a.jsx("button",{className:"rounded-md focus:outline-none",onClick:h,"aria-label":"Toggle color mode",children:k==="dark"?a.jsx(Qt,{className:"w-5 h-5","aria-hidden":"true"}):a.jsx(Jt,{className:"w-5 h-5","aria-hidden":"true"})})}),a.jsxs("li",{className:"relative inline-block text-left",ref:p,children:[a.jsxs("button",{className:"relative align-middle rounded-md focus:outline-none",onClick:C,children:[a.jsx(Ut,{className:"w-5 h-5 text-emerald-500","aria-hidden":"true"}),a.jsx("span",{className:"absolute z-10 top-0 right-0 inline-flex items-center justify-center p-1 h-5 w-5 text-xs font-medium leading-none text-red-100 transform -translate-x-1/2 -translate-y-1/2 bg-red-500 rounded-full",children:P})]}),te&&a.jsx("div",{className:"origin-top-right absolute md:right-0 -right-3 top-2 rounded-md shadow-lg bg-white dark:bg-gray-800  focus:outline-none",children:a.jsx("div",{className:`${_?.length===0||_?.length<=2?"h-40":_?.length<=3?"h-56":"h-330"} md:w-400 w-300`,children:a.jsxs(ct.Scrollbars,{children:[_?.length===0?a.jsx(cr,{title:"No new notification"}):a.jsx("ul",{className:"block text-sm border-t border-gray-100 dark:border-gray-700 rounded-md",children:_?.map((r,i)=>a.jsxs("li",{className:`flex justify-between items-center font-serif font-normal text-sm py-3 border-b border-gray-100 dark:border-gray-700 px-3 transition-colors duration-150 hover:bg-gray-100 ${r.status==="unread"&&"bg-gray-50"} hover:text-gray-800 dark:text-gray-400 ${r.status==="unread"&&"dark:bg-gray-800"} dark:hover:bg-gray-900  dark:hover:text-gray-100 cursor-pointer`,children:[a.jsxs(he,{to:r.productId?`/product/${r.productId}`:r.orderId?`/order/${r.orderId}`:"/our-staff",className:"flex items-center",onClick:()=>S(r._id),children:[a.jsx(K.Avatar,{className:"mr-2 md:block bg-gray-50 border border-gray-200",src:r.image,alt:"image"}),a.jsxs("div",{className:"notification-content",children:[a.jsx("h6",{className:"font-medium text-gray-500",children:r?.message}),a.jsxs("p",{className:"flex items-center text-xs text-gray-400",children:[r.productId?a.jsx(K.Badge,{type:"danger",children:"Stock Out"}):a.jsx(K.Badge,{type:"success",children:"New Order"}),a.jsx("span",{className:"ml-2",children:z(r.createdAt)})]})]}),r.status==="unread"&&a.jsx("span",{className:"px-2 focus:outline-none",children:a.jsx("img",{src:ar,width:12,height:12,alt:"ellipse",className:"w-3 h-3 text-emerald-600"})})]}),a.jsxs("div",{className:"group inline-block relative",children:[a.jsx("button",{type:"button",onClick:()=>o(r._id),className:"px-2 group-hover:text-blue-500 text-red-500 focus:outline-none",children:a.jsx(tr,{})}),a.jsx("div",{className:"absolute hidden group-hover:inline-block bg-gray-50 dark:text-red-400 mr-6 mb-1 right-0 z-50 px-3 py-2 text-sm font-medium text-red-600 rounded-lg shadow-sm tooltip dark:bg-gray-700",children:"Delete"})]})]},i+1))}),ie>5&&a.jsx("div",{className:"text-center py-2",children:a.jsx(he,{onClick:()=>Y(!1),to:"/notifications",className:"focus:outline-none hover:underline transition ease-out duration-200",children:"Show all notifications"})})]})})})]}),a.jsxs("li",{className:"relative inline-block text-left",ref:g,children:[a.jsx("button",{className:"rounded-full dark:bg-gray-500 bg-emerald-500 text-white h-8 w-8 font-medium mx-auto focus:outline-none",onClick:M,children:x.image?a.jsx(K.Avatar,{className:"align-middle",src:`${x.image}`,"aria-hidden":"true"}):a.jsx("span",{children:x.email[0].toUpperCase()})}),$&&a.jsxs("ul",{className:"origin-top-right absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-white dark:bg-gray-800 focus:outline-none",children:[a.jsx("li",{className:"justify-between font-serif font-medium py-2 pl-4 transition-colors duration-150 hover:bg-gray-100 text-gray-500 hover:text-emerald-500 dark:text-gray-400 dark:hover:bg-gray-800 dark:hover:text-gray-200",children:a.jsx(he,{to:"/dashboard",children:a.jsxs("span",{className:"flex items-center text-sm",children:[a.jsx(ut,{className:"w-4 h-4 mr-3","aria-hidden":"true"}),a.jsx("span",{children:L("Dashboard")})]})})}),a.jsx("li",{className:"justify-between font-serif font-medium py-2 pl-4 transition-colors duration-150 hover:bg-gray-100 text-gray-500 hover:text-emerald-500 dark:text-gray-400 dark:hover:bg-gray-800 dark:hover:text-gray-200",children:a.jsx(he,{to:"/edit-profile",children:a.jsxs("span",{className:"flex items-center text-sm",children:[a.jsx(dt,{className:"w-4 h-4 mr-3","aria-hidden":"true"}),a.jsx("span",{children:L("EditProfile")})]})})}),a.jsx("li",{onClick:N,className:"cursor-pointer justify-between font-serif font-medium py-2 pl-4 transition-colors duration-150 hover:bg-gray-100 text-gray-500 hover:text-emerald-500 dark:text-gray-400 dark:hover:bg-gray-800 dark:hover:text-gray-200",children:a.jsxs("span",{className:"flex items-center text-sm",children:[a.jsx(Xt,{className:"w-4 h-4 mr-3","aria-hidden":"true"}),a.jsx("span",{children:L("LogOut")})]})})]})]})]})]})})})};function hn(t){return T({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M454.65 169.4A31.82 31.82 0 00432 160h-64v-16a112 112 0 00-224 0v16H80a32 32 0 00-32 32v216c0 39 33 72 72 72h272a72.22 72.22 0 0050.48-20.55 69.48 69.48 0 0021.52-50.2V192a31.75 31.75 0 00-9.35-22.6zM176 144a80 80 0 01160 0v16H176zm192 96a112 112 0 01-224 0v-16a16 16 0 0132 0v16a80 80 0 00160 0v-16a16 16 0 0132 0z"}}]})(t)}function hr(t){return T({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{fill:"none",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"48",d:"M112 184l144 144 144-144"}}]})(t)}function fr(t){return T({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{fill:"none",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"48",d:"M184 112l144 144-144 144"}}]})(t)}function fn(t){return T({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{fill:"none",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"32",d:"M320 336h76c55 0 100-21.21 100-75.6s-53-73.47-96-75.6C391.11 99.74 329 48 256 48c-69 0-113.44 45.79-128 91.2-60 5.7-112 35.88-112 98.4S70 336 136 336h56m0 64.1l64 63.9 64-63.9M256 224v224.03"}}]})(t)}function gr(t){return T({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{fill:"none",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"32",d:"M304 336v40a40 40 0 01-40 40H104a40 40 0 01-40-40V136a40 40 0 0140-40h152c22.09 0 48 17.91 48 40v40m64 160l80-80-80-80m-192 80h256"}}]})(t)}function tt(t){return T({attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{fill:"none",strokeLinecap:"square",strokeLinejoin:"round",strokeWidth:"32",d:"M400 256H112"}}]})(t)}const mr=[{path:"/dashboard",icon:ut,name:"Dashboard"},{icon:Qe,name:"Catalog",routes:[{path:"/products",name:"Products"},{path:"/categories",name:"Categories"},{path:"/attributes",name:"Attributes"},{path:"/coupons",name:"Coupons"}]},{path:"/customers",icon:nr,name:"Customers"},{path:"/orders",icon:Yt,name:"Orders"},{path:"/our-staff",icon:rr,name:"OurStaff"},{path:"/settings?settingTab=common-settings",icon:dt,name:"Settings"},{icon:Gt,name:"International",routes:[{path:"/languages",name:"Languages"},{path:"/currencies",name:"Currencies"}]},{icon:er,name:"OnlineStore",routes:[{name:"ViewStore",path:"/store",outside:"store"},{path:"/store/customization",name:"StoreCustomization"},{path:"/store/store-settings",name:"StoreSettings"}]},{icon:Qe,name:"Pages",routes:[{path:"/404",name:"404"},{path:"/coming-soon",name:"Coming Soon"}]}],pr="data:image/svg+xml,%3csvg%20width='87'%20height='25'%20viewBox='0%200%2087%2025'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M26.2173%2024H29.1612C30.8267%2024%2031.6435%2023.1513%2031.6435%2022.0185C31.6435%2020.9176%2030.8622%2020.2713%2030.0881%2020.2322V20.1612C30.7983%2019.9943%2031.3594%2019.4972%2031.3594%2018.6129C31.3594%2017.5298%2030.5781%2016.7273%2029.0014%2016.7273H26.2173V24ZM27.5348%2022.8991V20.7685H28.9872C29.8004%2020.7685%2030.3047%2021.2656%2030.3047%2021.9155C30.3047%2022.4943%2029.907%2022.8991%2028.9517%2022.8991H27.5348ZM27.5348%2019.8203V17.8139H28.8665C29.6406%2017.8139%2030.0419%2018.2223%2030.0419%2018.7834C30.0419%2019.4226%2029.5234%2019.8203%2028.8381%2019.8203H27.5348ZM40.4235%2024L41.0237%2022.2067H43.7581L44.3618%2024H45.768L43.2041%2016.7273H41.5777L39.0173%2024H40.4235ZM41.3788%2021.1484L42.3625%2018.2188H42.4193L43.4029%2021.1484H41.3788ZM53.291%2024H58.6745V22.8956H54.9281L58.6532%2017.5476V16.7273H53.2697V17.8317H57.0197L53.291%2023.1797V24ZM67.5833%2024L68.1835%2022.2067H70.9178L71.5215%2024H72.9278L70.3638%2016.7273H68.7374L66.1771%2024H67.5833ZM68.5386%2021.1484L69.5222%2018.2188H69.579L70.5627%2021.1484H68.5386ZM80.5857%2024H81.9032V21.3366H83.2455L84.673%2024H86.1432L84.5701%2021.1165C85.4259%2020.772%2085.8769%2020.0405%2085.8769%2019.0533C85.8769%2017.6648%2084.982%2016.7273%2083.313%2016.7273H80.5857V24ZM81.9032%2020.2429V17.8281H83.1105C84.0942%2017.8281%2084.5346%2018.2791%2084.5346%2019.0533C84.5346%2019.8274%2084.0942%2020.2429%2083.1176%2020.2429H81.9032Z'%20fill='%23334155'/%3e%3cpath%20d='M26.3807%2014H29.1932V10.8466L30.3182%209.42614L33.2159%2014H36.5795L32.3807%207.55682L36.5057%202.36364H33.1932L29.3466%207.28409H29.1932V2.36364H26.3807V14ZM40.1321%2014L40.8991%2011.5568H44.9162L45.6832%2014H48.706L44.7798%202.36364H41.0355L37.1094%2014H40.1321ZM41.5696%209.42045L42.8594%205.29545H42.9503L44.2457%209.42045H41.5696ZM59.9744%206.57955C59.6903%203.79545%2057.6165%202.20455%2054.804%202.20455C51.7017%202.20455%2049.2869%204.33523%2049.2869%208.18182C49.2869%2012.0114%2051.6449%2014.1591%2054.804%2014.1591C57.9972%2014.1591%2059.7642%2011.9886%2059.9744%209.92614L57.1335%209.90909C56.9347%2011.0284%2056.0938%2011.6932%2054.8608%2011.6932C53.2017%2011.6932%2052.1562%2010.4943%2052.1562%208.18182C52.1562%205.96023%2053.179%204.67045%2054.8778%204.67045C56.1562%204.67045%2056.9915%205.41477%2057.1335%206.57955H59.9744ZM61.5838%2014H64.3963V9.32386H68.9815V14H71.7884V2.36364H68.9815V7.03409H64.3963V2.36364H61.5838V14ZM76.0696%2014L76.8366%2011.5568H80.8537L81.6207%2014H84.6435L80.7173%202.36364H76.973L73.0469%2014H76.0696ZM77.5071%209.42045L78.7969%205.29545H78.8878L80.1832%209.42045H77.5071Z'%20fill='%23334155'/%3e%3cg%20clip-path='url(%23clip0)'%3e%3cpath%20d='M15.9108%208.23217V6.78574C15.9108%203.59545%2013.3153%201%2010.125%201C6.93475%201%204.33931%203.59545%204.33931%206.78574V8.23217H0V20.5269C0%2022.524%201.61897%2024.143%203.61609%2024.143H16.634C18.6311%2024.143%2020.2501%2022.524%2020.2501%2020.5269V8.23217H15.9108ZM7.23218%206.78574C7.23218%205.19059%208.5299%203.89287%2010.125%203.89287C11.7202%203.89287%2013.0179%205.19059%2013.0179%206.78574V8.23217H7.23218V6.78574ZM14.4644%2012.2099C13.8652%2012.2099%2013.3795%2011.7242%2013.3795%2011.125C13.3795%2010.5259%2013.8652%2010.0402%2014.4644%2010.0402C15.0635%2010.0402%2015.5492%2010.5259%2015.5492%2011.125C15.5492%2011.7242%2015.0635%2012.2099%2014.4644%2012.2099ZM5.78574%2012.2099C5.1866%2012.2099%204.70091%2011.7242%204.70091%2011.125C4.70091%2010.5259%205.1866%2010.0402%205.78574%2010.0402C6.38488%2010.0402%206.87057%2010.5259%206.87057%2011.125C6.87057%2011.7242%206.38488%2012.2099%205.78574%2012.2099Z'%20fill='%2310B981'/%3e%3c/g%3e%3cdefs%3e%3cclipPath%20id='clip0'%3e%3crect%20width='20.2501'%20height='23.143'%20fill='white'%20transform='translate(0%201)'/%3e%3c/clipPath%3e%3c/defs%3e%3c/svg%3e",vr="data:image/svg+xml,%3csvg%20width='87'%20height='25'%20viewBox='0%200%2087%2025'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M26.2173%2024H29.1612C30.8267%2024%2031.6435%2023.1513%2031.6435%2022.0185C31.6435%2020.9176%2030.8622%2020.2713%2030.0881%2020.2322V20.1612C30.7983%2019.9943%2031.3594%2019.4972%2031.3594%2018.6129C31.3594%2017.5298%2030.5781%2016.7273%2029.0014%2016.7273H26.2173V24ZM27.5348%2022.8991V20.7685H28.9872C29.8004%2020.7685%2030.3047%2021.2656%2030.3047%2021.9155C30.3047%2022.4943%2029.907%2022.8991%2028.9517%2022.8991H27.5348ZM27.5348%2019.8203V17.8139H28.8665C29.6406%2017.8139%2030.0419%2018.2223%2030.0419%2018.7834C30.0419%2019.4226%2029.5234%2019.8203%2028.8381%2019.8203H27.5348ZM40.4235%2024L41.0237%2022.2067H43.7581L44.3618%2024H45.768L43.2041%2016.7273H41.5777L39.0173%2024H40.4235ZM41.3788%2021.1484L42.3625%2018.2188H42.4193L43.4029%2021.1484H41.3788ZM53.291%2024H58.6745V22.8956H54.9281L58.6532%2017.5476V16.7273H53.2697V17.8317H57.0197L53.291%2023.1797V24ZM67.5833%2024L68.1835%2022.2067H70.9178L71.5215%2024H72.9278L70.3638%2016.7273H68.7374L66.1771%2024H67.5833ZM68.5386%2021.1484L69.5222%2018.2188H69.579L70.5627%2021.1484H68.5386ZM80.5857%2024H81.9032V21.3366H83.2455L84.673%2024H86.1432L84.5701%2021.1165C85.4259%2020.772%2085.8769%2020.0405%2085.8769%2019.0533C85.8769%2017.6648%2084.982%2016.7273%2083.313%2016.7273H80.5857V24ZM81.9032%2020.2429V17.8281H83.1105C84.0942%2017.8281%2084.5346%2018.2791%2084.5346%2019.0533C84.5346%2019.8274%2084.0942%2020.2429%2083.1176%2020.2429H81.9032Z'%20fill='%23575757'/%3e%3cpath%20d='M26.3807%2014H29.1932V10.8466L30.3182%209.42614L33.2159%2014H36.5795L32.3807%207.55682L36.5057%202.36364H33.1932L29.3466%207.28409H29.1932V2.36364H26.3807V14ZM40.1321%2014L40.8991%2011.5568H44.9162L45.6832%2014H48.706L44.7798%202.36364H41.0355L37.1094%2014H40.1321ZM41.5696%209.42045L42.8594%205.29545H42.9503L44.2457%209.42045H41.5696ZM59.9744%206.57955C59.6903%203.79545%2057.6165%202.20455%2054.804%202.20455C51.7017%202.20455%2049.2869%204.33523%2049.2869%208.18182C49.2869%2012.0114%2051.6449%2014.1591%2054.804%2014.1591C57.9972%2014.1591%2059.7642%2011.9886%2059.9744%209.92614L57.1335%209.90909C56.9347%2011.0284%2056.0938%2011.6932%2054.8608%2011.6932C53.2017%2011.6932%2052.1562%2010.4943%2052.1562%208.18182C52.1562%205.96023%2053.179%204.67045%2054.8778%204.67045C56.1562%204.67045%2056.9915%205.41477%2057.1335%206.57955H59.9744ZM61.5838%2014H64.3963V9.32386H68.9815V14H71.7884V2.36364H68.9815V7.03409H64.3963V2.36364H61.5838V14ZM76.0696%2014L76.8366%2011.5568H80.8537L81.6207%2014H84.6435L80.7173%202.36364H76.973L73.0469%2014H76.0696ZM77.5071%209.42045L78.7969%205.29545H78.8878L80.1832%209.42045H77.5071Z'%20fill='%23575757'/%3e%3cg%20clip-path='url(%23clip0)'%3e%3cpath%20d='M15.9108%208.23217V6.78574C15.9108%203.59545%2013.3153%201%2010.125%201C6.93475%201%204.33931%203.59545%204.33931%206.78574V8.23217H0V20.5269C0%2022.524%201.61897%2024.143%203.61609%2024.143H16.634C18.6311%2024.143%2020.2501%2022.524%2020.2501%2020.5269V8.23217H15.9108ZM7.23218%206.78574C7.23218%205.19059%208.5299%203.89287%2010.125%203.89287C11.7202%203.89287%2013.0179%205.19059%2013.0179%206.78574V8.23217H7.23218V6.78574ZM14.4644%2012.2099C13.8652%2012.2099%2013.3795%2011.7242%2013.3795%2011.125C13.3795%2010.5259%2013.8652%2010.0402%2014.4644%2010.0402C15.0635%2010.0402%2015.5492%2010.5259%2015.5492%2011.125C15.5492%2011.7242%2015.0635%2012.2099%2014.4644%2012.2099ZM5.78574%2012.2099C5.1866%2012.2099%204.70091%2011.7242%204.70091%2011.125C4.70091%2010.5259%205.1866%2010.0402%205.78574%2010.0402C6.38488%2010.0402%206.87057%2010.5259%206.87057%2011.125C6.87057%2011.7242%206.38488%2012.2099%205.78574%2012.2099Z'%20fill='%23575757'/%3e%3c/g%3e%3cdefs%3e%3cclipPath%20id='clip0'%3e%3crect%20width='20.2501'%20height='23.143'%20fill='white'%20transform='translate(0%201)'/%3e%3c/clipPath%3e%3c/defs%3e%3c/svg%3e",yr=({route:t})=>{const{t:l}=Ne(),[d,m]=v.useState(!1);return a.jsx(a.Fragment,{children:a.jsxs("li",{className:"relative px-6 py-3",children:[a.jsx("button",{className:"inline-flex items-center justify-between focus:outline-none w-full text-sm font-semibold transition-colors duration-150 hover:text-emerald-600 dark:hover:text-gray-200",onClick:()=>m(!d),"aria-haspopup":"true",children:a.jsxs("span",{className:"inline-flex items-center",children:[a.jsx(t.icon,{className:"w-5 h-5","aria-hidden":"true"}),a.jsx("span",{className:"ml-4 mt-1",children:l(`${t.name}`)}),a.jsx("span",{className:"pl-4 mt-1",children:d?a.jsx(hr,{}):a.jsx(fr,{})})]})}),d&&a.jsx("ul",{className:"p-2  overflow-hidden text-sm font-medium text-gray-500 rounded-md dark:text-gray-400 dark:bg-gray-900","aria-label":"submenu",children:t.routes.map((c,b)=>a.jsx("li",{children:c?.outside?a.jsxs("a",{href:"http://localhost:3000",target:"_blank",className:"flex items-center font-serif py-1 text-sm text-gray-600 hover:text-emerald-600 cursor-pointer",rel:"noreferrer",children:[a.jsx(de,{path:c.path,exact:c.exact,children:a.jsx("span",{className:"absolute inset-y-0 left-0 w-1 bg-emerald-500 rounded-tr-lg rounded-br-lg","aria-hidden":"true"})}),a.jsx("span",{className:"text-xs text-gray-500 pr-1",children:a.jsx(tt,{})}),a.jsx("span",{className:"text-gray-500 hover:text-emerald-600 dark:hover:text-gray-200",children:l(`${c.name}`)})]}):a.jsxs(at,{to:c.path,className:"flex items-center font-serif py-1 text-sm text-gray-600 hover:text-emerald-600 cursor-pointer",rel:"noreferrer",children:[a.jsx(de,{path:c.path,exact:t.exact,children:a.jsx("span",{className:"absolute inset-y-0 left-0 w-1 bg-emerald-600 rounded-tr-lg rounded-br-lg","aria-hidden":"true"})}),a.jsx("span",{className:"text-xs text-gray-500 pr-1",children:a.jsx(tt,{})}),a.jsx("span",{className:"text-gray-500 hover:text-emerald-600 dark:hover:text-gray-200",children:l(`${c.name}`)})]})},b+1))})]},t.name)})},ft=()=>{const{t}=Ne(),{mode:l}=v.useContext(K.WindmillContext),{dispatch:d}=v.useContext(De),{accessList:m}=st(),c=()=>{d({type:"USER_LOGOUT"}),je.remove("adminInfo")},b=mr.map(u=>{if(u.routes){const k=u.routes.filter(h=>{const g=h.path.split("?")[0].split("/")[1];return m.includes(g)});return k.length>0?{...u,routes:k}:null}const x=u.path?.split("?")[0].split("/")[1];return x&&m.includes(x)?u:null}).filter(Boolean);return a.jsxs("div",{className:"py-4 text-gray-500 dark:text-gray-400",children:[a.jsx("a",{className:" text-gray-900 dark:text-gray-200",href:"/dashboard",children:l==="dark"?a.jsx("img",{src:vr,alt:"gloopi",width:"135",className:"pl-6"}):a.jsx("img",{src:pr,alt:"gloopi",width:"135",className:"pl-6"})}),a.jsx("ul",{className:"mt-8",children:b?.map(u=>u.routes?a.jsx(yr,{route:u},u.name):a.jsx("li",{className:"relative",children:a.jsxs(at,{exact:!0,to:u.path,target:`${u?.outside?"_blank":"_self"}`,className:"px-6 py-4 inline-flex items-center w-full text-sm font-semibold transition-colors duration-150 hover:text-emerald-700 dark:hover:text-gray-200",activeStyle:{color:"#0d9e6d"},rel:"noreferrer",children:[a.jsx(de,{path:u.path,exact:u.exact,children:a.jsx("span",{className:"absolute inset-y-0 left-0 w-1 bg-emerald-500 rounded-tr-lg rounded-br-lg","aria-hidden":"true"})}),a.jsx(u.icon,{className:"w-5 h-5","aria-hidden":"true"}),a.jsx("span",{className:"ml-4",children:t(`${u.name}`)})]})},u.name))}),a.jsx("span",{className:"lg:fixed bottom-0 px-6 py-6 w-64 mx-auto relative mt-3 block",children:a.jsx(K.Button,{onClick:c,size:"large",className:"w-full",children:a.jsxs("span",{className:"flex items-center",children:[a.jsx(gr,{className:"mr-3 text-lg"}),a.jsx("span",{className:"text-sm",children:t("LogOut")})]})})})]})},xr=()=>a.jsx("aside",{className:"z-30 flex-shrink-0 hidden shadow-sm w-64 overflow-y-auto bg-white dark:bg-gray-800 lg:block",children:a.jsx(ft,{})});function kr(){const{isSidebarOpen:t,closeSidebar:l}=v.useContext(ge);return a.jsx(K.Transition,{show:t,children:a.jsxs(a.Fragment,{children:[a.jsx(K.Transition,{enter:"transition ease-in-out duration-150",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"transition ease-in-out duration-150",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:a.jsx(K.Backdrop,{onClick:l})}),a.jsx(K.Transition,{enter:"transition ease-in-out duration-150",enterFrom:"opacity-0 transform -translate-x-20",enterTo:"opacity-100",leave:"transition ease-in-out duration-150",leaveFrom:"opacity-100",leaveTo:"opacity-0 transform -translate-x-20",children:a.jsx("aside",{className:"fixed inset-y-0 z-50 flex-shrink-0 w-64 mt-16 overflow-y-auto bg-white dark:bg-gray-800 lg:hidden",children:a.jsx(ft,{})})})]})})}const br=()=>a.jsxs(a.Fragment,{children:[a.jsx(xr,{}),a.jsx(kr,{})]}),wr=v.lazy(()=>V(()=>import("./Dashboard-bmOwL-ir.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21]))),Sr=v.lazy(()=>V(()=>import("./Attributes-fusadkhi.js"),__vite__mapDeps([22,1,15,23,24,25,26,27,3,4,7,8,6,5,28,29,30,11,31,32,33,34,35,36,37,38,10,12,19,20,2,21]))),Hr=v.lazy(()=>V(()=>import("./ChildAttributes-DnqoCjdK.js"),__vite__mapDeps([39,1,23,24,25,28,29,3,4,7,8,6,30,11,31,32,33,5,26,27,15,34,35,36,40,19,20,2,21,10]))),Lr=v.lazy(()=>V(()=>import("./Products-KGwJMDVZ.js"),__vite__mapDeps([41,1,11,23,24,25,37,38,10,3,4,19,20,42,30,6,43,31,34,32,44,36,45,46,15,29,28,7,8,33,5,26,27,12,21]))),Tr=v.lazy(()=>V(()=>import("./ProductDetails-DaFthmiY.js"),__vite__mapDeps([47,1,11,2,3,4,5,6,7,8,42,30,23,24,25,43,31,34,32,44,36,45,46,15,29,40,20,10]))),_r=v.lazy(()=>V(()=>import("./Category-CV1YsYME.js"),__vite__mapDeps([48,1,11,3,4,23,24,25,2,5,6,7,8,26,27,15,28,29,30,31,32,33,20,49,34,45,46,36,37,38,10,12,19,21]))),Cr=v.lazy(()=>V(()=>import("./ChildCategory-X9NjJqvR.js"),__vite__mapDeps([50,1,49,28,29,3,4,7,8,6,30,23,24,25,11,31,32,33,5,26,27,15,34,45,46,36,40,19,20,2,21,10]))),Mr=v.lazy(()=>V(()=>import("./Staff-EU7FJ_LS.js"),__vite__mapDeps([51,1,11,2,3,4,5,6,7,8,23,24,25,43,31,34,52,27,36,53,32,45,46,12,14,15,26,29,19,20,21,10]))),jr=v.lazy(()=>V(()=>import("./Customers-CDSaZG3L.js"),__vite__mapDeps([54,1,37,38,10,25,3,4,23,24,26,27,7,8,6,5,15,31,34,32,12,19,20,11,2,21]))),Dr=v.lazy(()=>V(()=>import("./CustomerOrder-DTywdyNw.js"),__vite__mapDeps([55,1,11,17,2,3,4,5,6,7,8,20,40,14,18,10]))),Vr=v.lazy(()=>V(()=>import("./Orders-C4vZ_Guq.js"),__vite__mapDeps([56,1,38,6,11,2,3,4,5,7,8,17,19,20,13,14,15,16,18,12,25,21,10]))),Nr=v.lazy(()=>V(()=>import("./OrderInvoice-DE8Ez2u_.js"),__vite__mapDeps([57,1,16,46,4,58,11,6,14,17,40,20,25,5,10]))),zr=v.lazy(()=>V(()=>import("./Coupons-UHvQwB1A.js"),__vite__mapDeps([59,1,7,11,23,24,25,2,3,4,5,6,8,20,26,27,15,28,29,30,31,32,33,34,44,45,46,36,12,19,37,38,10,21]))),Or=v.lazy(()=>V(()=>import("./404-CE5DCzqU.js"),__vite__mapDeps([60,1,10]))),Er=v.lazy(()=>V(()=>import("./ComingSoon-BWDZjz5t.js"),__vite__mapDeps([61,1]))),$r=v.lazy(()=>V(()=>import("./EditProfile-VgWWB-Nm.js"),__vite__mapDeps([62,1,52,31,27,6,36,58,32,45,46,4,34,53,21,10]))),Wr=v.lazy(()=>V(()=>import("./Languages-DayBgvtb.js"),__vite__mapDeps([63,1,28,29,3,4,7,8,6,30,23,24,25,11,31,32,33,5,26,27,15,34,12,19,2,20,21,10]))),Rr=v.lazy(()=>V(()=>import("./Currencies-BZXFJAfc.js"),__vite__mapDeps([64,1,28,29,3,4,7,8,6,30,23,24,25,11,31,32,33,5,34,26,27,15,20,2,12,19,21,10]))),Fr=v.lazy(()=>V(()=>import("./Setting-DEaW96v2.js"),__vite__mapDeps([65,1,31,20,5,6,34,66,21,33,29,67,25,11,8,10]))),Ar=v.lazy(()=>V(()=>import("./StoreHome-Bib7SISz.js"),__vite__mapDeps([68,1,31,25,66,33,29,45,46,4,6,43,5,7,20,24,21,10]))),Pr=v.lazy(()=>V(()=>import("./StoreSetting-C79kxAm5.js"),__vite__mapDeps([69,1,31,20,66,33,29,5,6,21,67,25,10]))),Ir=v.lazy(()=>V(()=>import("./Notifications-DD3SXWP6.js"),__vite__mapDeps([70,1,20,6,10]))),Br=[{path:"/dashboard",component:wr},{path:"/products",component:Lr},{path:"/attributes",component:Sr},{path:"/attributes/:id",component:Hr},{path:"/product/:id",component:Tr},{path:"/categories",component:_r},{path:"/languages",component:Wr},{path:"/currencies",component:Rr},{path:"/categories/:id",component:Cr},{path:"/customers",component:jr},{path:"/customer-order/:id",component:Dr},{path:"/our-staff",component:Mr},{path:"/orders",component:Vr},{path:"/order/:id",component:Nr},{path:"/coupons",component:zr},{path:"/settings",component:Fr},{path:"/store/customization",component:Ar},{path:"/store/store-settings",component:Pr},{path:"/404",component:Or},{path:"/coming-soon",component:Er},{path:"/edit-profile",component:$r},{path:"/notifications",component:Ir}],gn=[{label:"Dashboard",value:"dashboard"},{label:"Products",value:"products"},{label:"Categories",value:"categories"},{label:"Attributes",value:"attributes"},{label:"Coupons",value:"coupons"},{label:"Customers",value:"customers"},{label:"Orders",value:"orders"},{label:"Staff",value:"our-staff"},{label:"Settings",value:"settings"},{label:"Languages",value:"languages"},{label:"Currencies",value:"currencies"},{label:"ViewStore",value:"store"},{label:"StoreCustomization",value:"customization"},{label:"StoreSettings",value:"store-settings"},{label:"Product Details",value:"product"},{label:"Order Invoice",value:"order"},{label:"Edit Profile",value:"edit-profile"},{label:"Customer Order",value:"customer-order"},{label:"Notification",value:"notifications"},{label:"Coming Soon",value:"coming-soon"}],qr=v.lazy(()=>V(()=>import("./404-CE5DCzqU.js"),__vite__mapDeps([60,1,10]))),Zr=()=>{const{isSidebarOpen:t,closeSidebar:l,navBar:d}=v.useContext(ge);let m=Ve();const c=navigator.onLine;return v.useEffect(()=>{l()},[m]),a.jsxs(a.Fragment,{children:[!c&&a.jsxs("div",{className:"flex justify-center bg-red-600 text-white",children:["You are in offline mode!"," "]}),a.jsxs("div",{className:`flex h-screen bg-gray-50 dark:bg-gray-900 ${t&&"overflow-hidden"}`,children:[d&&a.jsx(br,{}),a.jsxs("div",{className:"flex flex-col flex-1 w-full",children:[a.jsx(dr,{}),a.jsx(Mt,{children:a.jsx(v.Suspense,{fallback:a.jsx(wt,{}),children:a.jsxs(kt,{children:[Br.map((b,u)=>b.component?a.jsx(de,{exact:!0,path:`${b.path}`,render:x=>a.jsx(b.component,{...x})},u):null),a.jsx(bt,{exact:!0,from:"/",to:"/dashboard"}),a.jsx(de,{component:qr})]})})})]})]})]})},mn=Object.freeze(Object.defineProperty({__proto__:null,default:Zr},Symbol.toStringTag,{value:"Module"}));export{ar as A,an as F,tt as I,mn as L,ce as N,Gr as S,nn as a,on as b,Xr as c,Me as d,Qr as e,tr as f,tn as g,Kr as h,dn as i,un as j,st as k,ct as l,hn as m,_t as n,fn as o,rn as p,vr as q,gn as r,pr as s,en as t,ht as u,ln as v,Jr as w,sn as x,cn as y,dt as z};
