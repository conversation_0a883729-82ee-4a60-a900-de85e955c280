import{f as a}from"./index-BnbL29JP.js";const e={registerAdmin:async t=>a.post("/admin/register",t),loginAdmin:async t=>a.post("/admin/login",t),forgetPassword:async t=>a.put("/admin/forget-password",t),resetPassword:async t=>a.put("/admin/reset-password",t),signUpWithProvider:async t=>a.post("/admin/signup",t),addStaff:async t=>a.post("/admin/add",t),getAllStaff:async t=>a.get("/admin",t),getStaffById:async(t,n)=>a.post(`/admin/${t}`,n),updateStaff:async(t,n)=>a.put(`/admin/${t}`,n),updateStaffStatus:async(t,n)=>a.put(`/admin/update-status/${t}`,n),deleteStaff:async t=>a.delete(`/admin/${t}`)};export{e as A};
