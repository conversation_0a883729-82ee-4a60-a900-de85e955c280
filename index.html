<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8" />
  <link rel="icon" href="/favicon.png" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <meta name="theme-color" content="#000000" />

  <link rel="apple-touch-icon" href="/favicon.png" />
  <meta name="description" content="React eCommerce Admin Dashboard" />
  <meta property="og:title" content="Gloopi - React eCommerce Admin Dashboard" />
  <meta property="og:type" content="eCommerce Website" />
  <meta property="og:url" content="https://gloopi-admin.vercel.app" />
  <meta property="og:image"
    content="https://res.cloudinary.com/ahossain/image/upload/v1636572997/gloopi-admin_ijlexj.png" />

  <!-- <link rel="manifest" href="/manifest.json" /> -->

  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet" />
  <title>Gloopi | React eCommerce Admin Dashboard</title>
</head>

<body class="antialiased">
  <noscript>You need to enable JavaScript to run this app.</noscript>
  <div id="root"></div>
  <script type="module" src="./src/main.jsx"></script>
  <script>
    const global = globalThis;
  </script>
</body>

</html>